[{"directory": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/build/Release", "command": "C:\\PROGRA~1\\MICROS~1\\2022\\COMMUN~1\\VC\\Tools\\MSVC\\1444~1.352\\bin\\Hostx64\\x64\\cl.exe  /nologo /TP -DAGRPC_BOOST_ASIO -DARCUS -DBOOST_ALL_NO_LIB -DBOOST_STACKTRACE_USE_NOOP -DBOOST_STACKTRACE_USE_WINDBG -DBOOST_STACKTRACE_USE_WINDBG_CACHED -DCARES_STATICLIB -DCURA_ENGINE_HASH=\\\"unknown\\\" -DCURA_ENGINE_VERSION=\\\"5.11.0-alpha.0\\\" -DENABLE_PLUGINS -DNOMINMAX -DSENTRY_ENVIRONMENT=\\\"development\\\" -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSTB_TEXTEDIT_KEYTYPE=unsigned -IC:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\include -IC:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\build\\Release -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\arcusee7666f716199\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\proto47a676cb9257b\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\zlibcfb9789dc2a53\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\spdlo2d4b540ab0f22\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\fmtdb20aad6e1bd4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\range0301bf3d76d5d\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\clippf8ddcd4a5d961\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbo34ee9aeb574f9\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbofe72fb50cf7c4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbo1b62b48748b82\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\rapidf7a3355ba53c4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\stb6342cecb318f5\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\boosta79d738920bc3\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\scrip4cfd014ad4660\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\nearg388d58da7a54c\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\curaebb245e64b5ac7\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\asio-c31dc2a380bf0\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\grpc8b6c73b4e5756\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\absei6a15444d60d47\\p\\include -external:W0 /DWIN32 /D_WINDOWS /EHsc /O2 /Ob2 /DNDEBUG -std:c++20 -MD /utf-8 /permissive- /FoCMakeFiles\\_CuraEngine.dir\\src\\Application.cpp.obj /FdCMakeFiles\\_CuraEngine.dir\\_CuraEngine.pdb /FS -c C:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\src\\Application.cpp", "file": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/src/Application.cpp", "output": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/build/Release/CMakeFiles/_CuraEngine.dir/src/Application.cpp.obj"}, {"directory": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/build/Release", "command": "C:\\PROGRA~1\\MICROS~1\\2022\\COMMUN~1\\VC\\Tools\\MSVC\\1444~1.352\\bin\\Hostx64\\x64\\cl.exe  /nologo /TP -DAGRPC_BOOST_ASIO -DARCUS -DBOOST_ALL_NO_LIB -DBOOST_STACKTRACE_USE_NOOP -DBOOST_STACKTRACE_USE_WINDBG -DBOOST_STACKTRACE_USE_WINDBG_CACHED -DCARES_STATICLIB -DCURA_ENGINE_HASH=\\\"unknown\\\" -DCURA_ENGINE_VERSION=\\\"5.11.0-alpha.0\\\" -DENABLE_PLUGINS -DNOMINMAX -DSENTRY_ENVIRONMENT=\\\"development\\\" -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSTB_TEXTEDIT_KEYTYPE=unsigned -IC:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\include -IC:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\build\\Release -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\arcusee7666f716199\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\proto47a676cb9257b\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\zlibcfb9789dc2a53\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\spdlo2d4b540ab0f22\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\fmtdb20aad6e1bd4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\range0301bf3d76d5d\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\clippf8ddcd4a5d961\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbo34ee9aeb574f9\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbofe72fb50cf7c4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbo1b62b48748b82\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\rapidf7a3355ba53c4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\stb6342cecb318f5\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\boosta79d738920bc3\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\scrip4cfd014ad4660\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\nearg388d58da7a54c\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\curaebb245e64b5ac7\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\asio-c31dc2a380bf0\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\grpc8b6c73b4e5756\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\absei6a15444d60d47\\p\\include -external:W0 /DWIN32 /D_WINDOWS /EHsc /O2 /Ob2 /DNDEBUG -std:c++20 -MD /utf-8 /permissive- /FoCMakeFiles\\_CuraEngine.dir\\src\\bridge.cpp.obj /FdCMakeFiles\\_CuraEngine.dir\\_CuraEngine.pdb /FS -c C:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\src\\bridge.cpp", "file": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/src/bridge.cpp", "output": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/build/Release/CMakeFiles/_CuraEngine.dir/src/bridge.cpp.obj"}, {"directory": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/build/Release", "command": "C:\\PROGRA~1\\MICROS~1\\2022\\COMMUN~1\\VC\\Tools\\MSVC\\1444~1.352\\bin\\Hostx64\\x64\\cl.exe  /nologo /TP -DAGRPC_BOOST_ASIO -DARCUS -DBOOST_ALL_NO_LIB -DBOOST_STACKTRACE_USE_NOOP -DBOOST_STACKTRACE_USE_WINDBG -DBOOST_STACKTRACE_USE_WINDBG_CACHED -DCARES_STATICLIB -DCURA_ENGINE_HASH=\\\"unknown\\\" -DCURA_ENGINE_VERSION=\\\"5.11.0-alpha.0\\\" -DENABLE_PLUGINS -DNOMINMAX -DSENTRY_ENVIRONMENT=\\\"development\\\" -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSTB_TEXTEDIT_KEYTYPE=unsigned -IC:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\include -IC:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\build\\Release -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\arcusee7666f716199\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\proto47a676cb9257b\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\zlibcfb9789dc2a53\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\spdlo2d4b540ab0f22\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\fmtdb20aad6e1bd4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\range0301bf3d76d5d\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\clippf8ddcd4a5d961\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbo34ee9aeb574f9\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbofe72fb50cf7c4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbo1b62b48748b82\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\rapidf7a3355ba53c4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\stb6342cecb318f5\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\boosta79d738920bc3\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\scrip4cfd014ad4660\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\nearg388d58da7a54c\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\curaebb245e64b5ac7\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\asio-c31dc2a380bf0\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\grpc8b6c73b4e5756\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\absei6a15444d60d47\\p\\include -external:W0 /DWIN32 /D_WINDOWS /EHsc /O2 /Ob2 /DNDEBUG -std:c++20 -MD /utf-8 /permissive- /FoCMakeFiles\\_CuraEngine.dir\\src\\ConicalOverhang.cpp.obj /FdCMakeFiles\\_CuraEngine.dir\\_CuraEngine.pdb /FS -c C:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\src\\ConicalOverhang.cpp", "file": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/src/ConicalOverhang.cpp", "output": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/build/Release/CMakeFiles/_CuraEngine.dir/src/ConicalOverhang.cpp.obj"}, {"directory": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/build/Release", "command": "C:\\PROGRA~1\\MICROS~1\\2022\\COMMUN~1\\VC\\Tools\\MSVC\\1444~1.352\\bin\\Hostx64\\x64\\cl.exe  /nologo /TP -DAGRPC_BOOST_ASIO -DARCUS -DBOOST_ALL_NO_LIB -DBOOST_STACKTRACE_USE_NOOP -DBOOST_STACKTRACE_USE_WINDBG -DBOOST_STACKTRACE_USE_WINDBG_CACHED -DCARES_STATICLIB -DCURA_ENGINE_HASH=\\\"unknown\\\" -DCURA_ENGINE_VERSION=\\\"5.11.0-alpha.0\\\" -DENABLE_PLUGINS -DNOMINMAX -DSENTRY_ENVIRONMENT=\\\"development\\\" -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSTB_TEXTEDIT_KEYTYPE=unsigned -IC:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\include -IC:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\build\\Release -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\arcusee7666f716199\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\proto47a676cb9257b\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\zlibcfb9789dc2a53\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\spdlo2d4b540ab0f22\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\fmtdb20aad6e1bd4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\range0301bf3d76d5d\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\clippf8ddcd4a5d961\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbo34ee9aeb574f9\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbofe72fb50cf7c4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbo1b62b48748b82\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\rapidf7a3355ba53c4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\stb6342cecb318f5\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\boosta79d738920bc3\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\scrip4cfd014ad4660\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\nearg388d58da7a54c\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\curaebb245e64b5ac7\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\asio-c31dc2a380bf0\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\grpc8b6c73b4e5756\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\absei6a15444d60d47\\p\\include -external:W0 /DWIN32 /D_WINDOWS /EHsc /O2 /Ob2 /DNDEBUG -std:c++20 -MD /utf-8 /permissive- /FoCMakeFiles\\_CuraEngine.dir\\src\\ExtruderPlan.cpp.obj /FdCMakeFiles\\_CuraEngine.dir\\_CuraEngine.pdb /FS -c C:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\src\\ExtruderPlan.cpp", "file": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/src/ExtruderPlan.cpp", "output": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/build/Release/CMakeFiles/_CuraEngine.dir/src/ExtruderPlan.cpp.obj"}, {"directory": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/build/Release", "command": "C:\\PROGRA~1\\MICROS~1\\2022\\COMMUN~1\\VC\\Tools\\MSVC\\1444~1.352\\bin\\Hostx64\\x64\\cl.exe  /nologo /TP -DAGRPC_BOOST_ASIO -DARCUS -DBOOST_ALL_NO_LIB -DBOOST_STACKTRACE_USE_NOOP -DBOOST_STACKTRACE_USE_WINDBG -DBOOST_STACKTRACE_USE_WINDBG_CACHED -DCARES_STATICLIB -DCURA_ENGINE_HASH=\\\"unknown\\\" -DCURA_ENGINE_VERSION=\\\"5.11.0-alpha.0\\\" -DENABLE_PLUGINS -DNOMINMAX -DSENTRY_ENVIRONMENT=\\\"development\\\" -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSTB_TEXTEDIT_KEYTYPE=unsigned -IC:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\include -IC:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\build\\Release -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\arcusee7666f716199\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\proto47a676cb9257b\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\zlibcfb9789dc2a53\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\spdlo2d4b540ab0f22\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\fmtdb20aad6e1bd4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\range0301bf3d76d5d\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\clippf8ddcd4a5d961\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbo34ee9aeb574f9\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbofe72fb50cf7c4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbo1b62b48748b82\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\rapidf7a3355ba53c4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\stb6342cecb318f5\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\boosta79d738920bc3\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\scrip4cfd014ad4660\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\nearg388d58da7a54c\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\curaebb245e64b5ac7\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\asio-c31dc2a380bf0\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\grpc8b6c73b4e5756\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\absei6a15444d60d47\\p\\include -external:W0 /DWIN32 /D_WINDOWS /EHsc /O2 /Ob2 /DNDEBUG -std:c++20 -MD /utf-8 /permissive- /FoCMakeFiles\\_CuraEngine.dir\\src\\ExtruderTrain.cpp.obj /FdCMakeFiles\\_CuraEngine.dir\\_CuraEngine.pdb /FS -c C:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\src\\ExtruderTrain.cpp", "file": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/src/ExtruderTrain.cpp", "output": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/build/Release/CMakeFiles/_CuraEngine.dir/src/ExtruderTrain.cpp.obj"}, {"directory": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/build/Release", "command": "C:\\PROGRA~1\\MICROS~1\\2022\\COMMUN~1\\VC\\Tools\\MSVC\\1444~1.352\\bin\\Hostx64\\x64\\cl.exe  /nologo /TP -DAGRPC_BOOST_ASIO -DARCUS -DBOOST_ALL_NO_LIB -DBOOST_STACKTRACE_USE_NOOP -DBOOST_STACKTRACE_USE_WINDBG -DBOOST_STACKTRACE_USE_WINDBG_CACHED -DCARES_STATICLIB -DCURA_ENGINE_HASH=\\\"unknown\\\" -DCURA_ENGINE_VERSION=\\\"5.11.0-alpha.0\\\" -DENABLE_PLUGINS -DNOMINMAX -DSENTRY_ENVIRONMENT=\\\"development\\\" -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSTB_TEXTEDIT_KEYTYPE=unsigned -IC:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\include -IC:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\build\\Release -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\arcusee7666f716199\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\proto47a676cb9257b\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\zlibcfb9789dc2a53\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\spdlo2d4b540ab0f22\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\fmtdb20aad6e1bd4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\range0301bf3d76d5d\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\clippf8ddcd4a5d961\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbo34ee9aeb574f9\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbofe72fb50cf7c4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbo1b62b48748b82\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\rapidf7a3355ba53c4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\stb6342cecb318f5\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\boosta79d738920bc3\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\scrip4cfd014ad4660\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\nearg388d58da7a54c\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\curaebb245e64b5ac7\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\asio-c31dc2a380bf0\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\grpc8b6c73b4e5756\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\absei6a15444d60d47\\p\\include -external:W0 /DWIN32 /D_WINDOWS /EHsc /O2 /Ob2 /DNDEBUG -std:c++20 -MD /utf-8 /permissive- /FoCMakeFiles\\_CuraEngine.dir\\src\\FffGcodeWriter.cpp.obj /FdCMakeFiles\\_CuraEngine.dir\\_CuraEngine.pdb /FS -c C:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\src\\FffGcodeWriter.cpp", "file": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/src/FffGcodeWriter.cpp", "output": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/build/Release/CMakeFiles/_CuraEngine.dir/src/FffGcodeWriter.cpp.obj"}, {"directory": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/build/Release", "command": "C:\\PROGRA~1\\MICROS~1\\2022\\COMMUN~1\\VC\\Tools\\MSVC\\1444~1.352\\bin\\Hostx64\\x64\\cl.exe  /nologo /TP -DAGRPC_BOOST_ASIO -DARCUS -DBOOST_ALL_NO_LIB -DBOOST_STACKTRACE_USE_NOOP -DBOOST_STACKTRACE_USE_WINDBG -DBOOST_STACKTRACE_USE_WINDBG_CACHED -DCARES_STATICLIB -DCURA_ENGINE_HASH=\\\"unknown\\\" -DCURA_ENGINE_VERSION=\\\"5.11.0-alpha.0\\\" -DENABLE_PLUGINS -DNOMINMAX -DSENTRY_ENVIRONMENT=\\\"development\\\" -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSTB_TEXTEDIT_KEYTYPE=unsigned -IC:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\include -IC:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\build\\Release -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\arcusee7666f716199\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\proto47a676cb9257b\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\zlibcfb9789dc2a53\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\spdlo2d4b540ab0f22\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\fmtdb20aad6e1bd4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\range0301bf3d76d5d\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\clippf8ddcd4a5d961\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbo34ee9aeb574f9\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbofe72fb50cf7c4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbo1b62b48748b82\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\rapidf7a3355ba53c4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\stb6342cecb318f5\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\boosta79d738920bc3\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\scrip4cfd014ad4660\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\nearg388d58da7a54c\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\curaebb245e64b5ac7\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\asio-c31dc2a380bf0\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\grpc8b6c73b4e5756\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\absei6a15444d60d47\\p\\include -external:W0 /DWIN32 /D_WINDOWS /EHsc /O2 /Ob2 /DNDEBUG -std:c++20 -MD /utf-8 /permissive- /FoCMakeFiles\\_CuraEngine.dir\\src\\FffPolygonGenerator.cpp.obj /FdCMakeFiles\\_CuraEngine.dir\\_CuraEngine.pdb /FS -c C:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\src\\FffPolygonGenerator.cpp", "file": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/src/FffPolygonGenerator.cpp", "output": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/build/Release/CMakeFiles/_CuraEngine.dir/src/FffPolygonGenerator.cpp.obj"}, {"directory": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/build/Release", "command": "C:\\PROGRA~1\\MICROS~1\\2022\\COMMUN~1\\VC\\Tools\\MSVC\\1444~1.352\\bin\\Hostx64\\x64\\cl.exe  /nologo /TP -DAGRPC_BOOST_ASIO -DARCUS -DBOOST_ALL_NO_LIB -DBOOST_STACKTRACE_USE_NOOP -DBOOST_STACKTRACE_USE_WINDBG -DBOOST_STACKTRACE_USE_WINDBG_CACHED -DCARES_STATICLIB -DCURA_ENGINE_HASH=\\\"unknown\\\" -DCURA_ENGINE_VERSION=\\\"5.11.0-alpha.0\\\" -DENABLE_PLUGINS -DNOMINMAX -DSENTRY_ENVIRONMENT=\\\"development\\\" -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSTB_TEXTEDIT_KEYTYPE=unsigned -IC:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\include -IC:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\build\\Release -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\arcusee7666f716199\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\proto47a676cb9257b\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\zlibcfb9789dc2a53\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\spdlo2d4b540ab0f22\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\fmtdb20aad6e1bd4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\range0301bf3d76d5d\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\clippf8ddcd4a5d961\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbo34ee9aeb574f9\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbofe72fb50cf7c4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbo1b62b48748b82\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\rapidf7a3355ba53c4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\stb6342cecb318f5\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\boosta79d738920bc3\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\scrip4cfd014ad4660\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\nearg388d58da7a54c\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\curaebb245e64b5ac7\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\asio-c31dc2a380bf0\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\grpc8b6c73b4e5756\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\absei6a15444d60d47\\p\\include -external:W0 /DWIN32 /D_WINDOWS /EHsc /O2 /Ob2 /DNDEBUG -std:c++20 -MD /utf-8 /permissive- /FoCMakeFiles\\_CuraEngine.dir\\src\\FffProcessor.cpp.obj /FdCMakeFiles\\_CuraEngine.dir\\_CuraEngine.pdb /FS -c C:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\src\\FffProcessor.cpp", "file": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/src/FffProcessor.cpp", "output": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/build/Release/CMakeFiles/_CuraEngine.dir/src/FffProcessor.cpp.obj"}, {"directory": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/build/Release", "command": "C:\\PROGRA~1\\MICROS~1\\2022\\COMMUN~1\\VC\\Tools\\MSVC\\1444~1.352\\bin\\Hostx64\\x64\\cl.exe  /nologo /TP -DAGRPC_BOOST_ASIO -DARCUS -DBOOST_ALL_NO_LIB -DBOOST_STACKTRACE_USE_NOOP -DBOOST_STACKTRACE_USE_WINDBG -DBOOST_STACKTRACE_USE_WINDBG_CACHED -DCARES_STATICLIB -DCURA_ENGINE_HASH=\\\"unknown\\\" -DCURA_ENGINE_VERSION=\\\"5.11.0-alpha.0\\\" -DENABLE_PLUGINS -DNOMINMAX -DSENTRY_ENVIRONMENT=\\\"development\\\" -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSTB_TEXTEDIT_KEYTYPE=unsigned -IC:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\include -IC:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\build\\Release -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\arcusee7666f716199\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\proto47a676cb9257b\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\zlibcfb9789dc2a53\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\spdlo2d4b540ab0f22\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\fmtdb20aad6e1bd4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\range0301bf3d76d5d\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\clippf8ddcd4a5d961\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbo34ee9aeb574f9\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbofe72fb50cf7c4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbo1b62b48748b82\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\rapidf7a3355ba53c4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\stb6342cecb318f5\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\boosta79d738920bc3\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\scrip4cfd014ad4660\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\nearg388d58da7a54c\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\curaebb245e64b5ac7\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\asio-c31dc2a380bf0\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\grpc8b6c73b4e5756\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\absei6a15444d60d47\\p\\include -external:W0 /DWIN32 /D_WINDOWS /EHsc /O2 /Ob2 /DNDEBUG -std:c++20 -MD /utf-8 /permissive- /FoCMakeFiles\\_CuraEngine.dir\\src\\gcodeExport.cpp.obj /FdCMakeFiles\\_CuraEngine.dir\\_CuraEngine.pdb /FS -c C:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\src\\gcodeExport.cpp", "file": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/src/gcodeExport.cpp", "output": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/build/Release/CMakeFiles/_CuraEngine.dir/src/gcodeExport.cpp.obj"}, {"directory": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/build/Release", "command": "C:\\PROGRA~1\\MICROS~1\\2022\\COMMUN~1\\VC\\Tools\\MSVC\\1444~1.352\\bin\\Hostx64\\x64\\cl.exe  /nologo /TP -DAGRPC_BOOST_ASIO -DARCUS -DBOOST_ALL_NO_LIB -DBOOST_STACKTRACE_USE_NOOP -DBOOST_STACKTRACE_USE_WINDBG -DBOOST_STACKTRACE_USE_WINDBG_CACHED -DCARES_STATICLIB -DCURA_ENGINE_HASH=\\\"unknown\\\" -DCURA_ENGINE_VERSION=\\\"5.11.0-alpha.0\\\" -DENABLE_PLUGINS -DNOMINMAX -DSENTRY_ENVIRONMENT=\\\"development\\\" -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSTB_TEXTEDIT_KEYTYPE=unsigned -IC:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\include -IC:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\build\\Release -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\arcusee7666f716199\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\proto47a676cb9257b\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\zlibcfb9789dc2a53\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\spdlo2d4b540ab0f22\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\fmtdb20aad6e1bd4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\range0301bf3d76d5d\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\clippf8ddcd4a5d961\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbo34ee9aeb574f9\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbofe72fb50cf7c4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbo1b62b48748b82\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\rapidf7a3355ba53c4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\stb6342cecb318f5\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\boosta79d738920bc3\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\scrip4cfd014ad4660\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\nearg388d58da7a54c\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\curaebb245e64b5ac7\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\asio-c31dc2a380bf0\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\grpc8b6c73b4e5756\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\absei6a15444d60d47\\p\\include -external:W0 /DWIN32 /D_WINDOWS /EHsc /O2 /Ob2 /DNDEBUG -std:c++20 -MD /utf-8 /permissive- /FoCMakeFiles\\_CuraEngine.dir\\src\\GCodePathConfig.cpp.obj /FdCMakeFiles\\_CuraEngine.dir\\_CuraEngine.pdb /FS -c C:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\src\\GCodePathConfig.cpp", "file": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/src/GCodePathConfig.cpp", "output": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/build/Release/CMakeFiles/_CuraEngine.dir/src/GCodePathConfig.cpp.obj"}, {"directory": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/build/Release", "command": "C:\\PROGRA~1\\MICROS~1\\2022\\COMMUN~1\\VC\\Tools\\MSVC\\1444~1.352\\bin\\Hostx64\\x64\\cl.exe  /nologo /TP -DAGRPC_BOOST_ASIO -DARCUS -DBOOST_ALL_NO_LIB -DBOOST_STACKTRACE_USE_NOOP -DBOOST_STACKTRACE_USE_WINDBG -DBOOST_STACKTRACE_USE_WINDBG_CACHED -DCARES_STATICLIB -DCURA_ENGINE_HASH=\\\"unknown\\\" -DCURA_ENGINE_VERSION=\\\"5.11.0-alpha.0\\\" -DENABLE_PLUGINS -DNOMINMAX -DSENTRY_ENVIRONMENT=\\\"development\\\" -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSTB_TEXTEDIT_KEYTYPE=unsigned -IC:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\include -IC:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\build\\Release -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\arcusee7666f716199\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\proto47a676cb9257b\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\zlibcfb9789dc2a53\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\spdlo2d4b540ab0f22\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\fmtdb20aad6e1bd4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\range0301bf3d76d5d\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\clippf8ddcd4a5d961\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbo34ee9aeb574f9\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbofe72fb50cf7c4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbo1b62b48748b82\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\rapidf7a3355ba53c4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\stb6342cecb318f5\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\boosta79d738920bc3\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\scrip4cfd014ad4660\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\nearg388d58da7a54c\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\curaebb245e64b5ac7\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\asio-c31dc2a380bf0\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\grpc8b6c73b4e5756\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\absei6a15444d60d47\\p\\include -external:W0 /DWIN32 /D_WINDOWS /EHsc /O2 /Ob2 /DNDEBUG -std:c++20 -MD /utf-8 /permissive- /FoCMakeFiles\\_CuraEngine.dir\\src\\infill.cpp.obj /FdCMakeFiles\\_CuraEngine.dir\\_CuraEngine.pdb /FS -c C:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\src\\infill.cpp", "file": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/src/infill.cpp", "output": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/build/Release/CMakeFiles/_CuraEngine.dir/src/infill.cpp.obj"}, {"directory": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/build/Release", "command": "C:\\PROGRA~1\\MICROS~1\\2022\\COMMUN~1\\VC\\Tools\\MSVC\\1444~1.352\\bin\\Hostx64\\x64\\cl.exe  /nologo /TP -DAGRPC_BOOST_ASIO -DARCUS -DBOOST_ALL_NO_LIB -DBOOST_STACKTRACE_USE_NOOP -DBOOST_STACKTRACE_USE_WINDBG -DBOOST_STACKTRACE_USE_WINDBG_CACHED -DCARES_STATICLIB -DCURA_ENGINE_HASH=\\\"unknown\\\" -DCURA_ENGINE_VERSION=\\\"5.11.0-alpha.0\\\" -DENABLE_PLUGINS -DNOMINMAX -DSENTRY_ENVIRONMENT=\\\"development\\\" -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSTB_TEXTEDIT_KEYTYPE=unsigned -IC:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\include -IC:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\build\\Release -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\arcusee7666f716199\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\proto47a676cb9257b\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\zlibcfb9789dc2a53\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\spdlo2d4b540ab0f22\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\fmtdb20aad6e1bd4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\range0301bf3d76d5d\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\clippf8ddcd4a5d961\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbo34ee9aeb574f9\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbofe72fb50cf7c4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbo1b62b48748b82\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\rapidf7a3355ba53c4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\stb6342cecb318f5\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\boosta79d738920bc3\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\scrip4cfd014ad4660\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\nearg388d58da7a54c\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\curaebb245e64b5ac7\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\asio-c31dc2a380bf0\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\grpc8b6c73b4e5756\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\absei6a15444d60d47\\p\\include -external:W0 /DWIN32 /D_WINDOWS /EHsc /O2 /Ob2 /DNDEBUG -std:c++20 -MD /utf-8 /permissive- /FoCMakeFiles\\_CuraEngine.dir\\src\\InterlockingGenerator.cpp.obj /FdCMakeFiles\\_CuraEngine.dir\\_CuraEngine.pdb /FS -c C:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\src\\InterlockingGenerator.cpp", "file": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/src/InterlockingGenerator.cpp", "output": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/build/Release/CMakeFiles/_CuraEngine.dir/src/InterlockingGenerator.cpp.obj"}, {"directory": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/build/Release", "command": "C:\\PROGRA~1\\MICROS~1\\2022\\COMMUN~1\\VC\\Tools\\MSVC\\1444~1.352\\bin\\Hostx64\\x64\\cl.exe  /nologo /TP -DAGRPC_BOOST_ASIO -DARCUS -DBOOST_ALL_NO_LIB -DBOOST_STACKTRACE_USE_NOOP -DBOOST_STACKTRACE_USE_WINDBG -DBOOST_STACKTRACE_USE_WINDBG_CACHED -DCARES_STATICLIB -DCURA_ENGINE_HASH=\\\"unknown\\\" -DCURA_ENGINE_VERSION=\\\"5.11.0-alpha.0\\\" -DENABLE_PLUGINS -DNOMINMAX -DSENTRY_ENVIRONMENT=\\\"development\\\" -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSTB_TEXTEDIT_KEYTYPE=unsigned -IC:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\include -IC:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\build\\Release -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\arcusee7666f716199\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\proto47a676cb9257b\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\zlibcfb9789dc2a53\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\spdlo2d4b540ab0f22\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\fmtdb20aad6e1bd4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\range0301bf3d76d5d\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\clippf8ddcd4a5d961\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbo34ee9aeb574f9\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbofe72fb50cf7c4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbo1b62b48748b82\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\rapidf7a3355ba53c4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\stb6342cecb318f5\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\boosta79d738920bc3\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\scrip4cfd014ad4660\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\nearg388d58da7a54c\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\curaebb245e64b5ac7\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\asio-c31dc2a380bf0\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\grpc8b6c73b4e5756\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\absei6a15444d60d47\\p\\include -external:W0 /DWIN32 /D_WINDOWS /EHsc /O2 /Ob2 /DNDEBUG -std:c++20 -MD /utf-8 /permissive- /FoCMakeFiles\\_CuraEngine.dir\\src\\InsetOrderOptimizer.cpp.obj /FdCMakeFiles\\_CuraEngine.dir\\_CuraEngine.pdb /FS -c C:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\src\\InsetOrderOptimizer.cpp", "file": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/src/InsetOrderOptimizer.cpp", "output": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/build/Release/CMakeFiles/_CuraEngine.dir/src/InsetOrderOptimizer.cpp.obj"}, {"directory": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/build/Release", "command": "C:\\PROGRA~1\\MICROS~1\\2022\\COMMUN~1\\VC\\Tools\\MSVC\\1444~1.352\\bin\\Hostx64\\x64\\cl.exe  /nologo /TP -DAGRPC_BOOST_ASIO -DARCUS -DBOOST_ALL_NO_LIB -DBOOST_STACKTRACE_USE_NOOP -DBOOST_STACKTRACE_USE_WINDBG -DBOOST_STACKTRACE_USE_WINDBG_CACHED -DCARES_STATICLIB -DCURA_ENGINE_HASH=\\\"unknown\\\" -DCURA_ENGINE_VERSION=\\\"5.11.0-alpha.0\\\" -DENABLE_PLUGINS -DNOMINMAX -DSENTRY_ENVIRONMENT=\\\"development\\\" -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSTB_TEXTEDIT_KEYTYPE=unsigned -IC:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\include -IC:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\build\\Release -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\arcusee7666f716199\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\proto47a676cb9257b\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\zlibcfb9789dc2a53\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\spdlo2d4b540ab0f22\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\fmtdb20aad6e1bd4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\range0301bf3d76d5d\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\clippf8ddcd4a5d961\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbo34ee9aeb574f9\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbofe72fb50cf7c4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbo1b62b48748b82\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\rapidf7a3355ba53c4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\stb6342cecb318f5\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\boosta79d738920bc3\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\scrip4cfd014ad4660\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\nearg388d58da7a54c\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\curaebb245e64b5ac7\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\asio-c31dc2a380bf0\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\grpc8b6c73b4e5756\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\absei6a15444d60d47\\p\\include -external:W0 /DWIN32 /D_WINDOWS /EHsc /O2 /Ob2 /DNDEBUG -std:c++20 -MD /utf-8 /permissive- /FoCMakeFiles\\_CuraEngine.dir\\src\\layerPart.cpp.obj /FdCMakeFiles\\_CuraEngine.dir\\_CuraEngine.pdb /FS -c C:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\src\\layerPart.cpp", "file": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/src/layerPart.cpp", "output": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/build/Release/CMakeFiles/_CuraEngine.dir/src/layerPart.cpp.obj"}, {"directory": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/build/Release", "command": "C:\\PROGRA~1\\MICROS~1\\2022\\COMMUN~1\\VC\\Tools\\MSVC\\1444~1.352\\bin\\Hostx64\\x64\\cl.exe  /nologo /TP -DAGRPC_BOOST_ASIO -DARCUS -DBOOST_ALL_NO_LIB -DBOOST_STACKTRACE_USE_NOOP -DBOOST_STACKTRACE_USE_WINDBG -DBOOST_STACKTRACE_USE_WINDBG_CACHED -DCARES_STATICLIB -DCURA_ENGINE_HASH=\\\"unknown\\\" -DCURA_ENGINE_VERSION=\\\"5.11.0-alpha.0\\\" -DENABLE_PLUGINS -DNOMINMAX -DSENTRY_ENVIRONMENT=\\\"development\\\" -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSTB_TEXTEDIT_KEYTYPE=unsigned -IC:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\include -IC:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\build\\Release -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\arcusee7666f716199\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\proto47a676cb9257b\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\zlibcfb9789dc2a53\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\spdlo2d4b540ab0f22\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\fmtdb20aad6e1bd4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\range0301bf3d76d5d\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\clippf8ddcd4a5d961\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbo34ee9aeb574f9\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbofe72fb50cf7c4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbo1b62b48748b82\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\rapidf7a3355ba53c4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\stb6342cecb318f5\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\boosta79d738920bc3\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\scrip4cfd014ad4660\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\nearg388d58da7a54c\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\curaebb245e64b5ac7\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\asio-c31dc2a380bf0\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\grpc8b6c73b4e5756\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\absei6a15444d60d47\\p\\include -external:W0 /DWIN32 /D_WINDOWS /EHsc /O2 /Ob2 /DNDEBUG -std:c++20 -MD /utf-8 /permissive- /FoCMakeFiles\\_CuraEngine.dir\\src\\LayerPlan.cpp.obj /FdCMakeFiles\\_CuraEngine.dir\\_CuraEngine.pdb /FS -c C:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\src\\LayerPlan.cpp", "file": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/src/LayerPlan.cpp", "output": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/build/Release/CMakeFiles/_CuraEngine.dir/src/LayerPlan.cpp.obj"}, {"directory": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/build/Release", "command": "C:\\PROGRA~1\\MICROS~1\\2022\\COMMUN~1\\VC\\Tools\\MSVC\\1444~1.352\\bin\\Hostx64\\x64\\cl.exe  /nologo /TP -DAGRPC_BOOST_ASIO -DARCUS -DBOOST_ALL_NO_LIB -DBOOST_STACKTRACE_USE_NOOP -DBOOST_STACKTRACE_USE_WINDBG -DBOOST_STACKTRACE_USE_WINDBG_CACHED -DCARES_STATICLIB -DCURA_ENGINE_HASH=\\\"unknown\\\" -DCURA_ENGINE_VERSION=\\\"5.11.0-alpha.0\\\" -DENABLE_PLUGINS -DNOMINMAX -DSENTRY_ENVIRONMENT=\\\"development\\\" -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSTB_TEXTEDIT_KEYTYPE=unsigned -IC:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\include -IC:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\build\\Release -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\arcusee7666f716199\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\proto47a676cb9257b\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\zlibcfb9789dc2a53\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\spdlo2d4b540ab0f22\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\fmtdb20aad6e1bd4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\range0301bf3d76d5d\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\clippf8ddcd4a5d961\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbo34ee9aeb574f9\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbofe72fb50cf7c4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbo1b62b48748b82\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\rapidf7a3355ba53c4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\stb6342cecb318f5\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\boosta79d738920bc3\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\scrip4cfd014ad4660\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\nearg388d58da7a54c\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\curaebb245e64b5ac7\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\asio-c31dc2a380bf0\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\grpc8b6c73b4e5756\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\absei6a15444d60d47\\p\\include -external:W0 /DWIN32 /D_WINDOWS /EHsc /O2 /Ob2 /DNDEBUG -std:c++20 -MD /utf-8 /permissive- /FoCMakeFiles\\_CuraEngine.dir\\src\\LayerPlanBuffer.cpp.obj /FdCMakeFiles\\_CuraEngine.dir\\_CuraEngine.pdb /FS -c C:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\src\\LayerPlanBuffer.cpp", "file": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/src/LayerPlanBuffer.cpp", "output": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/build/Release/CMakeFiles/_CuraEngine.dir/src/LayerPlanBuffer.cpp.obj"}, {"directory": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/build/Release", "command": "C:\\PROGRA~1\\MICROS~1\\2022\\COMMUN~1\\VC\\Tools\\MSVC\\1444~1.352\\bin\\Hostx64\\x64\\cl.exe  /nologo /TP -DAGRPC_BOOST_ASIO -DARCUS -DBOOST_ALL_NO_LIB -DBOOST_STACKTRACE_USE_NOOP -DBOOST_STACKTRACE_USE_WINDBG -DBOOST_STACKTRACE_USE_WINDBG_CACHED -DCARES_STATICLIB -DCURA_ENGINE_HASH=\\\"unknown\\\" -DCURA_ENGINE_VERSION=\\\"5.11.0-alpha.0\\\" -DENABLE_PLUGINS -DNOMINMAX -DSENTRY_ENVIRONMENT=\\\"development\\\" -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSTB_TEXTEDIT_KEYTYPE=unsigned -IC:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\include -IC:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\build\\Release -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\arcusee7666f716199\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\proto47a676cb9257b\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\zlibcfb9789dc2a53\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\spdlo2d4b540ab0f22\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\fmtdb20aad6e1bd4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\range0301bf3d76d5d\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\clippf8ddcd4a5d961\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbo34ee9aeb574f9\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbofe72fb50cf7c4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbo1b62b48748b82\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\rapidf7a3355ba53c4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\stb6342cecb318f5\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\boosta79d738920bc3\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\scrip4cfd014ad4660\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\nearg388d58da7a54c\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\curaebb245e64b5ac7\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\asio-c31dc2a380bf0\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\grpc8b6c73b4e5756\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\absei6a15444d60d47\\p\\include -external:W0 /DWIN32 /D_WINDOWS /EHsc /O2 /Ob2 /DNDEBUG -std:c++20 -MD /utf-8 /permissive- /FoCMakeFiles\\_CuraEngine.dir\\src\\mesh.cpp.obj /FdCMakeFiles\\_CuraEngine.dir\\_CuraEngine.pdb /FS -c C:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\src\\mesh.cpp", "file": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/src/mesh.cpp", "output": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/build/Release/CMakeFiles/_CuraEngine.dir/src/mesh.cpp.obj"}, {"directory": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/build/Release", "command": "C:\\PROGRA~1\\MICROS~1\\2022\\COMMUN~1\\VC\\Tools\\MSVC\\1444~1.352\\bin\\Hostx64\\x64\\cl.exe  /nologo /TP -DAGRPC_BOOST_ASIO -DARCUS -DBOOST_ALL_NO_LIB -DBOOST_STACKTRACE_USE_NOOP -DBOOST_STACKTRACE_USE_WINDBG -DBOOST_STACKTRACE_USE_WINDBG_CACHED -DCARES_STATICLIB -DCURA_ENGINE_HASH=\\\"unknown\\\" -DCURA_ENGINE_VERSION=\\\"5.11.0-alpha.0\\\" -DENABLE_PLUGINS -DNOMINMAX -DSENTRY_ENVIRONMENT=\\\"development\\\" -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSTB_TEXTEDIT_KEYTYPE=unsigned -IC:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\include -IC:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\build\\Release -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\arcusee7666f716199\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\proto47a676cb9257b\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\zlibcfb9789dc2a53\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\spdlo2d4b540ab0f22\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\fmtdb20aad6e1bd4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\range0301bf3d76d5d\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\clippf8ddcd4a5d961\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbo34ee9aeb574f9\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbofe72fb50cf7c4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbo1b62b48748b82\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\rapidf7a3355ba53c4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\stb6342cecb318f5\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\boosta79d738920bc3\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\scrip4cfd014ad4660\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\nearg388d58da7a54c\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\curaebb245e64b5ac7\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\asio-c31dc2a380bf0\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\grpc8b6c73b4e5756\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\absei6a15444d60d47\\p\\include -external:W0 /DWIN32 /D_WINDOWS /EHsc /O2 /Ob2 /DNDEBUG -std:c++20 -MD /utf-8 /permissive- /FoCMakeFiles\\_CuraEngine.dir\\src\\MeshGroup.cpp.obj /FdCMakeFiles\\_CuraEngine.dir\\_CuraEngine.pdb /FS -c C:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\src\\MeshGroup.cpp", "file": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/src/MeshGroup.cpp", "output": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/build/Release/CMakeFiles/_CuraEngine.dir/src/MeshGroup.cpp.obj"}, {"directory": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/build/Release", "command": "C:\\PROGRA~1\\MICROS~1\\2022\\COMMUN~1\\VC\\Tools\\MSVC\\1444~1.352\\bin\\Hostx64\\x64\\cl.exe  /nologo /TP -DAGRPC_BOOST_ASIO -DARCUS -DBOOST_ALL_NO_LIB -DBOOST_STACKTRACE_USE_NOOP -DBOOST_STACKTRACE_USE_WINDBG -DBOOST_STACKTRACE_USE_WINDBG_CACHED -DCARES_STATICLIB -DCURA_ENGINE_HASH=\\\"unknown\\\" -DCURA_ENGINE_VERSION=\\\"5.11.0-alpha.0\\\" -DENABLE_PLUGINS -DNOMINMAX -DSENTRY_ENVIRONMENT=\\\"development\\\" -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSTB_TEXTEDIT_KEYTYPE=unsigned -IC:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\include -IC:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\build\\Release -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\arcusee7666f716199\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\proto47a676cb9257b\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\zlibcfb9789dc2a53\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\spdlo2d4b540ab0f22\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\fmtdb20aad6e1bd4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\range0301bf3d76d5d\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\clippf8ddcd4a5d961\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbo34ee9aeb574f9\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbofe72fb50cf7c4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbo1b62b48748b82\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\rapidf7a3355ba53c4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\stb6342cecb318f5\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\boosta79d738920bc3\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\scrip4cfd014ad4660\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\nearg388d58da7a54c\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\curaebb245e64b5ac7\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\asio-c31dc2a380bf0\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\grpc8b6c73b4e5756\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\absei6a15444d60d47\\p\\include -external:W0 /DWIN32 /D_WINDOWS /EHsc /O2 /Ob2 /DNDEBUG -std:c++20 -MD /utf-8 /permissive- /FoCMakeFiles\\_CuraEngine.dir\\src\\Mold.cpp.obj /FdCMakeFiles\\_CuraEngine.dir\\_CuraEngine.pdb /FS -c C:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\src\\Mold.cpp", "file": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/src/Mold.cpp", "output": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/build/Release/CMakeFiles/_CuraEngine.dir/src/Mold.cpp.obj"}, {"directory": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/build/Release", "command": "C:\\PROGRA~1\\MICROS~1\\2022\\COMMUN~1\\VC\\Tools\\MSVC\\1444~1.352\\bin\\Hostx64\\x64\\cl.exe  /nologo /TP -DAGRPC_BOOST_ASIO -DARCUS -DBOOST_ALL_NO_LIB -DBOOST_STACKTRACE_USE_NOOP -DBOOST_STACKTRACE_USE_WINDBG -DBOOST_STACKTRACE_USE_WINDBG_CACHED -DCARES_STATICLIB -DCURA_ENGINE_HASH=\\\"unknown\\\" -DCURA_ENGINE_VERSION=\\\"5.11.0-alpha.0\\\" -DENABLE_PLUGINS -DNOMINMAX -DSENTRY_ENVIRONMENT=\\\"development\\\" -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSTB_TEXTEDIT_KEYTYPE=unsigned -IC:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\include -IC:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\build\\Release -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\arcusee7666f716199\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\proto47a676cb9257b\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\zlibcfb9789dc2a53\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\spdlo2d4b540ab0f22\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\fmtdb20aad6e1bd4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\range0301bf3d76d5d\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\clippf8ddcd4a5d961\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbo34ee9aeb574f9\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbofe72fb50cf7c4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbo1b62b48748b82\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\rapidf7a3355ba53c4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\stb6342cecb318f5\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\boosta79d738920bc3\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\scrip4cfd014ad4660\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\nearg388d58da7a54c\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\curaebb245e64b5ac7\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\asio-c31dc2a380bf0\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\grpc8b6c73b4e5756\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\absei6a15444d60d47\\p\\include -external:W0 /DWIN32 /D_WINDOWS /EHsc /O2 /Ob2 /DNDEBUG -std:c++20 -MD /utf-8 /permissive- /FoCMakeFiles\\_CuraEngine.dir\\src\\multiVolumes.cpp.obj /FdCMakeFiles\\_CuraEngine.dir\\_CuraEngine.pdb /FS -c C:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\src\\multiVolumes.cpp", "file": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/src/multiVolumes.cpp", "output": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/build/Release/CMakeFiles/_CuraEngine.dir/src/multiVolumes.cpp.obj"}, {"directory": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/build/Release", "command": "C:\\PROGRA~1\\MICROS~1\\2022\\COMMUN~1\\VC\\Tools\\MSVC\\1444~1.352\\bin\\Hostx64\\x64\\cl.exe  /nologo /TP -DAGRPC_BOOST_ASIO -DARCUS -DBOOST_ALL_NO_LIB -DBOOST_STACKTRACE_USE_NOOP -DBOOST_STACKTRACE_USE_WINDBG -DBOOST_STACKTRACE_USE_WINDBG_CACHED -DCARES_STATICLIB -DCURA_ENGINE_HASH=\\\"unknown\\\" -DCURA_ENGINE_VERSION=\\\"5.11.0-alpha.0\\\" -DENABLE_PLUGINS -DNOMINMAX -DSENTRY_ENVIRONMENT=\\\"development\\\" -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSTB_TEXTEDIT_KEYTYPE=unsigned -IC:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\include -IC:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\build\\Release -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\arcusee7666f716199\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\proto47a676cb9257b\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\zlibcfb9789dc2a53\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\spdlo2d4b540ab0f22\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\fmtdb20aad6e1bd4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\range0301bf3d76d5d\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\clippf8ddcd4a5d961\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbo34ee9aeb574f9\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbofe72fb50cf7c4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbo1b62b48748b82\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\rapidf7a3355ba53c4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\stb6342cecb318f5\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\boosta79d738920bc3\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\scrip4cfd014ad4660\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\nearg388d58da7a54c\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\curaebb245e64b5ac7\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\asio-c31dc2a380bf0\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\grpc8b6c73b4e5756\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\absei6a15444d60d47\\p\\include -external:W0 /DWIN32 /D_WINDOWS /EHsc /O2 /Ob2 /DNDEBUG -std:c++20 -MD /utf-8 /permissive- /FoCMakeFiles\\_CuraEngine.dir\\src\\path_ordering.cpp.obj /FdCMakeFiles\\_CuraEngine.dir\\_CuraEngine.pdb /FS -c C:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\src\\path_ordering.cpp", "file": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/src/path_ordering.cpp", "output": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/build/Release/CMakeFiles/_CuraEngine.dir/src/path_ordering.cpp.obj"}, {"directory": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/build/Release", "command": "C:\\PROGRA~1\\MICROS~1\\2022\\COMMUN~1\\VC\\Tools\\MSVC\\1444~1.352\\bin\\Hostx64\\x64\\cl.exe  /nologo /TP -DAGRPC_BOOST_ASIO -DARCUS -DBOOST_ALL_NO_LIB -DBOOST_STACKTRACE_USE_NOOP -DBOOST_STACKTRACE_USE_WINDBG -DBOOST_STACKTRACE_USE_WINDBG_CACHED -DCARES_STATICLIB -DCURA_ENGINE_HASH=\\\"unknown\\\" -DCURA_ENGINE_VERSION=\\\"5.11.0-alpha.0\\\" -DENABLE_PLUGINS -DNOMINMAX -DSENTRY_ENVIRONMENT=\\\"development\\\" -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSTB_TEXTEDIT_KEYTYPE=unsigned -IC:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\include -IC:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\build\\Release -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\arcusee7666f716199\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\proto47a676cb9257b\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\zlibcfb9789dc2a53\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\spdlo2d4b540ab0f22\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\fmtdb20aad6e1bd4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\range0301bf3d76d5d\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\clippf8ddcd4a5d961\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbo34ee9aeb574f9\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbofe72fb50cf7c4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbo1b62b48748b82\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\rapidf7a3355ba53c4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\stb6342cecb318f5\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\boosta79d738920bc3\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\scrip4cfd014ad4660\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\nearg388d58da7a54c\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\curaebb245e64b5ac7\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\asio-c31dc2a380bf0\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\grpc8b6c73b4e5756\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\absei6a15444d60d47\\p\\include -external:W0 /DWIN32 /D_WINDOWS /EHsc /O2 /Ob2 /DNDEBUG -std:c++20 -MD /utf-8 /permissive- /FoCMakeFiles\\_CuraEngine.dir\\src\\PathAdapter.cpp.obj /FdCMakeFiles\\_CuraEngine.dir\\_CuraEngine.pdb /FS -c C:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\src\\PathAdapter.cpp", "file": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/src/PathAdapter.cpp", "output": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/build/Release/CMakeFiles/_CuraEngine.dir/src/PathAdapter.cpp.obj"}, {"directory": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/build/Release", "command": "C:\\PROGRA~1\\MICROS~1\\2022\\COMMUN~1\\VC\\Tools\\MSVC\\1444~1.352\\bin\\Hostx64\\x64\\cl.exe  /nologo /TP -DAGRPC_BOOST_ASIO -DARCUS -DBOOST_ALL_NO_LIB -DBOOST_STACKTRACE_USE_NOOP -DBOOST_STACKTRACE_USE_WINDBG -DBOOST_STACKTRACE_USE_WINDBG_CACHED -DCARES_STATICLIB -DCURA_ENGINE_HASH=\\\"unknown\\\" -DCURA_ENGINE_VERSION=\\\"5.11.0-alpha.0\\\" -DENABLE_PLUGINS -DNOMINMAX -DSENTRY_ENVIRONMENT=\\\"development\\\" -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSTB_TEXTEDIT_KEYTYPE=unsigned -IC:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\include -IC:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\build\\Release -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\arcusee7666f716199\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\proto47a676cb9257b\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\zlibcfb9789dc2a53\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\spdlo2d4b540ab0f22\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\fmtdb20aad6e1bd4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\range0301bf3d76d5d\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\clippf8ddcd4a5d961\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbo34ee9aeb574f9\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbofe72fb50cf7c4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbo1b62b48748b82\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\rapidf7a3355ba53c4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\stb6342cecb318f5\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\boosta79d738920bc3\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\scrip4cfd014ad4660\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\nearg388d58da7a54c\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\curaebb245e64b5ac7\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\asio-c31dc2a380bf0\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\grpc8b6c73b4e5756\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\absei6a15444d60d47\\p\\include -external:W0 /DWIN32 /D_WINDOWS /EHsc /O2 /Ob2 /DNDEBUG -std:c++20 -MD /utf-8 /permissive- /FoCMakeFiles\\_CuraEngine.dir\\src\\Preheat.cpp.obj /FdCMakeFiles\\_CuraEngine.dir\\_CuraEngine.pdb /FS -c C:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\src\\Preheat.cpp", "file": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/src/Preheat.cpp", "output": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/build/Release/CMakeFiles/_CuraEngine.dir/src/Preheat.cpp.obj"}, {"directory": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/build/Release", "command": "C:\\PROGRA~1\\MICROS~1\\2022\\COMMUN~1\\VC\\Tools\\MSVC\\1444~1.352\\bin\\Hostx64\\x64\\cl.exe  /nologo /TP -DAGRPC_BOOST_ASIO -DARCUS -DBOOST_ALL_NO_LIB -DBOOST_STACKTRACE_USE_NOOP -DBOOST_STACKTRACE_USE_WINDBG -DBOOST_STACKTRACE_USE_WINDBG_CACHED -DCARES_STATICLIB -DCURA_ENGINE_HASH=\\\"unknown\\\" -DCURA_ENGINE_VERSION=\\\"5.11.0-alpha.0\\\" -DENABLE_PLUGINS -DNOMINMAX -DSENTRY_ENVIRONMENT=\\\"development\\\" -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSTB_TEXTEDIT_KEYTYPE=unsigned -IC:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\include -IC:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\build\\Release -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\arcusee7666f716199\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\proto47a676cb9257b\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\zlibcfb9789dc2a53\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\spdlo2d4b540ab0f22\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\fmtdb20aad6e1bd4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\range0301bf3d76d5d\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\clippf8ddcd4a5d961\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbo34ee9aeb574f9\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbofe72fb50cf7c4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbo1b62b48748b82\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\rapidf7a3355ba53c4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\stb6342cecb318f5\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\boosta79d738920bc3\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\scrip4cfd014ad4660\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\nearg388d58da7a54c\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\curaebb245e64b5ac7\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\asio-c31dc2a380bf0\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\grpc8b6c73b4e5756\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\absei6a15444d60d47\\p\\include -external:W0 /DWIN32 /D_WINDOWS /EHsc /O2 /Ob2 /DNDEBUG -std:c++20 -MD /utf-8 /permissive- /FoCMakeFiles\\_CuraEngine.dir\\src\\PrimeTower\\PrimeTower.cpp.obj /FdCMakeFiles\\_CuraEngine.dir\\_CuraEngine.pdb /FS -c C:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\src\\PrimeTower\\PrimeTower.cpp", "file": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/src/PrimeTower/PrimeTower.cpp", "output": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/build/Release/CMakeFiles/_CuraEngine.dir/src/PrimeTower/PrimeTower.cpp.obj"}, {"directory": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/build/Release", "command": "C:\\PROGRA~1\\MICROS~1\\2022\\COMMUN~1\\VC\\Tools\\MSVC\\1444~1.352\\bin\\Hostx64\\x64\\cl.exe  /nologo /TP -DAGRPC_BOOST_ASIO -DARCUS -DBOOST_ALL_NO_LIB -DBOOST_STACKTRACE_USE_NOOP -DBOOST_STACKTRACE_USE_WINDBG -DBOOST_STACKTRACE_USE_WINDBG_CACHED -DCARES_STATICLIB -DCURA_ENGINE_HASH=\\\"unknown\\\" -DCURA_ENGINE_VERSION=\\\"5.11.0-alpha.0\\\" -DENABLE_PLUGINS -DNOMINMAX -DSENTRY_ENVIRONMENT=\\\"development\\\" -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSTB_TEXTEDIT_KEYTYPE=unsigned -IC:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\include -IC:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\build\\Release -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\arcusee7666f716199\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\proto47a676cb9257b\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\zlibcfb9789dc2a53\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\spdlo2d4b540ab0f22\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\fmtdb20aad6e1bd4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\range0301bf3d76d5d\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\clippf8ddcd4a5d961\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbo34ee9aeb574f9\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbofe72fb50cf7c4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbo1b62b48748b82\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\rapidf7a3355ba53c4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\stb6342cecb318f5\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\boosta79d738920bc3\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\scrip4cfd014ad4660\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\nearg388d58da7a54c\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\curaebb245e64b5ac7\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\asio-c31dc2a380bf0\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\grpc8b6c73b4e5756\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\absei6a15444d60d47\\p\\include -external:W0 /DWIN32 /D_WINDOWS /EHsc /O2 /Ob2 /DNDEBUG -std:c++20 -MD /utf-8 /permissive- /FoCMakeFiles\\_CuraEngine.dir\\src\\PrimeTower\\PrimeTowerNormal.cpp.obj /FdCMakeFiles\\_CuraEngine.dir\\_CuraEngine.pdb /FS -c C:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\src\\PrimeTower\\PrimeTowerNormal.cpp", "file": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/src/PrimeTower/PrimeTowerNormal.cpp", "output": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/build/Release/CMakeFiles/_CuraEngine.dir/src/PrimeTower/PrimeTowerNormal.cpp.obj"}, {"directory": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/build/Release", "command": "C:\\PROGRA~1\\MICROS~1\\2022\\COMMUN~1\\VC\\Tools\\MSVC\\1444~1.352\\bin\\Hostx64\\x64\\cl.exe  /nologo /TP -DAGRPC_BOOST_ASIO -DARCUS -DBOOST_ALL_NO_LIB -DBOOST_STACKTRACE_USE_NOOP -DBOOST_STACKTRACE_USE_WINDBG -DBOOST_STACKTRACE_USE_WINDBG_CACHED -DCARES_STATICLIB -DCURA_ENGINE_HASH=\\\"unknown\\\" -DCURA_ENGINE_VERSION=\\\"5.11.0-alpha.0\\\" -DENABLE_PLUGINS -DNOMINMAX -DSENTRY_ENVIRONMENT=\\\"development\\\" -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSTB_TEXTEDIT_KEYTYPE=unsigned -IC:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\include -IC:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\build\\Release -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\arcusee7666f716199\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\proto47a676cb9257b\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\zlibcfb9789dc2a53\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\spdlo2d4b540ab0f22\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\fmtdb20aad6e1bd4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\range0301bf3d76d5d\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\clippf8ddcd4a5d961\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbo34ee9aeb574f9\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbofe72fb50cf7c4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbo1b62b48748b82\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\rapidf7a3355ba53c4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\stb6342cecb318f5\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\boosta79d738920bc3\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\scrip4cfd014ad4660\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\nearg388d58da7a54c\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\curaebb245e64b5ac7\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\asio-c31dc2a380bf0\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\grpc8b6c73b4e5756\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\absei6a15444d60d47\\p\\include -external:W0 /DWIN32 /D_WINDOWS /EHsc /O2 /Ob2 /DNDEBUG -std:c++20 -MD /utf-8 /permissive- /FoCMakeFiles\\_CuraEngine.dir\\src\\PrimeTower\\PrimeTowerInterleaved.cpp.obj /FdCMakeFiles\\_CuraEngine.dir\\_CuraEngine.pdb /FS -c C:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\src\\PrimeTower\\PrimeTowerInterleaved.cpp", "file": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/src/PrimeTower/PrimeTowerInterleaved.cpp", "output": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/build/Release/CMakeFiles/_CuraEngine.dir/src/PrimeTower/PrimeTowerInterleaved.cpp.obj"}, {"directory": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/build/Release", "command": "C:\\PROGRA~1\\MICROS~1\\2022\\COMMUN~1\\VC\\Tools\\MSVC\\1444~1.352\\bin\\Hostx64\\x64\\cl.exe  /nologo /TP -DAGRPC_BOOST_ASIO -DARCUS -DBOOST_ALL_NO_LIB -DBOOST_STACKTRACE_USE_NOOP -DBOOST_STACKTRACE_USE_WINDBG -DBOOST_STACKTRACE_USE_WINDBG_CACHED -DCARES_STATICLIB -DCURA_ENGINE_HASH=\\\"unknown\\\" -DCURA_ENGINE_VERSION=\\\"5.11.0-alpha.0\\\" -DENABLE_PLUGINS -DNOMINMAX -DSENTRY_ENVIRONMENT=\\\"development\\\" -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSTB_TEXTEDIT_KEYTYPE=unsigned -IC:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\include -IC:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\build\\Release -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\arcusee7666f716199\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\proto47a676cb9257b\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\zlibcfb9789dc2a53\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\spdlo2d4b540ab0f22\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\fmtdb20aad6e1bd4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\range0301bf3d76d5d\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\clippf8ddcd4a5d961\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbo34ee9aeb574f9\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbofe72fb50cf7c4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbo1b62b48748b82\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\rapidf7a3355ba53c4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\stb6342cecb318f5\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\boosta79d738920bc3\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\scrip4cfd014ad4660\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\nearg388d58da7a54c\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\curaebb245e64b5ac7\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\asio-c31dc2a380bf0\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\grpc8b6c73b4e5756\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\absei6a15444d60d47\\p\\include -external:W0 /DWIN32 /D_WINDOWS /EHsc /O2 /Ob2 /DNDEBUG -std:c++20 -MD /utf-8 /permissive- /FoCMakeFiles\\_CuraEngine.dir\\src\\raft.cpp.obj /FdCMakeFiles\\_CuraEngine.dir\\_CuraEngine.pdb /FS -c C:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\src\\raft.cpp", "file": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/src/raft.cpp", "output": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/build/Release/CMakeFiles/_CuraEngine.dir/src/raft.cpp.obj"}, {"directory": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/build/Release", "command": "C:\\PROGRA~1\\MICROS~1\\2022\\COMMUN~1\\VC\\Tools\\MSVC\\1444~1.352\\bin\\Hostx64\\x64\\cl.exe  /nologo /TP -DAGRPC_BOOST_ASIO -DARCUS -DBOOST_ALL_NO_LIB -DBOOST_STACKTRACE_USE_NOOP -DBOOST_STACKTRACE_USE_WINDBG -DBOOST_STACKTRACE_USE_WINDBG_CACHED -DCARES_STATICLIB -DCURA_ENGINE_HASH=\\\"unknown\\\" -DCURA_ENGINE_VERSION=\\\"5.11.0-alpha.0\\\" -DENABLE_PLUGINS -DNOMINMAX -DSENTRY_ENVIRONMENT=\\\"development\\\" -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSTB_TEXTEDIT_KEYTYPE=unsigned -IC:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\include -IC:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\build\\Release -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\arcusee7666f716199\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\proto47a676cb9257b\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\zlibcfb9789dc2a53\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\spdlo2d4b540ab0f22\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\fmtdb20aad6e1bd4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\range0301bf3d76d5d\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\clippf8ddcd4a5d961\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbo34ee9aeb574f9\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbofe72fb50cf7c4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbo1b62b48748b82\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\rapidf7a3355ba53c4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\stb6342cecb318f5\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\boosta79d738920bc3\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\scrip4cfd014ad4660\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\nearg388d58da7a54c\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\curaebb245e64b5ac7\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\asio-c31dc2a380bf0\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\grpc8b6c73b4e5756\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\absei6a15444d60d47\\p\\include -external:W0 /DWIN32 /D_WINDOWS /EHsc /O2 /Ob2 /DNDEBUG -std:c++20 -MD /utf-8 /permissive- /FoCMakeFiles\\_CuraEngine.dir\\src\\Scene.cpp.obj /FdCMakeFiles\\_CuraEngine.dir\\_CuraEngine.pdb /FS -c C:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\src\\Scene.cpp", "file": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/src/Scene.cpp", "output": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/build/Release/CMakeFiles/_CuraEngine.dir/src/Scene.cpp.obj"}, {"directory": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/build/Release", "command": "C:\\PROGRA~1\\MICROS~1\\2022\\COMMUN~1\\VC\\Tools\\MSVC\\1444~1.352\\bin\\Hostx64\\x64\\cl.exe  /nologo /TP -DAGRPC_BOOST_ASIO -DARCUS -DBOOST_ALL_NO_LIB -DBOOST_STACKTRACE_USE_NOOP -DBOOST_STACKTRACE_USE_WINDBG -DBOOST_STACKTRACE_USE_WINDBG_CACHED -DCARES_STATICLIB -DCURA_ENGINE_HASH=\\\"unknown\\\" -DCURA_ENGINE_VERSION=\\\"5.11.0-alpha.0\\\" -DENABLE_PLUGINS -DNOMINMAX -DSENTRY_ENVIRONMENT=\\\"development\\\" -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSTB_TEXTEDIT_KEYTYPE=unsigned -IC:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\include -IC:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\build\\Release -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\arcusee7666f716199\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\proto47a676cb9257b\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\zlibcfb9789dc2a53\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\spdlo2d4b540ab0f22\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\fmtdb20aad6e1bd4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\range0301bf3d76d5d\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\clippf8ddcd4a5d961\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbo34ee9aeb574f9\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbofe72fb50cf7c4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbo1b62b48748b82\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\rapidf7a3355ba53c4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\stb6342cecb318f5\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\boosta79d738920bc3\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\scrip4cfd014ad4660\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\nearg388d58da7a54c\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\curaebb245e64b5ac7\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\asio-c31dc2a380bf0\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\grpc8b6c73b4e5756\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\absei6a15444d60d47\\p\\include -external:W0 /DWIN32 /D_WINDOWS /EHsc /O2 /Ob2 /DNDEBUG -std:c++20 -MD /utf-8 /permissive- /FoCMakeFiles\\_CuraEngine.dir\\src\\SkeletalTrapezoidation.cpp.obj /FdCMakeFiles\\_CuraEngine.dir\\_CuraEngine.pdb /FS -c C:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\src\\SkeletalTrapezoidation.cpp", "file": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/src/SkeletalTrapezoidation.cpp", "output": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/build/Release/CMakeFiles/_CuraEngine.dir/src/SkeletalTrapezoidation.cpp.obj"}, {"directory": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/build/Release", "command": "C:\\PROGRA~1\\MICROS~1\\2022\\COMMUN~1\\VC\\Tools\\MSVC\\1444~1.352\\bin\\Hostx64\\x64\\cl.exe  /nologo /TP -DAGRPC_BOOST_ASIO -DARCUS -DBOOST_ALL_NO_LIB -DBOOST_STACKTRACE_USE_NOOP -DBOOST_STACKTRACE_USE_WINDBG -DBOOST_STACKTRACE_USE_WINDBG_CACHED -DCARES_STATICLIB -DCURA_ENGINE_HASH=\\\"unknown\\\" -DCURA_ENGINE_VERSION=\\\"5.11.0-alpha.0\\\" -DENABLE_PLUGINS -DNOMINMAX -DSENTRY_ENVIRONMENT=\\\"development\\\" -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSTB_TEXTEDIT_KEYTYPE=unsigned -IC:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\include -IC:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\build\\Release -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\arcusee7666f716199\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\proto47a676cb9257b\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\zlibcfb9789dc2a53\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\spdlo2d4b540ab0f22\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\fmtdb20aad6e1bd4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\range0301bf3d76d5d\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\clippf8ddcd4a5d961\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbo34ee9aeb574f9\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbofe72fb50cf7c4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbo1b62b48748b82\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\rapidf7a3355ba53c4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\stb6342cecb318f5\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\boosta79d738920bc3\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\scrip4cfd014ad4660\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\nearg388d58da7a54c\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\curaebb245e64b5ac7\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\asio-c31dc2a380bf0\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\grpc8b6c73b4e5756\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\absei6a15444d60d47\\p\\include -external:W0 /DWIN32 /D_WINDOWS /EHsc /O2 /Ob2 /DNDEBUG -std:c++20 -MD /utf-8 /permissive- /FoCMakeFiles\\_CuraEngine.dir\\src\\SkeletalTrapezoidationGraph.cpp.obj /FdCMakeFiles\\_CuraEngine.dir\\_CuraEngine.pdb /FS -c C:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\src\\SkeletalTrapezoidationGraph.cpp", "file": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/src/SkeletalTrapezoidationGraph.cpp", "output": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/build/Release/CMakeFiles/_CuraEngine.dir/src/SkeletalTrapezoidationGraph.cpp.obj"}, {"directory": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/build/Release", "command": "C:\\PROGRA~1\\MICROS~1\\2022\\COMMUN~1\\VC\\Tools\\MSVC\\1444~1.352\\bin\\Hostx64\\x64\\cl.exe  /nologo /TP -DAGRPC_BOOST_ASIO -DARCUS -DBOOST_ALL_NO_LIB -DBOOST_STACKTRACE_USE_NOOP -DBOOST_STACKTRACE_USE_WINDBG -DBOOST_STACKTRACE_USE_WINDBG_CACHED -DCARES_STATICLIB -DCURA_ENGINE_HASH=\\\"unknown\\\" -DCURA_ENGINE_VERSION=\\\"5.11.0-alpha.0\\\" -DENABLE_PLUGINS -DNOMINMAX -DSENTRY_ENVIRONMENT=\\\"development\\\" -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSTB_TEXTEDIT_KEYTYPE=unsigned -IC:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\include -IC:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\build\\Release -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\arcusee7666f716199\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\proto47a676cb9257b\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\zlibcfb9789dc2a53\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\spdlo2d4b540ab0f22\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\fmtdb20aad6e1bd4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\range0301bf3d76d5d\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\clippf8ddcd4a5d961\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbo34ee9aeb574f9\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbofe72fb50cf7c4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbo1b62b48748b82\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\rapidf7a3355ba53c4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\stb6342cecb318f5\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\boosta79d738920bc3\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\scrip4cfd014ad4660\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\nearg388d58da7a54c\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\curaebb245e64b5ac7\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\asio-c31dc2a380bf0\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\grpc8b6c73b4e5756\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\absei6a15444d60d47\\p\\include -external:W0 /DWIN32 /D_WINDOWS /EHsc /O2 /Ob2 /DNDEBUG -std:c++20 -MD /utf-8 /permissive- /FoCMakeFiles\\_CuraEngine.dir\\src\\skin.cpp.obj /FdCMakeFiles\\_CuraEngine.dir\\_CuraEngine.pdb /FS -c C:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\src\\skin.cpp", "file": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/src/skin.cpp", "output": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/build/Release/CMakeFiles/_CuraEngine.dir/src/skin.cpp.obj"}, {"directory": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/build/Release", "command": "C:\\PROGRA~1\\MICROS~1\\2022\\COMMUN~1\\VC\\Tools\\MSVC\\1444~1.352\\bin\\Hostx64\\x64\\cl.exe  /nologo /TP -DAGRPC_BOOST_ASIO -DARCUS -DBOOST_ALL_NO_LIB -DBOOST_STACKTRACE_USE_NOOP -DBOOST_STACKTRACE_USE_WINDBG -DBOOST_STACKTRACE_USE_WINDBG_CACHED -DCARES_STATICLIB -DCURA_ENGINE_HASH=\\\"unknown\\\" -DCURA_ENGINE_VERSION=\\\"5.11.0-alpha.0\\\" -DENABLE_PLUGINS -DNOMINMAX -DSENTRY_ENVIRONMENT=\\\"development\\\" -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSTB_TEXTEDIT_KEYTYPE=unsigned -IC:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\include -IC:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\build\\Release -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\arcusee7666f716199\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\proto47a676cb9257b\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\zlibcfb9789dc2a53\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\spdlo2d4b540ab0f22\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\fmtdb20aad6e1bd4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\range0301bf3d76d5d\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\clippf8ddcd4a5d961\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbo34ee9aeb574f9\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbofe72fb50cf7c4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbo1b62b48748b82\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\rapidf7a3355ba53c4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\stb6342cecb318f5\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\boosta79d738920bc3\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\scrip4cfd014ad4660\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\nearg388d58da7a54c\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\curaebb245e64b5ac7\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\asio-c31dc2a380bf0\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\grpc8b6c73b4e5756\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\absei6a15444d60d47\\p\\include -external:W0 /DWIN32 /D_WINDOWS /EHsc /O2 /Ob2 /DNDEBUG -std:c++20 -MD /utf-8 /permissive- /FoCMakeFiles\\_CuraEngine.dir\\src\\SkirtBrim.cpp.obj /FdCMakeFiles\\_CuraEngine.dir\\_CuraEngine.pdb /FS -c C:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\src\\SkirtBrim.cpp", "file": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/src/SkirtBrim.cpp", "output": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/build/Release/CMakeFiles/_CuraEngine.dir/src/SkirtBrim.cpp.obj"}, {"directory": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/build/Release", "command": "C:\\PROGRA~1\\MICROS~1\\2022\\COMMUN~1\\VC\\Tools\\MSVC\\1444~1.352\\bin\\Hostx64\\x64\\cl.exe  /nologo /TP -DAGRPC_BOOST_ASIO -DARCUS -DBOOST_ALL_NO_LIB -DBOOST_STACKTRACE_USE_NOOP -DBOOST_STACKTRACE_USE_WINDBG -DBOOST_STACKTRACE_USE_WINDBG_CACHED -DCARES_STATICLIB -DCURA_ENGINE_HASH=\\\"unknown\\\" -DCURA_ENGINE_VERSION=\\\"5.11.0-alpha.0\\\" -DENABLE_PLUGINS -DNOMINMAX -DSENTRY_ENVIRONMENT=\\\"development\\\" -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSTB_TEXTEDIT_KEYTYPE=unsigned -IC:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\include -IC:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\build\\Release -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\arcusee7666f716199\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\proto47a676cb9257b\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\zlibcfb9789dc2a53\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\spdlo2d4b540ab0f22\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\fmtdb20aad6e1bd4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\range0301bf3d76d5d\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\clippf8ddcd4a5d961\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbo34ee9aeb574f9\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbofe72fb50cf7c4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbo1b62b48748b82\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\rapidf7a3355ba53c4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\stb6342cecb318f5\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\boosta79d738920bc3\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\scrip4cfd014ad4660\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\nearg388d58da7a54c\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\curaebb245e64b5ac7\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\asio-c31dc2a380bf0\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\grpc8b6c73b4e5756\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\absei6a15444d60d47\\p\\include -external:W0 /DWIN32 /D_WINDOWS /EHsc /O2 /Ob2 /DNDEBUG -std:c++20 -MD /utf-8 /permissive- /FoCMakeFiles\\_CuraEngine.dir\\src\\SupportInfillPart.cpp.obj /FdCMakeFiles\\_CuraEngine.dir\\_CuraEngine.pdb /FS -c C:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\src\\SupportInfillPart.cpp", "file": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/src/SupportInfillPart.cpp", "output": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/build/Release/CMakeFiles/_CuraEngine.dir/src/SupportInfillPart.cpp.obj"}, {"directory": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/build/Release", "command": "C:\\PROGRA~1\\MICROS~1\\2022\\COMMUN~1\\VC\\Tools\\MSVC\\1444~1.352\\bin\\Hostx64\\x64\\cl.exe  /nologo /TP -DAGRPC_BOOST_ASIO -DARCUS -DBOOST_ALL_NO_LIB -DBOOST_STACKTRACE_USE_NOOP -DBOOST_STACKTRACE_USE_WINDBG -DBOOST_STACKTRACE_USE_WINDBG_CACHED -DCARES_STATICLIB -DCURA_ENGINE_HASH=\\\"unknown\\\" -DCURA_ENGINE_VERSION=\\\"5.11.0-alpha.0\\\" -DENABLE_PLUGINS -DNOMINMAX -DSENTRY_ENVIRONMENT=\\\"development\\\" -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSTB_TEXTEDIT_KEYTYPE=unsigned -IC:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\include -IC:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\build\\Release -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\arcusee7666f716199\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\proto47a676cb9257b\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\zlibcfb9789dc2a53\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\spdlo2d4b540ab0f22\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\fmtdb20aad6e1bd4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\range0301bf3d76d5d\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\clippf8ddcd4a5d961\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbo34ee9aeb574f9\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbofe72fb50cf7c4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbo1b62b48748b82\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\rapidf7a3355ba53c4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\stb6342cecb318f5\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\boosta79d738920bc3\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\scrip4cfd014ad4660\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\nearg388d58da7a54c\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\curaebb245e64b5ac7\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\asio-c31dc2a380bf0\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\grpc8b6c73b4e5756\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\absei6a15444d60d47\\p\\include -external:W0 /DWIN32 /D_WINDOWS /EHsc /O2 /Ob2 /DNDEBUG -std:c++20 -MD /utf-8 /permissive- /FoCMakeFiles\\_CuraEngine.dir\\src\\Slice.cpp.obj /FdCMakeFiles\\_CuraEngine.dir\\_CuraEngine.pdb /FS -c C:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\src\\Slice.cpp", "file": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/src/Slice.cpp", "output": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/build/Release/CMakeFiles/_CuraEngine.dir/src/Slice.cpp.obj"}, {"directory": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/build/Release", "command": "C:\\PROGRA~1\\MICROS~1\\2022\\COMMUN~1\\VC\\Tools\\MSVC\\1444~1.352\\bin\\Hostx64\\x64\\cl.exe  /nologo /TP -DAGRPC_BOOST_ASIO -DARCUS -DBOOST_ALL_NO_LIB -DBOOST_STACKTRACE_USE_NOOP -DBOOST_STACKTRACE_USE_WINDBG -DBOOST_STACKTRACE_USE_WINDBG_CACHED -DCARES_STATICLIB -DCURA_ENGINE_HASH=\\\"unknown\\\" -DCURA_ENGINE_VERSION=\\\"5.11.0-alpha.0\\\" -DENABLE_PLUGINS -DNOMINMAX -DSENTRY_ENVIRONMENT=\\\"development\\\" -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSTB_TEXTEDIT_KEYTYPE=unsigned -IC:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\include -IC:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\build\\Release -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\arcusee7666f716199\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\proto47a676cb9257b\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\zlibcfb9789dc2a53\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\spdlo2d4b540ab0f22\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\fmtdb20aad6e1bd4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\range0301bf3d76d5d\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\clippf8ddcd4a5d961\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbo34ee9aeb574f9\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbofe72fb50cf7c4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbo1b62b48748b82\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\rapidf7a3355ba53c4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\stb6342cecb318f5\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\boosta79d738920bc3\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\scrip4cfd014ad4660\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\nearg388d58da7a54c\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\curaebb245e64b5ac7\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\asio-c31dc2a380bf0\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\grpc8b6c73b4e5756\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\absei6a15444d60d47\\p\\include -external:W0 /DWIN32 /D_WINDOWS /EHsc /O2 /Ob2 /DNDEBUG -std:c++20 -MD /utf-8 /permissive- /FoCMakeFiles\\_CuraEngine.dir\\src\\sliceDataStorage.cpp.obj /FdCMakeFiles\\_CuraEngine.dir\\_CuraEngine.pdb /FS -c C:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\src\\sliceDataStorage.cpp", "file": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/src/sliceDataStorage.cpp", "output": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/build/Release/CMakeFiles/_CuraEngine.dir/src/sliceDataStorage.cpp.obj"}, {"directory": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/build/Release", "command": "C:\\PROGRA~1\\MICROS~1\\2022\\COMMUN~1\\VC\\Tools\\MSVC\\1444~1.352\\bin\\Hostx64\\x64\\cl.exe  /nologo /TP -DAGRPC_BOOST_ASIO -DARCUS -DBOOST_ALL_NO_LIB -DBOOST_STACKTRACE_USE_NOOP -DBOOST_STACKTRACE_USE_WINDBG -DBOOST_STACKTRACE_USE_WINDBG_CACHED -DCARES_STATICLIB -DCURA_ENGINE_HASH=\\\"unknown\\\" -DCURA_ENGINE_VERSION=\\\"5.11.0-alpha.0\\\" -DENABLE_PLUGINS -DNOMINMAX -DSENTRY_ENVIRONMENT=\\\"development\\\" -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSTB_TEXTEDIT_KEYTYPE=unsigned -IC:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\include -IC:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\build\\Release -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\arcusee7666f716199\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\proto47a676cb9257b\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\zlibcfb9789dc2a53\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\spdlo2d4b540ab0f22\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\fmtdb20aad6e1bd4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\range0301bf3d76d5d\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\clippf8ddcd4a5d961\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbo34ee9aeb574f9\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbofe72fb50cf7c4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbo1b62b48748b82\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\rapidf7a3355ba53c4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\stb6342cecb318f5\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\boosta79d738920bc3\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\scrip4cfd014ad4660\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\nearg388d58da7a54c\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\curaebb245e64b5ac7\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\asio-c31dc2a380bf0\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\grpc8b6c73b4e5756\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\absei6a15444d60d47\\p\\include -external:W0 /DWIN32 /D_WINDOWS /EHsc /O2 /Ob2 /DNDEBUG -std:c++20 -MD /utf-8 /permissive- /FoCMakeFiles\\_CuraEngine.dir\\src\\slicer.cpp.obj /FdCMakeFiles\\_CuraEngine.dir\\_CuraEngine.pdb /FS -c C:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\src\\slicer.cpp", "file": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/src/slicer.cpp", "output": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/build/Release/CMakeFiles/_CuraEngine.dir/src/slicer.cpp.obj"}, {"directory": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/build/Release", "command": "C:\\PROGRA~1\\MICROS~1\\2022\\COMMUN~1\\VC\\Tools\\MSVC\\1444~1.352\\bin\\Hostx64\\x64\\cl.exe  /nologo /TP -DAGRPC_BOOST_ASIO -DARCUS -DBOOST_ALL_NO_LIB -DBOOST_STACKTRACE_USE_NOOP -DBOOST_STACKTRACE_USE_WINDBG -DBOOST_STACKTRACE_USE_WINDBG_CACHED -DCARES_STATICLIB -DCURA_ENGINE_HASH=\\\"unknown\\\" -DCURA_ENGINE_VERSION=\\\"5.11.0-alpha.0\\\" -DENABLE_PLUGINS -DNOMINMAX -DSENTRY_ENVIRONMENT=\\\"development\\\" -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSTB_TEXTEDIT_KEYTYPE=unsigned -IC:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\include -IC:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\build\\Release -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\arcusee7666f716199\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\proto47a676cb9257b\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\zlibcfb9789dc2a53\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\spdlo2d4b540ab0f22\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\fmtdb20aad6e1bd4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\range0301bf3d76d5d\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\clippf8ddcd4a5d961\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbo34ee9aeb574f9\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbofe72fb50cf7c4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbo1b62b48748b82\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\rapidf7a3355ba53c4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\stb6342cecb318f5\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\boosta79d738920bc3\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\scrip4cfd014ad4660\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\nearg388d58da7a54c\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\curaebb245e64b5ac7\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\asio-c31dc2a380bf0\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\grpc8b6c73b4e5756\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\absei6a15444d60d47\\p\\include -external:W0 /DWIN32 /D_WINDOWS /EHsc /O2 /Ob2 /DNDEBUG -std:c++20 -MD /utf-8 /permissive- /FoCMakeFiles\\_CuraEngine.dir\\src\\support.cpp.obj /FdCMakeFiles\\_CuraEngine.dir\\_CuraEngine.pdb /FS -c C:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\src\\support.cpp", "file": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/src/support.cpp", "output": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/build/Release/CMakeFiles/_CuraEngine.dir/src/support.cpp.obj"}, {"directory": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/build/Release", "command": "C:\\PROGRA~1\\MICROS~1\\2022\\COMMUN~1\\VC\\Tools\\MSVC\\1444~1.352\\bin\\Hostx64\\x64\\cl.exe  /nologo /TP -DAGRPC_BOOST_ASIO -DARCUS -DBOOST_ALL_NO_LIB -DBOOST_STACKTRACE_USE_NOOP -DBOOST_STACKTRACE_USE_WINDBG -DBOOST_STACKTRACE_USE_WINDBG_CACHED -DCARES_STATICLIB -DCURA_ENGINE_HASH=\\\"unknown\\\" -DCURA_ENGINE_VERSION=\\\"5.11.0-alpha.0\\\" -DENABLE_PLUGINS -DNOMINMAX -DSENTRY_ENVIRONMENT=\\\"development\\\" -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSTB_TEXTEDIT_KEYTYPE=unsigned -IC:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\include -IC:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\build\\Release -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\arcusee7666f716199\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\proto47a676cb9257b\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\zlibcfb9789dc2a53\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\spdlo2d4b540ab0f22\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\fmtdb20aad6e1bd4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\range0301bf3d76d5d\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\clippf8ddcd4a5d961\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbo34ee9aeb574f9\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbofe72fb50cf7c4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbo1b62b48748b82\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\rapidf7a3355ba53c4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\stb6342cecb318f5\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\boosta79d738920bc3\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\scrip4cfd014ad4660\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\nearg388d58da7a54c\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\curaebb245e64b5ac7\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\asio-c31dc2a380bf0\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\grpc8b6c73b4e5756\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\absei6a15444d60d47\\p\\include -external:W0 /DWIN32 /D_WINDOWS /EHsc /O2 /Ob2 /DNDEBUG -std:c++20 -MD /utf-8 /permissive- /FoCMakeFiles\\_CuraEngine.dir\\src\\timeEstimate.cpp.obj /FdCMakeFiles\\_CuraEngine.dir\\_CuraEngine.pdb /FS -c C:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\src\\timeEstimate.cpp", "file": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/src/timeEstimate.cpp", "output": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/build/Release/CMakeFiles/_CuraEngine.dir/src/timeEstimate.cpp.obj"}, {"directory": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/build/Release", "command": "C:\\PROGRA~1\\MICROS~1\\2022\\COMMUN~1\\VC\\Tools\\MSVC\\1444~1.352\\bin\\Hostx64\\x64\\cl.exe  /nologo /TP -DAGRPC_BOOST_ASIO -DARCUS -DBOOST_ALL_NO_LIB -DBOOST_STACKTRACE_USE_NOOP -DBOOST_STACKTRACE_USE_WINDBG -DBOOST_STACKTRACE_USE_WINDBG_CACHED -DCARES_STATICLIB -DCURA_ENGINE_HASH=\\\"unknown\\\" -DCURA_ENGINE_VERSION=\\\"5.11.0-alpha.0\\\" -DENABLE_PLUGINS -DNOMINMAX -DSENTRY_ENVIRONMENT=\\\"development\\\" -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSTB_TEXTEDIT_KEYTYPE=unsigned -IC:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\include -IC:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\build\\Release -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\arcusee7666f716199\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\proto47a676cb9257b\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\zlibcfb9789dc2a53\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\spdlo2d4b540ab0f22\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\fmtdb20aad6e1bd4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\range0301bf3d76d5d\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\clippf8ddcd4a5d961\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbo34ee9aeb574f9\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbofe72fb50cf7c4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbo1b62b48748b82\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\rapidf7a3355ba53c4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\stb6342cecb318f5\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\boosta79d738920bc3\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\scrip4cfd014ad4660\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\nearg388d58da7a54c\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\curaebb245e64b5ac7\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\asio-c31dc2a380bf0\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\grpc8b6c73b4e5756\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\absei6a15444d60d47\\p\\include -external:W0 /DWIN32 /D_WINDOWS /EHsc /O2 /Ob2 /DNDEBUG -std:c++20 -MD /utf-8 /permissive- /FoCMakeFiles\\_CuraEngine.dir\\src\\TopSurface.cpp.obj /FdCMakeFiles\\_CuraEngine.dir\\_CuraEngine.pdb /FS -c C:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\src\\TopSurface.cpp", "file": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/src/TopSurface.cpp", "output": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/build/Release/CMakeFiles/_CuraEngine.dir/src/TopSurface.cpp.obj"}, {"directory": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/build/Release", "command": "C:\\PROGRA~1\\MICROS~1\\2022\\COMMUN~1\\VC\\Tools\\MSVC\\1444~1.352\\bin\\Hostx64\\x64\\cl.exe  /nologo /TP -DAGRPC_BOOST_ASIO -DARCUS -DBOOST_ALL_NO_LIB -DBOOST_STACKTRACE_USE_NOOP -DBOOST_STACKTRACE_USE_WINDBG -DBOOST_STACKTRACE_USE_WINDBG_CACHED -DCARES_STATICLIB -DCURA_ENGINE_HASH=\\\"unknown\\\" -DCURA_ENGINE_VERSION=\\\"5.11.0-alpha.0\\\" -DENABLE_PLUGINS -DNOMINMAX -DSENTRY_ENVIRONMENT=\\\"development\\\" -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSTB_TEXTEDIT_KEYTYPE=unsigned -IC:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\include -IC:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\build\\Release -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\arcusee7666f716199\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\proto47a676cb9257b\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\zlibcfb9789dc2a53\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\spdlo2d4b540ab0f22\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\fmtdb20aad6e1bd4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\range0301bf3d76d5d\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\clippf8ddcd4a5d961\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbo34ee9aeb574f9\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbofe72fb50cf7c4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbo1b62b48748b82\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\rapidf7a3355ba53c4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\stb6342cecb318f5\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\boosta79d738920bc3\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\scrip4cfd014ad4660\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\nearg388d58da7a54c\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\curaebb245e64b5ac7\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\asio-c31dc2a380bf0\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\grpc8b6c73b4e5756\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\absei6a15444d60d47\\p\\include -external:W0 /DWIN32 /D_WINDOWS /EHsc /O2 /Ob2 /DNDEBUG -std:c++20 -MD /utf-8 /permissive- /FoCMakeFiles\\_CuraEngine.dir\\src\\TreeSupportTipGenerator.cpp.obj /FdCMakeFiles\\_CuraEngine.dir\\_CuraEngine.pdb /FS -c C:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\src\\TreeSupportTipGenerator.cpp", "file": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/src/TreeSupportTipGenerator.cpp", "output": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/build/Release/CMakeFiles/_CuraEngine.dir/src/TreeSupportTipGenerator.cpp.obj"}, {"directory": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/build/Release", "command": "C:\\PROGRA~1\\MICROS~1\\2022\\COMMUN~1\\VC\\Tools\\MSVC\\1444~1.352\\bin\\Hostx64\\x64\\cl.exe  /nologo /TP -DAGRPC_BOOST_ASIO -DARCUS -DBOOST_ALL_NO_LIB -DBOOST_STACKTRACE_USE_NOOP -DBOOST_STACKTRACE_USE_WINDBG -DBOOST_STACKTRACE_USE_WINDBG_CACHED -DCARES_STATICLIB -DCURA_ENGINE_HASH=\\\"unknown\\\" -DCURA_ENGINE_VERSION=\\\"5.11.0-alpha.0\\\" -DENABLE_PLUGINS -DNOMINMAX -DSENTRY_ENVIRONMENT=\\\"development\\\" -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSTB_TEXTEDIT_KEYTYPE=unsigned -IC:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\include -IC:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\build\\Release -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\arcusee7666f716199\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\proto47a676cb9257b\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\zlibcfb9789dc2a53\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\spdlo2d4b540ab0f22\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\fmtdb20aad6e1bd4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\range0301bf3d76d5d\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\clippf8ddcd4a5d961\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbo34ee9aeb574f9\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbofe72fb50cf7c4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbo1b62b48748b82\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\rapidf7a3355ba53c4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\stb6342cecb318f5\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\boosta79d738920bc3\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\scrip4cfd014ad4660\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\nearg388d58da7a54c\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\curaebb245e64b5ac7\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\asio-c31dc2a380bf0\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\grpc8b6c73b4e5756\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\absei6a15444d60d47\\p\\include -external:W0 /DWIN32 /D_WINDOWS /EHsc /O2 /Ob2 /DNDEBUG -std:c++20 -MD /utf-8 /permissive- /FoCMakeFiles\\_CuraEngine.dir\\src\\TreeModelVolumes.cpp.obj /FdCMakeFiles\\_CuraEngine.dir\\_CuraEngine.pdb /FS -c C:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\src\\TreeModelVolumes.cpp", "file": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/src/TreeModelVolumes.cpp", "output": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/build/Release/CMakeFiles/_CuraEngine.dir/src/TreeModelVolumes.cpp.obj"}, {"directory": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/build/Release", "command": "C:\\PROGRA~1\\MICROS~1\\2022\\COMMUN~1\\VC\\Tools\\MSVC\\1444~1.352\\bin\\Hostx64\\x64\\cl.exe  /nologo /TP -DAGRPC_BOOST_ASIO -DARCUS -DBOOST_ALL_NO_LIB -DBOOST_STACKTRACE_USE_NOOP -DBOOST_STACKTRACE_USE_WINDBG -DBOOST_STACKTRACE_USE_WINDBG_CACHED -DCARES_STATICLIB -DCURA_ENGINE_HASH=\\\"unknown\\\" -DCURA_ENGINE_VERSION=\\\"5.11.0-alpha.0\\\" -DENABLE_PLUGINS -DNOMINMAX -DSENTRY_ENVIRONMENT=\\\"development\\\" -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSTB_TEXTEDIT_KEYTYPE=unsigned -IC:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\include -IC:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\build\\Release -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\arcusee7666f716199\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\proto47a676cb9257b\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\zlibcfb9789dc2a53\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\spdlo2d4b540ab0f22\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\fmtdb20aad6e1bd4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\range0301bf3d76d5d\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\clippf8ddcd4a5d961\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbo34ee9aeb574f9\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbofe72fb50cf7c4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbo1b62b48748b82\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\rapidf7a3355ba53c4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\stb6342cecb318f5\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\boosta79d738920bc3\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\scrip4cfd014ad4660\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\nearg388d58da7a54c\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\curaebb245e64b5ac7\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\asio-c31dc2a380bf0\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\grpc8b6c73b4e5756\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\absei6a15444d60d47\\p\\include -external:W0 /DWIN32 /D_WINDOWS /EHsc /O2 /Ob2 /DNDEBUG -std:c++20 -MD /utf-8 /permissive- /FoCMakeFiles\\_CuraEngine.dir\\src\\TreeSupport.cpp.obj /FdCMakeFiles\\_CuraEngine.dir\\_CuraEngine.pdb /FS -c C:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\src\\TreeSupport.cpp", "file": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/src/TreeSupport.cpp", "output": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/build/Release/CMakeFiles/_CuraEngine.dir/src/TreeSupport.cpp.obj"}, {"directory": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/build/Release", "command": "C:\\PROGRA~1\\MICROS~1\\2022\\COMMUN~1\\VC\\Tools\\MSVC\\1444~1.352\\bin\\Hostx64\\x64\\cl.exe  /nologo /TP -DAGRPC_BOOST_ASIO -DARCUS -DBOOST_ALL_NO_LIB -DBOOST_STACKTRACE_USE_NOOP -DBOOST_STACKTRACE_USE_WINDBG -DBOOST_STACKTRACE_USE_WINDBG_CACHED -DCARES_STATICLIB -DCURA_ENGINE_HASH=\\\"unknown\\\" -DCURA_ENGINE_VERSION=\\\"5.11.0-alpha.0\\\" -DENABLE_PLUGINS -DNOMINMAX -DSENTRY_ENVIRONMENT=\\\"development\\\" -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSTB_TEXTEDIT_KEYTYPE=unsigned -IC:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\include -IC:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\build\\Release -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\arcusee7666f716199\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\proto47a676cb9257b\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\zlibcfb9789dc2a53\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\spdlo2d4b540ab0f22\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\fmtdb20aad6e1bd4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\range0301bf3d76d5d\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\clippf8ddcd4a5d961\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbo34ee9aeb574f9\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbofe72fb50cf7c4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbo1b62b48748b82\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\rapidf7a3355ba53c4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\stb6342cecb318f5\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\boosta79d738920bc3\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\scrip4cfd014ad4660\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\nearg388d58da7a54c\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\curaebb245e64b5ac7\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\asio-c31dc2a380bf0\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\grpc8b6c73b4e5756\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\absei6a15444d60d47\\p\\include -external:W0 /DWIN32 /D_WINDOWS /EHsc /O2 /Ob2 /DNDEBUG -std:c++20 -MD /utf-8 /permissive- /FoCMakeFiles\\_CuraEngine.dir\\src\\WallsComputation.cpp.obj /FdCMakeFiles\\_CuraEngine.dir\\_CuraEngine.pdb /FS -c C:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\src\\WallsComputation.cpp", "file": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/src/WallsComputation.cpp", "output": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/build/Release/CMakeFiles/_CuraEngine.dir/src/WallsComputation.cpp.obj"}, {"directory": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/build/Release", "command": "C:\\PROGRA~1\\MICROS~1\\2022\\COMMUN~1\\VC\\Tools\\MSVC\\1444~1.352\\bin\\Hostx64\\x64\\cl.exe  /nologo /TP -DAGRPC_BOOST_ASIO -DARCUS -DBOOST_ALL_NO_LIB -DBOOST_STACKTRACE_USE_NOOP -DBOOST_STACKTRACE_USE_WINDBG -DBOOST_STACKTRACE_USE_WINDBG_CACHED -DCARES_STATICLIB -DCURA_ENGINE_HASH=\\\"unknown\\\" -DCURA_ENGINE_VERSION=\\\"5.11.0-alpha.0\\\" -DENABLE_PLUGINS -DNOMINMAX -DSENTRY_ENVIRONMENT=\\\"development\\\" -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSTB_TEXTEDIT_KEYTYPE=unsigned -IC:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\include -IC:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\build\\Release -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\arcusee7666f716199\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\proto47a676cb9257b\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\zlibcfb9789dc2a53\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\spdlo2d4b540ab0f22\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\fmtdb20aad6e1bd4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\range0301bf3d76d5d\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\clippf8ddcd4a5d961\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbo34ee9aeb574f9\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbofe72fb50cf7c4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbo1b62b48748b82\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\rapidf7a3355ba53c4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\stb6342cecb318f5\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\boosta79d738920bc3\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\scrip4cfd014ad4660\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\nearg388d58da7a54c\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\curaebb245e64b5ac7\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\asio-c31dc2a380bf0\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\grpc8b6c73b4e5756\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\absei6a15444d60d47\\p\\include -external:W0 /DWIN32 /D_WINDOWS /EHsc /O2 /Ob2 /DNDEBUG -std:c++20 -MD /utf-8 /permissive- /FoCMakeFiles\\_CuraEngine.dir\\src\\WallToolPaths.cpp.obj /FdCMakeFiles\\_CuraEngine.dir\\_CuraEngine.pdb /FS -c C:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\src\\WallToolPaths.cpp", "file": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/src/WallToolPaths.cpp", "output": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/build/Release/CMakeFiles/_CuraEngine.dir/src/WallToolPaths.cpp.obj"}, {"directory": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/build/Release", "command": "C:\\PROGRA~1\\MICROS~1\\2022\\COMMUN~1\\VC\\Tools\\MSVC\\1444~1.352\\bin\\Hostx64\\x64\\cl.exe  /nologo /TP -DAGRPC_BOOST_ASIO -DARCUS -DBOOST_ALL_NO_LIB -DBOOST_STACKTRACE_USE_NOOP -DBOOST_STACKTRACE_USE_WINDBG -DBOOST_STACKTRACE_USE_WINDBG_CACHED -DCARES_STATICLIB -DCURA_ENGINE_HASH=\\\"unknown\\\" -DCURA_ENGINE_VERSION=\\\"5.11.0-alpha.0\\\" -DENABLE_PLUGINS -DNOMINMAX -DSENTRY_ENVIRONMENT=\\\"development\\\" -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSTB_TEXTEDIT_KEYTYPE=unsigned -IC:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\include -IC:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\build\\Release -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\arcusee7666f716199\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\proto47a676cb9257b\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\zlibcfb9789dc2a53\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\spdlo2d4b540ab0f22\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\fmtdb20aad6e1bd4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\range0301bf3d76d5d\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\clippf8ddcd4a5d961\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbo34ee9aeb574f9\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbofe72fb50cf7c4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbo1b62b48748b82\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\rapidf7a3355ba53c4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\stb6342cecb318f5\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\boosta79d738920bc3\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\scrip4cfd014ad4660\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\nearg388d58da7a54c\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\curaebb245e64b5ac7\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\asio-c31dc2a380bf0\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\grpc8b6c73b4e5756\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\absei6a15444d60d47\\p\\include -external:W0 /DWIN32 /D_WINDOWS /EHsc /O2 /Ob2 /DNDEBUG -std:c++20 -MD /utf-8 /permissive- /FoCMakeFiles\\_CuraEngine.dir\\src\\BeadingStrategy\\BeadingStrategy.cpp.obj /FdCMakeFiles\\_CuraEngine.dir\\_CuraEngine.pdb /FS -c C:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\src\\BeadingStrategy\\BeadingStrategy.cpp", "file": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/src/BeadingStrategy/BeadingStrategy.cpp", "output": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/build/Release/CMakeFiles/_CuraEngine.dir/src/BeadingStrategy/BeadingStrategy.cpp.obj"}, {"directory": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/build/Release", "command": "C:\\PROGRA~1\\MICROS~1\\2022\\COMMUN~1\\VC\\Tools\\MSVC\\1444~1.352\\bin\\Hostx64\\x64\\cl.exe  /nologo /TP -DAGRPC_BOOST_ASIO -DARCUS -DBOOST_ALL_NO_LIB -DBOOST_STACKTRACE_USE_NOOP -DBOOST_STACKTRACE_USE_WINDBG -DBOOST_STACKTRACE_USE_WINDBG_CACHED -DCARES_STATICLIB -DCURA_ENGINE_HASH=\\\"unknown\\\" -DCURA_ENGINE_VERSION=\\\"5.11.0-alpha.0\\\" -DENABLE_PLUGINS -DNOMINMAX -DSENTRY_ENVIRONMENT=\\\"development\\\" -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSTB_TEXTEDIT_KEYTYPE=unsigned -IC:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\include -IC:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\build\\Release -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\arcusee7666f716199\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\proto47a676cb9257b\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\zlibcfb9789dc2a53\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\spdlo2d4b540ab0f22\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\fmtdb20aad6e1bd4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\range0301bf3d76d5d\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\clippf8ddcd4a5d961\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbo34ee9aeb574f9\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbofe72fb50cf7c4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbo1b62b48748b82\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\rapidf7a3355ba53c4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\stb6342cecb318f5\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\boosta79d738920bc3\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\scrip4cfd014ad4660\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\nearg388d58da7a54c\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\curaebb245e64b5ac7\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\asio-c31dc2a380bf0\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\grpc8b6c73b4e5756\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\absei6a15444d60d47\\p\\include -external:W0 /DWIN32 /D_WINDOWS /EHsc /O2 /Ob2 /DNDEBUG -std:c++20 -MD /utf-8 /permissive- /FoCMakeFiles\\_CuraEngine.dir\\src\\BeadingStrategy\\BeadingStrategyFactory.cpp.obj /FdCMakeFiles\\_CuraEngine.dir\\_CuraEngine.pdb /FS -c C:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\src\\BeadingStrategy\\BeadingStrategyFactory.cpp", "file": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/src/BeadingStrategy/BeadingStrategyFactory.cpp", "output": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/build/Release/CMakeFiles/_CuraEngine.dir/src/BeadingStrategy/BeadingStrategyFactory.cpp.obj"}, {"directory": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/build/Release", "command": "C:\\PROGRA~1\\MICROS~1\\2022\\COMMUN~1\\VC\\Tools\\MSVC\\1444~1.352\\bin\\Hostx64\\x64\\cl.exe  /nologo /TP -DAGRPC_BOOST_ASIO -DARCUS -DBOOST_ALL_NO_LIB -DBOOST_STACKTRACE_USE_NOOP -DBOOST_STACKTRACE_USE_WINDBG -DBOOST_STACKTRACE_USE_WINDBG_CACHED -DCARES_STATICLIB -DCURA_ENGINE_HASH=\\\"unknown\\\" -DCURA_ENGINE_VERSION=\\\"5.11.0-alpha.0\\\" -DENABLE_PLUGINS -DNOMINMAX -DSENTRY_ENVIRONMENT=\\\"development\\\" -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSTB_TEXTEDIT_KEYTYPE=unsigned -IC:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\include -IC:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\build\\Release -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\arcusee7666f716199\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\proto47a676cb9257b\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\zlibcfb9789dc2a53\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\spdlo2d4b540ab0f22\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\fmtdb20aad6e1bd4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\range0301bf3d76d5d\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\clippf8ddcd4a5d961\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbo34ee9aeb574f9\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbofe72fb50cf7c4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbo1b62b48748b82\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\rapidf7a3355ba53c4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\stb6342cecb318f5\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\boosta79d738920bc3\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\scrip4cfd014ad4660\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\nearg388d58da7a54c\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\curaebb245e64b5ac7\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\asio-c31dc2a380bf0\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\grpc8b6c73b4e5756\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\absei6a15444d60d47\\p\\include -external:W0 /DWIN32 /D_WINDOWS /EHsc /O2 /Ob2 /DNDEBUG -std:c++20 -MD /utf-8 /permissive- /FoCMakeFiles\\_CuraEngine.dir\\src\\BeadingStrategy\\DistributedBeadingStrategy.cpp.obj /FdCMakeFiles\\_CuraEngine.dir\\_CuraEngine.pdb /FS -c C:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\src\\BeadingStrategy\\DistributedBeadingStrategy.cpp", "file": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/src/BeadingStrategy/DistributedBeadingStrategy.cpp", "output": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/build/Release/CMakeFiles/_CuraEngine.dir/src/BeadingStrategy/DistributedBeadingStrategy.cpp.obj"}, {"directory": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/build/Release", "command": "C:\\PROGRA~1\\MICROS~1\\2022\\COMMUN~1\\VC\\Tools\\MSVC\\1444~1.352\\bin\\Hostx64\\x64\\cl.exe  /nologo /TP -DAGRPC_BOOST_ASIO -DARCUS -DBOOST_ALL_NO_LIB -DBOOST_STACKTRACE_USE_NOOP -DBOOST_STACKTRACE_USE_WINDBG -DBOOST_STACKTRACE_USE_WINDBG_CACHED -DCARES_STATICLIB -DCURA_ENGINE_HASH=\\\"unknown\\\" -DCURA_ENGINE_VERSION=\\\"5.11.0-alpha.0\\\" -DENABLE_PLUGINS -DNOMINMAX -DSENTRY_ENVIRONMENT=\\\"development\\\" -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSTB_TEXTEDIT_KEYTYPE=unsigned -IC:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\include -IC:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\build\\Release -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\arcusee7666f716199\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\proto47a676cb9257b\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\zlibcfb9789dc2a53\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\spdlo2d4b540ab0f22\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\fmtdb20aad6e1bd4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\range0301bf3d76d5d\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\clippf8ddcd4a5d961\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbo34ee9aeb574f9\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbofe72fb50cf7c4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbo1b62b48748b82\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\rapidf7a3355ba53c4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\stb6342cecb318f5\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\boosta79d738920bc3\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\scrip4cfd014ad4660\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\nearg388d58da7a54c\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\curaebb245e64b5ac7\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\asio-c31dc2a380bf0\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\grpc8b6c73b4e5756\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\absei6a15444d60d47\\p\\include -external:W0 /DWIN32 /D_WINDOWS /EHsc /O2 /Ob2 /DNDEBUG -std:c++20 -MD /utf-8 /permissive- /FoCMakeFiles\\_CuraEngine.dir\\src\\BeadingStrategy\\LimitedBeadingStrategy.cpp.obj /FdCMakeFiles\\_CuraEngine.dir\\_CuraEngine.pdb /FS -c C:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\src\\BeadingStrategy\\LimitedBeadingStrategy.cpp", "file": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/src/BeadingStrategy/LimitedBeadingStrategy.cpp", "output": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/build/Release/CMakeFiles/_CuraEngine.dir/src/BeadingStrategy/LimitedBeadingStrategy.cpp.obj"}, {"directory": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/build/Release", "command": "C:\\PROGRA~1\\MICROS~1\\2022\\COMMUN~1\\VC\\Tools\\MSVC\\1444~1.352\\bin\\Hostx64\\x64\\cl.exe  /nologo /TP -DAGRPC_BOOST_ASIO -DARCUS -DBOOST_ALL_NO_LIB -DBOOST_STACKTRACE_USE_NOOP -DBOOST_STACKTRACE_USE_WINDBG -DBOOST_STACKTRACE_USE_WINDBG_CACHED -DCARES_STATICLIB -DCURA_ENGINE_HASH=\\\"unknown\\\" -DCURA_ENGINE_VERSION=\\\"5.11.0-alpha.0\\\" -DENABLE_PLUGINS -DNOMINMAX -DSENTRY_ENVIRONMENT=\\\"development\\\" -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSTB_TEXTEDIT_KEYTYPE=unsigned -IC:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\include -IC:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\build\\Release -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\arcusee7666f716199\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\proto47a676cb9257b\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\zlibcfb9789dc2a53\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\spdlo2d4b540ab0f22\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\fmtdb20aad6e1bd4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\range0301bf3d76d5d\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\clippf8ddcd4a5d961\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbo34ee9aeb574f9\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbofe72fb50cf7c4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbo1b62b48748b82\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\rapidf7a3355ba53c4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\stb6342cecb318f5\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\boosta79d738920bc3\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\scrip4cfd014ad4660\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\nearg388d58da7a54c\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\curaebb245e64b5ac7\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\asio-c31dc2a380bf0\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\grpc8b6c73b4e5756\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\absei6a15444d60d47\\p\\include -external:W0 /DWIN32 /D_WINDOWS /EHsc /O2 /Ob2 /DNDEBUG -std:c++20 -MD /utf-8 /permissive- /FoCMakeFiles\\_CuraEngine.dir\\src\\BeadingStrategy\\RedistributeBeadingStrategy.cpp.obj /FdCMakeFiles\\_CuraEngine.dir\\_CuraEngine.pdb /FS -c C:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\src\\BeadingStrategy\\RedistributeBeadingStrategy.cpp", "file": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/src/BeadingStrategy/RedistributeBeadingStrategy.cpp", "output": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/build/Release/CMakeFiles/_CuraEngine.dir/src/BeadingStrategy/RedistributeBeadingStrategy.cpp.obj"}, {"directory": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/build/Release", "command": "C:\\PROGRA~1\\MICROS~1\\2022\\COMMUN~1\\VC\\Tools\\MSVC\\1444~1.352\\bin\\Hostx64\\x64\\cl.exe  /nologo /TP -DAGRPC_BOOST_ASIO -DARCUS -DBOOST_ALL_NO_LIB -DBOOST_STACKTRACE_USE_NOOP -DBOOST_STACKTRACE_USE_WINDBG -DBOOST_STACKTRACE_USE_WINDBG_CACHED -DCARES_STATICLIB -DCURA_ENGINE_HASH=\\\"unknown\\\" -DCURA_ENGINE_VERSION=\\\"5.11.0-alpha.0\\\" -DENABLE_PLUGINS -DNOMINMAX -DSENTRY_ENVIRONMENT=\\\"development\\\" -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSTB_TEXTEDIT_KEYTYPE=unsigned -IC:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\include -IC:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\build\\Release -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\arcusee7666f716199\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\proto47a676cb9257b\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\zlibcfb9789dc2a53\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\spdlo2d4b540ab0f22\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\fmtdb20aad6e1bd4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\range0301bf3d76d5d\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\clippf8ddcd4a5d961\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbo34ee9aeb574f9\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbofe72fb50cf7c4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbo1b62b48748b82\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\rapidf7a3355ba53c4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\stb6342cecb318f5\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\boosta79d738920bc3\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\scrip4cfd014ad4660\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\nearg388d58da7a54c\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\curaebb245e64b5ac7\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\asio-c31dc2a380bf0\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\grpc8b6c73b4e5756\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\absei6a15444d60d47\\p\\include -external:W0 /DWIN32 /D_WINDOWS /EHsc /O2 /Ob2 /DNDEBUG -std:c++20 -MD /utf-8 /permissive- /FoCMakeFiles\\_CuraEngine.dir\\src\\BeadingStrategy\\WideningBeadingStrategy.cpp.obj /FdCMakeFiles\\_CuraEngine.dir\\_CuraEngine.pdb /FS -c C:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\src\\BeadingStrategy\\WideningBeadingStrategy.cpp", "file": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/src/BeadingStrategy/WideningBeadingStrategy.cpp", "output": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/build/Release/CMakeFiles/_CuraEngine.dir/src/BeadingStrategy/WideningBeadingStrategy.cpp.obj"}, {"directory": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/build/Release", "command": "C:\\PROGRA~1\\MICROS~1\\2022\\COMMUN~1\\VC\\Tools\\MSVC\\1444~1.352\\bin\\Hostx64\\x64\\cl.exe  /nologo /TP -DAGRPC_BOOST_ASIO -DARCUS -DBOOST_ALL_NO_LIB -DBOOST_STACKTRACE_USE_NOOP -DBOOST_STACKTRACE_USE_WINDBG -DBOOST_STACKTRACE_USE_WINDBG_CACHED -DCARES_STATICLIB -DCURA_ENGINE_HASH=\\\"unknown\\\" -DCURA_ENGINE_VERSION=\\\"5.11.0-alpha.0\\\" -DENABLE_PLUGINS -DNOMINMAX -DSENTRY_ENVIRONMENT=\\\"development\\\" -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSTB_TEXTEDIT_KEYTYPE=unsigned -IC:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\include -IC:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\build\\Release -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\arcusee7666f716199\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\proto47a676cb9257b\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\zlibcfb9789dc2a53\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\spdlo2d4b540ab0f22\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\fmtdb20aad6e1bd4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\range0301bf3d76d5d\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\clippf8ddcd4a5d961\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbo34ee9aeb574f9\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbofe72fb50cf7c4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbo1b62b48748b82\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\rapidf7a3355ba53c4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\stb6342cecb318f5\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\boosta79d738920bc3\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\scrip4cfd014ad4660\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\nearg388d58da7a54c\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\curaebb245e64b5ac7\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\asio-c31dc2a380bf0\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\grpc8b6c73b4e5756\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\absei6a15444d60d47\\p\\include -external:W0 /DWIN32 /D_WINDOWS /EHsc /O2 /Ob2 /DNDEBUG -std:c++20 -MD /utf-8 /permissive- /FoCMakeFiles\\_CuraEngine.dir\\src\\BeadingStrategy\\OuterWallInsetBeadingStrategy.cpp.obj /FdCMakeFiles\\_CuraEngine.dir\\_CuraEngine.pdb /FS -c C:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\src\\BeadingStrategy\\OuterWallInsetBeadingStrategy.cpp", "file": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/src/BeadingStrategy/OuterWallInsetBeadingStrategy.cpp", "output": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/build/Release/CMakeFiles/_CuraEngine.dir/src/BeadingStrategy/OuterWallInsetBeadingStrategy.cpp.obj"}, {"directory": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/build/Release", "command": "C:\\PROGRA~1\\MICROS~1\\2022\\COMMUN~1\\VC\\Tools\\MSVC\\1444~1.352\\bin\\Hostx64\\x64\\cl.exe  /nologo /TP -DAGRPC_BOOST_ASIO -DARCUS -DBOOST_ALL_NO_LIB -DBOOST_STACKTRACE_USE_NOOP -DBOOST_STACKTRACE_USE_WINDBG -DBOOST_STACKTRACE_USE_WINDBG_CACHED -DCARES_STATICLIB -DCURA_ENGINE_HASH=\\\"unknown\\\" -DCURA_ENGINE_VERSION=\\\"5.11.0-alpha.0\\\" -DENABLE_PLUGINS -DNOMINMAX -DSENTRY_ENVIRONMENT=\\\"development\\\" -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSTB_TEXTEDIT_KEYTYPE=unsigned -IC:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\include -IC:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\build\\Release -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\arcusee7666f716199\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\proto47a676cb9257b\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\zlibcfb9789dc2a53\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\spdlo2d4b540ab0f22\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\fmtdb20aad6e1bd4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\range0301bf3d76d5d\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\clippf8ddcd4a5d961\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbo34ee9aeb574f9\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbofe72fb50cf7c4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbo1b62b48748b82\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\rapidf7a3355ba53c4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\stb6342cecb318f5\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\boosta79d738920bc3\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\scrip4cfd014ad4660\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\nearg388d58da7a54c\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\curaebb245e64b5ac7\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\asio-c31dc2a380bf0\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\grpc8b6c73b4e5756\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\absei6a15444d60d47\\p\\include -external:W0 /DWIN32 /D_WINDOWS /EHsc /O2 /Ob2 /DNDEBUG -std:c++20 -MD /utf-8 /permissive- /FoCMakeFiles\\_CuraEngine.dir\\src\\communication\\ArcusCommunication.cpp.obj /FdCMakeFiles\\_CuraEngine.dir\\_CuraEngine.pdb /FS -c C:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\src\\communication\\ArcusCommunication.cpp", "file": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/src/communication/ArcusCommunication.cpp", "output": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/build/Release/CMakeFiles/_CuraEngine.dir/src/communication/ArcusCommunication.cpp.obj"}, {"directory": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/build/Release", "command": "C:\\PROGRA~1\\MICROS~1\\2022\\COMMUN~1\\VC\\Tools\\MSVC\\1444~1.352\\bin\\Hostx64\\x64\\cl.exe  /nologo /TP -DAGRPC_BOOST_ASIO -DARCUS -DBOOST_ALL_NO_LIB -DBOOST_STACKTRACE_USE_NOOP -DBOOST_STACKTRACE_USE_WINDBG -DBOOST_STACKTRACE_USE_WINDBG_CACHED -DCARES_STATICLIB -DCURA_ENGINE_HASH=\\\"unknown\\\" -DCURA_ENGINE_VERSION=\\\"5.11.0-alpha.0\\\" -DENABLE_PLUGINS -DNOMINMAX -DSENTRY_ENVIRONMENT=\\\"development\\\" -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSTB_TEXTEDIT_KEYTYPE=unsigned -IC:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\include -IC:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\build\\Release -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\arcusee7666f716199\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\proto47a676cb9257b\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\zlibcfb9789dc2a53\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\spdlo2d4b540ab0f22\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\fmtdb20aad6e1bd4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\range0301bf3d76d5d\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\clippf8ddcd4a5d961\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbo34ee9aeb574f9\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbofe72fb50cf7c4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbo1b62b48748b82\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\rapidf7a3355ba53c4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\stb6342cecb318f5\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\boosta79d738920bc3\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\scrip4cfd014ad4660\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\nearg388d58da7a54c\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\curaebb245e64b5ac7\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\asio-c31dc2a380bf0\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\grpc8b6c73b4e5756\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\absei6a15444d60d47\\p\\include -external:W0 /DWIN32 /D_WINDOWS /EHsc /O2 /Ob2 /DNDEBUG -std:c++20 -MD /utf-8 /permissive- /FoCMakeFiles\\_CuraEngine.dir\\src\\communication\\ArcusCommunicationPrivate.cpp.obj /FdCMakeFiles\\_CuraEngine.dir\\_CuraEngine.pdb /FS -c C:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\src\\communication\\ArcusCommunicationPrivate.cpp", "file": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/src/communication/ArcusCommunicationPrivate.cpp", "output": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/build/Release/CMakeFiles/_CuraEngine.dir/src/communication/ArcusCommunicationPrivate.cpp.obj"}, {"directory": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/build/Release", "command": "C:\\PROGRA~1\\MICROS~1\\2022\\COMMUN~1\\VC\\Tools\\MSVC\\1444~1.352\\bin\\Hostx64\\x64\\cl.exe  /nologo /TP -DAGRPC_BOOST_ASIO -DARCUS -DBOOST_ALL_NO_LIB -DBOOST_STACKTRACE_USE_NOOP -DBOOST_STACKTRACE_USE_WINDBG -DBOOST_STACKTRACE_USE_WINDBG_CACHED -DCARES_STATICLIB -DCURA_ENGINE_HASH=\\\"unknown\\\" -DCURA_ENGINE_VERSION=\\\"5.11.0-alpha.0\\\" -DENABLE_PLUGINS -DNOMINMAX -DSENTRY_ENVIRONMENT=\\\"development\\\" -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSTB_TEXTEDIT_KEYTYPE=unsigned -IC:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\include -IC:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\build\\Release -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\arcusee7666f716199\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\proto47a676cb9257b\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\zlibcfb9789dc2a53\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\spdlo2d4b540ab0f22\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\fmtdb20aad6e1bd4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\range0301bf3d76d5d\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\clippf8ddcd4a5d961\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbo34ee9aeb574f9\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbofe72fb50cf7c4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbo1b62b48748b82\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\rapidf7a3355ba53c4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\stb6342cecb318f5\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\boosta79d738920bc3\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\scrip4cfd014ad4660\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\nearg388d58da7a54c\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\curaebb245e64b5ac7\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\asio-c31dc2a380bf0\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\grpc8b6c73b4e5756\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\absei6a15444d60d47\\p\\include -external:W0 /DWIN32 /D_WINDOWS /EHsc /O2 /Ob2 /DNDEBUG -std:c++20 -MD /utf-8 /permissive- /FoCMakeFiles\\_CuraEngine.dir\\src\\communication\\CommandLine.cpp.obj /FdCMakeFiles\\_CuraEngine.dir\\_CuraEngine.pdb /FS -c C:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\src\\communication\\CommandLine.cpp", "file": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/src/communication/CommandLine.cpp", "output": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/build/Release/CMakeFiles/_CuraEngine.dir/src/communication/CommandLine.cpp.obj"}, {"directory": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/build/Release", "command": "C:\\PROGRA~1\\MICROS~1\\2022\\COMMUN~1\\VC\\Tools\\MSVC\\1444~1.352\\bin\\Hostx64\\x64\\cl.exe  /nologo /TP -DAGRPC_BOOST_ASIO -DARCUS -DBOOST_ALL_NO_LIB -DBOOST_STACKTRACE_USE_NOOP -DBOOST_STACKTRACE_USE_WINDBG -DBOOST_STACKTRACE_USE_WINDBG_CACHED -DCARES_STATICLIB -DCURA_ENGINE_HASH=\\\"unknown\\\" -DCURA_ENGINE_VERSION=\\\"5.11.0-alpha.0\\\" -DENABLE_PLUGINS -DNOMINMAX -DSENTRY_ENVIRONMENT=\\\"development\\\" -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSTB_TEXTEDIT_KEYTYPE=unsigned -IC:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\include -IC:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\build\\Release -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\arcusee7666f716199\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\proto47a676cb9257b\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\zlibcfb9789dc2a53\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\spdlo2d4b540ab0f22\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\fmtdb20aad6e1bd4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\range0301bf3d76d5d\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\clippf8ddcd4a5d961\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbo34ee9aeb574f9\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbofe72fb50cf7c4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbo1b62b48748b82\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\rapidf7a3355ba53c4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\stb6342cecb318f5\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\boosta79d738920bc3\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\scrip4cfd014ad4660\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\nearg388d58da7a54c\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\curaebb245e64b5ac7\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\asio-c31dc2a380bf0\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\grpc8b6c73b4e5756\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\absei6a15444d60d47\\p\\include -external:W0 /DWIN32 /D_WINDOWS /EHsc /O2 /Ob2 /DNDEBUG -std:c++20 -MD /utf-8 /permissive- /FoCMakeFiles\\_CuraEngine.dir\\src\\communication\\EmscriptenCommunication.cpp.obj /FdCMakeFiles\\_CuraEngine.dir\\_CuraEngine.pdb /FS -c C:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\src\\communication\\EmscriptenCommunication.cpp", "file": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/src/communication/EmscriptenCommunication.cpp", "output": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/build/Release/CMakeFiles/_CuraEngine.dir/src/communication/EmscriptenCommunication.cpp.obj"}, {"directory": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/build/Release", "command": "C:\\PROGRA~1\\MICROS~1\\2022\\COMMUN~1\\VC\\Tools\\MSVC\\1444~1.352\\bin\\Hostx64\\x64\\cl.exe  /nologo /TP -DAGRPC_BOOST_ASIO -DARCUS -DBOOST_ALL_NO_LIB -DBOOST_STACKTRACE_USE_NOOP -DBOOST_STACKTRACE_USE_WINDBG -DBOOST_STACKTRACE_USE_WINDBG_CACHED -DCARES_STATICLIB -DCURA_ENGINE_HASH=\\\"unknown\\\" -DCURA_ENGINE_VERSION=\\\"5.11.0-alpha.0\\\" -DENABLE_PLUGINS -DNOMINMAX -DSENTRY_ENVIRONMENT=\\\"development\\\" -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSTB_TEXTEDIT_KEYTYPE=unsigned -IC:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\include -IC:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\build\\Release -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\arcusee7666f716199\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\proto47a676cb9257b\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\zlibcfb9789dc2a53\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\spdlo2d4b540ab0f22\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\fmtdb20aad6e1bd4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\range0301bf3d76d5d\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\clippf8ddcd4a5d961\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbo34ee9aeb574f9\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbofe72fb50cf7c4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbo1b62b48748b82\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\rapidf7a3355ba53c4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\stb6342cecb318f5\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\boosta79d738920bc3\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\scrip4cfd014ad4660\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\nearg388d58da7a54c\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\curaebb245e64b5ac7\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\asio-c31dc2a380bf0\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\grpc8b6c73b4e5756\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\absei6a15444d60d47\\p\\include -external:W0 /DWIN32 /D_WINDOWS /EHsc /O2 /Ob2 /DNDEBUG -std:c++20 -MD /utf-8 /permissive- /FoCMakeFiles\\_CuraEngine.dir\\src\\communication\\Listener.cpp.obj /FdCMakeFiles\\_CuraEngine.dir\\_CuraEngine.pdb /FS -c C:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\src\\communication\\Listener.cpp", "file": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/src/communication/Listener.cpp", "output": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/build/Release/CMakeFiles/_CuraEngine.dir/src/communication/Listener.cpp.obj"}, {"directory": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/build/Release", "command": "C:\\PROGRA~1\\MICROS~1\\2022\\COMMUN~1\\VC\\Tools\\MSVC\\1444~1.352\\bin\\Hostx64\\x64\\cl.exe  /nologo /TP -DAGRPC_BOOST_ASIO -DARCUS -DBOOST_ALL_NO_LIB -DBOOST_STACKTRACE_USE_NOOP -DBOOST_STACKTRACE_USE_WINDBG -DBOOST_STACKTRACE_USE_WINDBG_CACHED -DCARES_STATICLIB -DCURA_ENGINE_HASH=\\\"unknown\\\" -DCURA_ENGINE_VERSION=\\\"5.11.0-alpha.0\\\" -DENABLE_PLUGINS -DNOMINMAX -DSENTRY_ENVIRONMENT=\\\"development\\\" -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSTB_TEXTEDIT_KEYTYPE=unsigned -IC:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\include -IC:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\build\\Release -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\arcusee7666f716199\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\proto47a676cb9257b\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\zlibcfb9789dc2a53\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\spdlo2d4b540ab0f22\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\fmtdb20aad6e1bd4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\range0301bf3d76d5d\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\clippf8ddcd4a5d961\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbo34ee9aeb574f9\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbofe72fb50cf7c4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbo1b62b48748b82\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\rapidf7a3355ba53c4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\stb6342cecb318f5\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\boosta79d738920bc3\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\scrip4cfd014ad4660\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\nearg388d58da7a54c\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\curaebb245e64b5ac7\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\asio-c31dc2a380bf0\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\grpc8b6c73b4e5756\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\absei6a15444d60d47\\p\\include -external:W0 /DWIN32 /D_WINDOWS /EHsc /O2 /Ob2 /DNDEBUG -std:c++20 -MD /utf-8 /permissive- /FoCMakeFiles\\_CuraEngine.dir\\src\\infill\\ImageBasedDensityProvider.cpp.obj /FdCMakeFiles\\_CuraEngine.dir\\_CuraEngine.pdb /FS -c C:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\src\\infill\\ImageBasedDensityProvider.cpp", "file": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/src/infill/ImageBasedDensityProvider.cpp", "output": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/build/Release/CMakeFiles/_CuraEngine.dir/src/infill/ImageBasedDensityProvider.cpp.obj"}, {"directory": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/build/Release", "command": "C:\\PROGRA~1\\MICROS~1\\2022\\COMMUN~1\\VC\\Tools\\MSVC\\1444~1.352\\bin\\Hostx64\\x64\\cl.exe  /nologo /TP -DAGRPC_BOOST_ASIO -DARCUS -DBOOST_ALL_NO_LIB -DBOOST_STACKTRACE_USE_NOOP -DBOOST_STACKTRACE_USE_WINDBG -DBOOST_STACKTRACE_USE_WINDBG_CACHED -DCARES_STATICLIB -DCURA_ENGINE_HASH=\\\"unknown\\\" -DCURA_ENGINE_VERSION=\\\"5.11.0-alpha.0\\\" -DENABLE_PLUGINS -DNOMINMAX -DSENTRY_ENVIRONMENT=\\\"development\\\" -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSTB_TEXTEDIT_KEYTYPE=unsigned -IC:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\include -IC:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\build\\Release -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\arcusee7666f716199\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\proto47a676cb9257b\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\zlibcfb9789dc2a53\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\spdlo2d4b540ab0f22\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\fmtdb20aad6e1bd4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\range0301bf3d76d5d\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\clippf8ddcd4a5d961\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbo34ee9aeb574f9\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbofe72fb50cf7c4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbo1b62b48748b82\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\rapidf7a3355ba53c4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\stb6342cecb318f5\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\boosta79d738920bc3\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\scrip4cfd014ad4660\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\nearg388d58da7a54c\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\curaebb245e64b5ac7\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\asio-c31dc2a380bf0\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\grpc8b6c73b4e5756\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\absei6a15444d60d47\\p\\include -external:W0 /DWIN32 /D_WINDOWS /EHsc /O2 /Ob2 /DNDEBUG -std:c++20 -MD /utf-8 /permissive- /FoCMakeFiles\\_CuraEngine.dir\\src\\infill\\NoZigZagConnectorProcessor.cpp.obj /FdCMakeFiles\\_CuraEngine.dir\\_CuraEngine.pdb /FS -c C:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\src\\infill\\NoZigZagConnectorProcessor.cpp", "file": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/src/infill/NoZigZagConnectorProcessor.cpp", "output": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/build/Release/CMakeFiles/_CuraEngine.dir/src/infill/NoZigZagConnectorProcessor.cpp.obj"}, {"directory": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/build/Release", "command": "C:\\PROGRA~1\\MICROS~1\\2022\\COMMUN~1\\VC\\Tools\\MSVC\\1444~1.352\\bin\\Hostx64\\x64\\cl.exe  /nologo /TP -DAGRPC_BOOST_ASIO -DARCUS -DBOOST_ALL_NO_LIB -DBOOST_STACKTRACE_USE_NOOP -DBOOST_STACKTRACE_USE_WINDBG -DBOOST_STACKTRACE_USE_WINDBG_CACHED -DCARES_STATICLIB -DCURA_ENGINE_HASH=\\\"unknown\\\" -DCURA_ENGINE_VERSION=\\\"5.11.0-alpha.0\\\" -DENABLE_PLUGINS -DNOMINMAX -DSENTRY_ENVIRONMENT=\\\"development\\\" -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSTB_TEXTEDIT_KEYTYPE=unsigned -IC:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\include -IC:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\build\\Release -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\arcusee7666f716199\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\proto47a676cb9257b\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\zlibcfb9789dc2a53\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\spdlo2d4b540ab0f22\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\fmtdb20aad6e1bd4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\range0301bf3d76d5d\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\clippf8ddcd4a5d961\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbo34ee9aeb574f9\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbofe72fb50cf7c4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbo1b62b48748b82\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\rapidf7a3355ba53c4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\stb6342cecb318f5\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\boosta79d738920bc3\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\scrip4cfd014ad4660\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\nearg388d58da7a54c\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\curaebb245e64b5ac7\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\asio-c31dc2a380bf0\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\grpc8b6c73b4e5756\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\absei6a15444d60d47\\p\\include -external:W0 /DWIN32 /D_WINDOWS /EHsc /O2 /Ob2 /DNDEBUG -std:c++20 -MD /utf-8 /permissive- /FoCMakeFiles\\_CuraEngine.dir\\src\\infill\\ZigzagConnectorProcessor.cpp.obj /FdCMakeFiles\\_CuraEngine.dir\\_CuraEngine.pdb /FS -c C:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\src\\infill\\ZigzagConnectorProcessor.cpp", "file": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/src/infill/ZigzagConnectorProcessor.cpp", "output": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/build/Release/CMakeFiles/_CuraEngine.dir/src/infill/ZigzagConnectorProcessor.cpp.obj"}, {"directory": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/build/Release", "command": "C:\\PROGRA~1\\MICROS~1\\2022\\COMMUN~1\\VC\\Tools\\MSVC\\1444~1.352\\bin\\Hostx64\\x64\\cl.exe  /nologo /TP -DAGRPC_BOOST_ASIO -DARCUS -DBOOST_ALL_NO_LIB -DBOOST_STACKTRACE_USE_NOOP -DBOOST_STACKTRACE_USE_WINDBG -DBOOST_STACKTRACE_USE_WINDBG_CACHED -DCARES_STATICLIB -DCURA_ENGINE_HASH=\\\"unknown\\\" -DCURA_ENGINE_VERSION=\\\"5.11.0-alpha.0\\\" -DENABLE_PLUGINS -DNOMINMAX -DSENTRY_ENVIRONMENT=\\\"development\\\" -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSTB_TEXTEDIT_KEYTYPE=unsigned -IC:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\include -IC:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\build\\Release -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\arcusee7666f716199\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\proto47a676cb9257b\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\zlibcfb9789dc2a53\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\spdlo2d4b540ab0f22\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\fmtdb20aad6e1bd4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\range0301bf3d76d5d\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\clippf8ddcd4a5d961\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbo34ee9aeb574f9\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbofe72fb50cf7c4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbo1b62b48748b82\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\rapidf7a3355ba53c4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\stb6342cecb318f5\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\boosta79d738920bc3\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\scrip4cfd014ad4660\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\nearg388d58da7a54c\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\curaebb245e64b5ac7\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\asio-c31dc2a380bf0\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\grpc8b6c73b4e5756\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\absei6a15444d60d47\\p\\include -external:W0 /DWIN32 /D_WINDOWS /EHsc /O2 /Ob2 /DNDEBUG -std:c++20 -MD /utf-8 /permissive- /FoCMakeFiles\\_CuraEngine.dir\\src\\infill\\LightningDistanceField.cpp.obj /FdCMakeFiles\\_CuraEngine.dir\\_CuraEngine.pdb /FS -c C:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\src\\infill\\LightningDistanceField.cpp", "file": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/src/infill/LightningDistanceField.cpp", "output": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/build/Release/CMakeFiles/_CuraEngine.dir/src/infill/LightningDistanceField.cpp.obj"}, {"directory": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/build/Release", "command": "C:\\PROGRA~1\\MICROS~1\\2022\\COMMUN~1\\VC\\Tools\\MSVC\\1444~1.352\\bin\\Hostx64\\x64\\cl.exe  /nologo /TP -DAGRPC_BOOST_ASIO -DARCUS -DBOOST_ALL_NO_LIB -DBOOST_STACKTRACE_USE_NOOP -DBOOST_STACKTRACE_USE_WINDBG -DBOOST_STACKTRACE_USE_WINDBG_CACHED -DCARES_STATICLIB -DCURA_ENGINE_HASH=\\\"unknown\\\" -DCURA_ENGINE_VERSION=\\\"5.11.0-alpha.0\\\" -DENABLE_PLUGINS -DNOMINMAX -DSENTRY_ENVIRONMENT=\\\"development\\\" -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSTB_TEXTEDIT_KEYTYPE=unsigned -IC:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\include -IC:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\build\\Release -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\arcusee7666f716199\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\proto47a676cb9257b\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\zlibcfb9789dc2a53\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\spdlo2d4b540ab0f22\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\fmtdb20aad6e1bd4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\range0301bf3d76d5d\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\clippf8ddcd4a5d961\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbo34ee9aeb574f9\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbofe72fb50cf7c4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbo1b62b48748b82\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\rapidf7a3355ba53c4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\stb6342cecb318f5\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\boosta79d738920bc3\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\scrip4cfd014ad4660\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\nearg388d58da7a54c\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\curaebb245e64b5ac7\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\asio-c31dc2a380bf0\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\grpc8b6c73b4e5756\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\absei6a15444d60d47\\p\\include -external:W0 /DWIN32 /D_WINDOWS /EHsc /O2 /Ob2 /DNDEBUG -std:c++20 -MD /utf-8 /permissive- /FoCMakeFiles\\_CuraEngine.dir\\src\\infill\\LightningGenerator.cpp.obj /FdCMakeFiles\\_CuraEngine.dir\\_CuraEngine.pdb /FS -c C:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\src\\infill\\LightningGenerator.cpp", "file": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/src/infill/LightningGenerator.cpp", "output": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/build/Release/CMakeFiles/_CuraEngine.dir/src/infill/LightningGenerator.cpp.obj"}, {"directory": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/build/Release", "command": "C:\\PROGRA~1\\MICROS~1\\2022\\COMMUN~1\\VC\\Tools\\MSVC\\1444~1.352\\bin\\Hostx64\\x64\\cl.exe  /nologo /TP -DAGRPC_BOOST_ASIO -DARCUS -DBOOST_ALL_NO_LIB -DBOOST_STACKTRACE_USE_NOOP -DBOOST_STACKTRACE_USE_WINDBG -DBOOST_STACKTRACE_USE_WINDBG_CACHED -DCARES_STATICLIB -DCURA_ENGINE_HASH=\\\"unknown\\\" -DCURA_ENGINE_VERSION=\\\"5.11.0-alpha.0\\\" -DENABLE_PLUGINS -DNOMINMAX -DSENTRY_ENVIRONMENT=\\\"development\\\" -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSTB_TEXTEDIT_KEYTYPE=unsigned -IC:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\include -IC:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\build\\Release -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\arcusee7666f716199\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\proto47a676cb9257b\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\zlibcfb9789dc2a53\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\spdlo2d4b540ab0f22\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\fmtdb20aad6e1bd4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\range0301bf3d76d5d\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\clippf8ddcd4a5d961\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbo34ee9aeb574f9\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbofe72fb50cf7c4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbo1b62b48748b82\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\rapidf7a3355ba53c4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\stb6342cecb318f5\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\boosta79d738920bc3\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\scrip4cfd014ad4660\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\nearg388d58da7a54c\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\curaebb245e64b5ac7\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\asio-c31dc2a380bf0\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\grpc8b6c73b4e5756\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\absei6a15444d60d47\\p\\include -external:W0 /DWIN32 /D_WINDOWS /EHsc /O2 /Ob2 /DNDEBUG -std:c++20 -MD /utf-8 /permissive- /FoCMakeFiles\\_CuraEngine.dir\\src\\infill\\LightningLayer.cpp.obj /FdCMakeFiles\\_CuraEngine.dir\\_CuraEngine.pdb /FS -c C:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\src\\infill\\LightningLayer.cpp", "file": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/src/infill/LightningLayer.cpp", "output": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/build/Release/CMakeFiles/_CuraEngine.dir/src/infill/LightningLayer.cpp.obj"}, {"directory": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/build/Release", "command": "C:\\PROGRA~1\\MICROS~1\\2022\\COMMUN~1\\VC\\Tools\\MSVC\\1444~1.352\\bin\\Hostx64\\x64\\cl.exe  /nologo /TP -DAGRPC_BOOST_ASIO -DARCUS -DBOOST_ALL_NO_LIB -DBOOST_STACKTRACE_USE_NOOP -DBOOST_STACKTRACE_USE_WINDBG -DBOOST_STACKTRACE_USE_WINDBG_CACHED -DCARES_STATICLIB -DCURA_ENGINE_HASH=\\\"unknown\\\" -DCURA_ENGINE_VERSION=\\\"5.11.0-alpha.0\\\" -DENABLE_PLUGINS -DNOMINMAX -DSENTRY_ENVIRONMENT=\\\"development\\\" -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSTB_TEXTEDIT_KEYTYPE=unsigned -IC:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\include -IC:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\build\\Release -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\arcusee7666f716199\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\proto47a676cb9257b\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\zlibcfb9789dc2a53\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\spdlo2d4b540ab0f22\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\fmtdb20aad6e1bd4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\range0301bf3d76d5d\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\clippf8ddcd4a5d961\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbo34ee9aeb574f9\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbofe72fb50cf7c4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbo1b62b48748b82\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\rapidf7a3355ba53c4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\stb6342cecb318f5\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\boosta79d738920bc3\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\scrip4cfd014ad4660\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\nearg388d58da7a54c\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\curaebb245e64b5ac7\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\asio-c31dc2a380bf0\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\grpc8b6c73b4e5756\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\absei6a15444d60d47\\p\\include -external:W0 /DWIN32 /D_WINDOWS /EHsc /O2 /Ob2 /DNDEBUG -std:c++20 -MD /utf-8 /permissive- /FoCMakeFiles\\_CuraEngine.dir\\src\\infill\\LightningTreeNode.cpp.obj /FdCMakeFiles\\_CuraEngine.dir\\_CuraEngine.pdb /FS -c C:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\src\\infill\\LightningTreeNode.cpp", "file": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/src/infill/LightningTreeNode.cpp", "output": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/build/Release/CMakeFiles/_CuraEngine.dir/src/infill/LightningTreeNode.cpp.obj"}, {"directory": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/build/Release", "command": "C:\\PROGRA~1\\MICROS~1\\2022\\COMMUN~1\\VC\\Tools\\MSVC\\1444~1.352\\bin\\Hostx64\\x64\\cl.exe  /nologo /TP -DAGRPC_BOOST_ASIO -DARCUS -DBOOST_ALL_NO_LIB -DBOOST_STACKTRACE_USE_NOOP -DBOOST_STACKTRACE_USE_WINDBG -DBOOST_STACKTRACE_USE_WINDBG_CACHED -DCARES_STATICLIB -DCURA_ENGINE_HASH=\\\"unknown\\\" -DCURA_ENGINE_VERSION=\\\"5.11.0-alpha.0\\\" -DENABLE_PLUGINS -DNOMINMAX -DSENTRY_ENVIRONMENT=\\\"development\\\" -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSTB_TEXTEDIT_KEYTYPE=unsigned -IC:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\include -IC:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\build\\Release -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\arcusee7666f716199\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\proto47a676cb9257b\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\zlibcfb9789dc2a53\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\spdlo2d4b540ab0f22\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\fmtdb20aad6e1bd4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\range0301bf3d76d5d\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\clippf8ddcd4a5d961\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbo34ee9aeb574f9\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbofe72fb50cf7c4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbo1b62b48748b82\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\rapidf7a3355ba53c4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\stb6342cecb318f5\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\boosta79d738920bc3\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\scrip4cfd014ad4660\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\nearg388d58da7a54c\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\curaebb245e64b5ac7\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\asio-c31dc2a380bf0\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\grpc8b6c73b4e5756\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\absei6a15444d60d47\\p\\include -external:W0 /DWIN32 /D_WINDOWS /EHsc /O2 /Ob2 /DNDEBUG -std:c++20 -MD /utf-8 /permissive- /FoCMakeFiles\\_CuraEngine.dir\\src\\infill\\SierpinskiFill.cpp.obj /FdCMakeFiles\\_CuraEngine.dir\\_CuraEngine.pdb /FS -c C:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\src\\infill\\SierpinskiFill.cpp", "file": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/src/infill/SierpinskiFill.cpp", "output": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/build/Release/CMakeFiles/_CuraEngine.dir/src/infill/SierpinskiFill.cpp.obj"}, {"directory": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/build/Release", "command": "C:\\PROGRA~1\\MICROS~1\\2022\\COMMUN~1\\VC\\Tools\\MSVC\\1444~1.352\\bin\\Hostx64\\x64\\cl.exe  /nologo /TP -DAGRPC_BOOST_ASIO -DARCUS -DBOOST_ALL_NO_LIB -DBOOST_STACKTRACE_USE_NOOP -DBOOST_STACKTRACE_USE_WINDBG -DBOOST_STACKTRACE_USE_WINDBG_CACHED -DCARES_STATICLIB -DCURA_ENGINE_HASH=\\\"unknown\\\" -DCURA_ENGINE_VERSION=\\\"5.11.0-alpha.0\\\" -DENABLE_PLUGINS -DNOMINMAX -DSENTRY_ENVIRONMENT=\\\"development\\\" -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSTB_TEXTEDIT_KEYTYPE=unsigned -IC:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\include -IC:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\build\\Release -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\arcusee7666f716199\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\proto47a676cb9257b\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\zlibcfb9789dc2a53\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\spdlo2d4b540ab0f22\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\fmtdb20aad6e1bd4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\range0301bf3d76d5d\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\clippf8ddcd4a5d961\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbo34ee9aeb574f9\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbofe72fb50cf7c4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbo1b62b48748b82\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\rapidf7a3355ba53c4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\stb6342cecb318f5\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\boosta79d738920bc3\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\scrip4cfd014ad4660\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\nearg388d58da7a54c\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\curaebb245e64b5ac7\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\asio-c31dc2a380bf0\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\grpc8b6c73b4e5756\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\absei6a15444d60d47\\p\\include -external:W0 /DWIN32 /D_WINDOWS /EHsc /O2 /Ob2 /DNDEBUG -std:c++20 -MD /utf-8 /permissive- /FoCMakeFiles\\_CuraEngine.dir\\src\\infill\\SierpinskiFillProvider.cpp.obj /FdCMakeFiles\\_CuraEngine.dir\\_CuraEngine.pdb /FS -c C:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\src\\infill\\SierpinskiFillProvider.cpp", "file": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/src/infill/SierpinskiFillProvider.cpp", "output": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/build/Release/CMakeFiles/_CuraEngine.dir/src/infill/SierpinskiFillProvider.cpp.obj"}, {"directory": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/build/Release", "command": "C:\\PROGRA~1\\MICROS~1\\2022\\COMMUN~1\\VC\\Tools\\MSVC\\1444~1.352\\bin\\Hostx64\\x64\\cl.exe  /nologo /TP -DAGRPC_BOOST_ASIO -DARCUS -DBOOST_ALL_NO_LIB -DBOOST_STACKTRACE_USE_NOOP -DBOOST_STACKTRACE_USE_WINDBG -DBOOST_STACKTRACE_USE_WINDBG_CACHED -DCARES_STATICLIB -DCURA_ENGINE_HASH=\\\"unknown\\\" -DCURA_ENGINE_VERSION=\\\"5.11.0-alpha.0\\\" -DENABLE_PLUGINS -DNOMINMAX -DSENTRY_ENVIRONMENT=\\\"development\\\" -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSTB_TEXTEDIT_KEYTYPE=unsigned -IC:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\include -IC:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\build\\Release -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\arcusee7666f716199\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\proto47a676cb9257b\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\zlibcfb9789dc2a53\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\spdlo2d4b540ab0f22\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\fmtdb20aad6e1bd4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\range0301bf3d76d5d\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\clippf8ddcd4a5d961\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbo34ee9aeb574f9\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbofe72fb50cf7c4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbo1b62b48748b82\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\rapidf7a3355ba53c4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\stb6342cecb318f5\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\boosta79d738920bc3\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\scrip4cfd014ad4660\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\nearg388d58da7a54c\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\curaebb245e64b5ac7\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\asio-c31dc2a380bf0\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\grpc8b6c73b4e5756\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\absei6a15444d60d47\\p\\include -external:W0 /DWIN32 /D_WINDOWS /EHsc /O2 /Ob2 /DNDEBUG -std:c++20 -MD /utf-8 /permissive- /FoCMakeFiles\\_CuraEngine.dir\\src\\infill\\SubDivCube.cpp.obj /FdCMakeFiles\\_CuraEngine.dir\\_CuraEngine.pdb /FS -c C:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\src\\infill\\SubDivCube.cpp", "file": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/src/infill/SubDivCube.cpp", "output": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/build/Release/CMakeFiles/_CuraEngine.dir/src/infill/SubDivCube.cpp.obj"}, {"directory": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/build/Release", "command": "C:\\PROGRA~1\\MICROS~1\\2022\\COMMUN~1\\VC\\Tools\\MSVC\\1444~1.352\\bin\\Hostx64\\x64\\cl.exe  /nologo /TP -DAGRPC_BOOST_ASIO -DARCUS -DBOOST_ALL_NO_LIB -DBOOST_STACKTRACE_USE_NOOP -DBOOST_STACKTRACE_USE_WINDBG -DBOOST_STACKTRACE_USE_WINDBG_CACHED -DCARES_STATICLIB -DCURA_ENGINE_HASH=\\\"unknown\\\" -DCURA_ENGINE_VERSION=\\\"5.11.0-alpha.0\\\" -DENABLE_PLUGINS -DNOMINMAX -DSENTRY_ENVIRONMENT=\\\"development\\\" -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSTB_TEXTEDIT_KEYTYPE=unsigned -IC:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\include -IC:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\build\\Release -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\arcusee7666f716199\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\proto47a676cb9257b\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\zlibcfb9789dc2a53\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\spdlo2d4b540ab0f22\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\fmtdb20aad6e1bd4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\range0301bf3d76d5d\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\clippf8ddcd4a5d961\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbo34ee9aeb574f9\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbofe72fb50cf7c4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbo1b62b48748b82\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\rapidf7a3355ba53c4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\stb6342cecb318f5\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\boosta79d738920bc3\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\scrip4cfd014ad4660\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\nearg388d58da7a54c\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\curaebb245e64b5ac7\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\asio-c31dc2a380bf0\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\grpc8b6c73b4e5756\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\absei6a15444d60d47\\p\\include -external:W0 /DWIN32 /D_WINDOWS /EHsc /O2 /Ob2 /DNDEBUG -std:c++20 -MD /utf-8 /permissive- /FoCMakeFiles\\_CuraEngine.dir\\src\\infill\\GyroidInfill.cpp.obj /FdCMakeFiles\\_CuraEngine.dir\\_CuraEngine.pdb /FS -c C:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\src\\infill\\GyroidInfill.cpp", "file": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/src/infill/GyroidInfill.cpp", "output": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/build/Release/CMakeFiles/_CuraEngine.dir/src/infill/GyroidInfill.cpp.obj"}, {"directory": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/build/Release", "command": "C:\\PROGRA~1\\MICROS~1\\2022\\COMMUN~1\\VC\\Tools\\MSVC\\1444~1.352\\bin\\Hostx64\\x64\\cl.exe  /nologo /TP -DAGRPC_BOOST_ASIO -DARCUS -DBOOST_ALL_NO_LIB -DBOOST_STACKTRACE_USE_NOOP -DBOOST_STACKTRACE_USE_WINDBG -DBOOST_STACKTRACE_USE_WINDBG_CACHED -DCARES_STATICLIB -DCURA_ENGINE_HASH=\\\"unknown\\\" -DCURA_ENGINE_VERSION=\\\"5.11.0-alpha.0\\\" -DENABLE_PLUGINS -DNOMINMAX -DSENTRY_ENVIRONMENT=\\\"development\\\" -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSTB_TEXTEDIT_KEYTYPE=unsigned -IC:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\include -IC:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\build\\Release -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\arcusee7666f716199\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\proto47a676cb9257b\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\zlibcfb9789dc2a53\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\spdlo2d4b540ab0f22\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\fmtdb20aad6e1bd4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\range0301bf3d76d5d\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\clippf8ddcd4a5d961\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbo34ee9aeb574f9\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbofe72fb50cf7c4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbo1b62b48748b82\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\rapidf7a3355ba53c4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\stb6342cecb318f5\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\boosta79d738920bc3\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\scrip4cfd014ad4660\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\nearg388d58da7a54c\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\curaebb245e64b5ac7\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\asio-c31dc2a380bf0\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\grpc8b6c73b4e5756\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\absei6a15444d60d47\\p\\include -external:W0 /DWIN32 /D_WINDOWS /EHsc /O2 /Ob2 /DNDEBUG -std:c++20 -MD /utf-8 /permissive- /FoCMakeFiles\\_CuraEngine.dir\\src\\pathPlanning\\Comb.cpp.obj /FdCMakeFiles\\_CuraEngine.dir\\_CuraEngine.pdb /FS -c C:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\src\\pathPlanning\\Comb.cpp", "file": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/src/pathPlanning/Comb.cpp", "output": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/build/Release/CMakeFiles/_CuraEngine.dir/src/pathPlanning/Comb.cpp.obj"}, {"directory": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/build/Release", "command": "C:\\PROGRA~1\\MICROS~1\\2022\\COMMUN~1\\VC\\Tools\\MSVC\\1444~1.352\\bin\\Hostx64\\x64\\cl.exe  /nologo /TP -DAGRPC_BOOST_ASIO -DARCUS -DBOOST_ALL_NO_LIB -DBOOST_STACKTRACE_USE_NOOP -DBOOST_STACKTRACE_USE_WINDBG -DBOOST_STACKTRACE_USE_WINDBG_CACHED -DCARES_STATICLIB -DCURA_ENGINE_HASH=\\\"unknown\\\" -DCURA_ENGINE_VERSION=\\\"5.11.0-alpha.0\\\" -DENABLE_PLUGINS -DNOMINMAX -DSENTRY_ENVIRONMENT=\\\"development\\\" -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSTB_TEXTEDIT_KEYTYPE=unsigned -IC:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\include -IC:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\build\\Release -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\arcusee7666f716199\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\proto47a676cb9257b\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\zlibcfb9789dc2a53\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\spdlo2d4b540ab0f22\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\fmtdb20aad6e1bd4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\range0301bf3d76d5d\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\clippf8ddcd4a5d961\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbo34ee9aeb574f9\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbofe72fb50cf7c4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbo1b62b48748b82\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\rapidf7a3355ba53c4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\stb6342cecb318f5\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\boosta79d738920bc3\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\scrip4cfd014ad4660\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\nearg388d58da7a54c\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\curaebb245e64b5ac7\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\asio-c31dc2a380bf0\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\grpc8b6c73b4e5756\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\absei6a15444d60d47\\p\\include -external:W0 /DWIN32 /D_WINDOWS /EHsc /O2 /Ob2 /DNDEBUG -std:c++20 -MD /utf-8 /permissive- /FoCMakeFiles\\_CuraEngine.dir\\src\\pathPlanning\\GCodePath.cpp.obj /FdCMakeFiles\\_CuraEngine.dir\\_CuraEngine.pdb /FS -c C:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\src\\pathPlanning\\GCodePath.cpp", "file": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/src/pathPlanning/GCodePath.cpp", "output": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/build/Release/CMakeFiles/_CuraEngine.dir/src/pathPlanning/GCodePath.cpp.obj"}, {"directory": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/build/Release", "command": "C:\\PROGRA~1\\MICROS~1\\2022\\COMMUN~1\\VC\\Tools\\MSVC\\1444~1.352\\bin\\Hostx64\\x64\\cl.exe  /nologo /TP -DAGRPC_BOOST_ASIO -DARCUS -DBOOST_ALL_NO_LIB -DBOOST_STACKTRACE_USE_NOOP -DBOOST_STACKTRACE_USE_WINDBG -DBOOST_STACKTRACE_USE_WINDBG_CACHED -DCARES_STATICLIB -DCURA_ENGINE_HASH=\\\"unknown\\\" -DCURA_ENGINE_VERSION=\\\"5.11.0-alpha.0\\\" -DENABLE_PLUGINS -DNOMINMAX -DSENTRY_ENVIRONMENT=\\\"development\\\" -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSTB_TEXTEDIT_KEYTYPE=unsigned -IC:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\include -IC:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\build\\Release -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\arcusee7666f716199\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\proto47a676cb9257b\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\zlibcfb9789dc2a53\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\spdlo2d4b540ab0f22\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\fmtdb20aad6e1bd4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\range0301bf3d76d5d\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\clippf8ddcd4a5d961\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbo34ee9aeb574f9\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbofe72fb50cf7c4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbo1b62b48748b82\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\rapidf7a3355ba53c4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\stb6342cecb318f5\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\boosta79d738920bc3\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\scrip4cfd014ad4660\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\nearg388d58da7a54c\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\curaebb245e64b5ac7\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\asio-c31dc2a380bf0\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\grpc8b6c73b4e5756\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\absei6a15444d60d47\\p\\include -external:W0 /DWIN32 /D_WINDOWS /EHsc /O2 /Ob2 /DNDEBUG -std:c++20 -MD /utf-8 /permissive- /FoCMakeFiles\\_CuraEngine.dir\\src\\pathPlanning\\LinePolygonsCrossings.cpp.obj /FdCMakeFiles\\_CuraEngine.dir\\_CuraEngine.pdb /FS -c C:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\src\\pathPlanning\\LinePolygonsCrossings.cpp", "file": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/src/pathPlanning/LinePolygonsCrossings.cpp", "output": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/build/Release/CMakeFiles/_CuraEngine.dir/src/pathPlanning/LinePolygonsCrossings.cpp.obj"}, {"directory": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/build/Release", "command": "C:\\PROGRA~1\\MICROS~1\\2022\\COMMUN~1\\VC\\Tools\\MSVC\\1444~1.352\\bin\\Hostx64\\x64\\cl.exe  /nologo /TP -DAGRPC_BOOST_ASIO -DARCUS -DBOOST_ALL_NO_LIB -DBOOST_STACKTRACE_USE_NOOP -DBOOST_STACKTRACE_USE_WINDBG -DBOOST_STACKTRACE_USE_WINDBG_CACHED -DCARES_STATICLIB -DCURA_ENGINE_HASH=\\\"unknown\\\" -DCURA_ENGINE_VERSION=\\\"5.11.0-alpha.0\\\" -DENABLE_PLUGINS -DNOMINMAX -DSENTRY_ENVIRONMENT=\\\"development\\\" -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSTB_TEXTEDIT_KEYTYPE=unsigned -IC:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\include -IC:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\build\\Release -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\arcusee7666f716199\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\proto47a676cb9257b\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\zlibcfb9789dc2a53\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\spdlo2d4b540ab0f22\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\fmtdb20aad6e1bd4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\range0301bf3d76d5d\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\clippf8ddcd4a5d961\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbo34ee9aeb574f9\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbofe72fb50cf7c4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbo1b62b48748b82\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\rapidf7a3355ba53c4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\stb6342cecb318f5\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\boosta79d738920bc3\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\scrip4cfd014ad4660\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\nearg388d58da7a54c\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\curaebb245e64b5ac7\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\asio-c31dc2a380bf0\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\grpc8b6c73b4e5756\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\absei6a15444d60d47\\p\\include -external:W0 /DWIN32 /D_WINDOWS /EHsc /O2 /Ob2 /DNDEBUG -std:c++20 -MD /utf-8 /permissive- /FoCMakeFiles\\_CuraEngine.dir\\src\\pathPlanning\\NozzleTempInsert.cpp.obj /FdCMakeFiles\\_CuraEngine.dir\\_CuraEngine.pdb /FS -c C:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\src\\pathPlanning\\NozzleTempInsert.cpp", "file": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/src/pathPlanning/NozzleTempInsert.cpp", "output": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/build/Release/CMakeFiles/_CuraEngine.dir/src/pathPlanning/NozzleTempInsert.cpp.obj"}, {"directory": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/build/Release", "command": "C:\\PROGRA~1\\MICROS~1\\2022\\COMMUN~1\\VC\\Tools\\MSVC\\1444~1.352\\bin\\Hostx64\\x64\\cl.exe  /nologo /TP -DAGRPC_BOOST_ASIO -DARCUS -DBOOST_ALL_NO_LIB -DBOOST_STACKTRACE_USE_NOOP -DBOOST_STACKTRACE_USE_WINDBG -DBOOST_STACKTRACE_USE_WINDBG_CACHED -DCARES_STATICLIB -DCURA_ENGINE_HASH=\\\"unknown\\\" -DCURA_ENGINE_VERSION=\\\"5.11.0-alpha.0\\\" -DENABLE_PLUGINS -DNOMINMAX -DSENTRY_ENVIRONMENT=\\\"development\\\" -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSTB_TEXTEDIT_KEYTYPE=unsigned -IC:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\include -IC:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\build\\Release -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\arcusee7666f716199\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\proto47a676cb9257b\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\zlibcfb9789dc2a53\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\spdlo2d4b540ab0f22\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\fmtdb20aad6e1bd4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\range0301bf3d76d5d\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\clippf8ddcd4a5d961\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbo34ee9aeb574f9\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbofe72fb50cf7c4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbo1b62b48748b82\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\rapidf7a3355ba53c4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\stb6342cecb318f5\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\boosta79d738920bc3\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\scrip4cfd014ad4660\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\nearg388d58da7a54c\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\curaebb245e64b5ac7\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\asio-c31dc2a380bf0\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\grpc8b6c73b4e5756\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\absei6a15444d60d47\\p\\include -external:W0 /DWIN32 /D_WINDOWS /EHsc /O2 /Ob2 /DNDEBUG -std:c++20 -MD /utf-8 /permissive- /FoCMakeFiles\\_CuraEngine.dir\\src\\pathPlanning\\SpeedDerivatives.cpp.obj /FdCMakeFiles\\_CuraEngine.dir\\_CuraEngine.pdb /FS -c C:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\src\\pathPlanning\\SpeedDerivatives.cpp", "file": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/src/pathPlanning/SpeedDerivatives.cpp", "output": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/build/Release/CMakeFiles/_CuraEngine.dir/src/pathPlanning/SpeedDerivatives.cpp.obj"}, {"directory": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/build/Release", "command": "C:\\PROGRA~1\\MICROS~1\\2022\\COMMUN~1\\VC\\Tools\\MSVC\\1444~1.352\\bin\\Hostx64\\x64\\cl.exe  /nologo /TP -DAGRPC_BOOST_ASIO -DARCUS -DBOOST_ALL_NO_LIB -DBOOST_STACKTRACE_USE_NOOP -DBOOST_STACKTRACE_USE_WINDBG -DBOOST_STACKTRACE_USE_WINDBG_CACHED -DCARES_STATICLIB -DCURA_ENGINE_HASH=\\\"unknown\\\" -DCURA_ENGINE_VERSION=\\\"5.11.0-alpha.0\\\" -DENABLE_PLUGINS -DNOMINMAX -DSENTRY_ENVIRONMENT=\\\"development\\\" -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSTB_TEXTEDIT_KEYTYPE=unsigned -IC:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\include -IC:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\build\\Release -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\arcusee7666f716199\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\proto47a676cb9257b\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\zlibcfb9789dc2a53\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\spdlo2d4b540ab0f22\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\fmtdb20aad6e1bd4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\range0301bf3d76d5d\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\clippf8ddcd4a5d961\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbo34ee9aeb574f9\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbofe72fb50cf7c4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbo1b62b48748b82\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\rapidf7a3355ba53c4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\stb6342cecb318f5\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\boosta79d738920bc3\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\scrip4cfd014ad4660\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\nearg388d58da7a54c\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\curaebb245e64b5ac7\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\asio-c31dc2a380bf0\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\grpc8b6c73b4e5756\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\absei6a15444d60d47\\p\\include -external:W0 /DWIN32 /D_WINDOWS /EHsc /O2 /Ob2 /DNDEBUG -std:c++20 -MD /utf-8 /permissive- /FoCMakeFiles\\_CuraEngine.dir\\src\\plugins\\converters.cpp.obj /FdCMakeFiles\\_CuraEngine.dir\\_CuraEngine.pdb /FS -c C:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\src\\plugins\\converters.cpp", "file": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/src/plugins/converters.cpp", "output": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/build/Release/CMakeFiles/_CuraEngine.dir/src/plugins/converters.cpp.obj"}, {"directory": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/build/Release", "command": "C:\\PROGRA~1\\MICROS~1\\2022\\COMMUN~1\\VC\\Tools\\MSVC\\1444~1.352\\bin\\Hostx64\\x64\\cl.exe  /nologo /TP -DAGRPC_BOOST_ASIO -DARCUS -DBOOST_ALL_NO_LIB -DBOOST_STACKTRACE_USE_NOOP -DBOOST_STACKTRACE_USE_WINDBG -DBOOST_STACKTRACE_USE_WINDBG_CACHED -DCARES_STATICLIB -DCURA_ENGINE_HASH=\\\"unknown\\\" -DCURA_ENGINE_VERSION=\\\"5.11.0-alpha.0\\\" -DENABLE_PLUGINS -DNOMINMAX -DSENTRY_ENVIRONMENT=\\\"development\\\" -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSTB_TEXTEDIT_KEYTYPE=unsigned -IC:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\include -IC:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\build\\Release -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\arcusee7666f716199\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\proto47a676cb9257b\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\zlibcfb9789dc2a53\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\spdlo2d4b540ab0f22\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\fmtdb20aad6e1bd4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\range0301bf3d76d5d\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\clippf8ddcd4a5d961\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbo34ee9aeb574f9\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbofe72fb50cf7c4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbo1b62b48748b82\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\rapidf7a3355ba53c4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\stb6342cecb318f5\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\boosta79d738920bc3\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\scrip4cfd014ad4660\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\nearg388d58da7a54c\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\curaebb245e64b5ac7\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\asio-c31dc2a380bf0\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\grpc8b6c73b4e5756\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\absei6a15444d60d47\\p\\include -external:W0 /DWIN32 /D_WINDOWS /EHsc /O2 /Ob2 /DNDEBUG -std:c++20 -MD /utf-8 /permissive- /FoCMakeFiles\\_CuraEngine.dir\\src\\progress\\Progress.cpp.obj /FdCMakeFiles\\_CuraEngine.dir\\_CuraEngine.pdb /FS -c C:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\src\\progress\\Progress.cpp", "file": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/src/progress/Progress.cpp", "output": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/build/Release/CMakeFiles/_CuraEngine.dir/src/progress/Progress.cpp.obj"}, {"directory": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/build/Release", "command": "C:\\PROGRA~1\\MICROS~1\\2022\\COMMUN~1\\VC\\Tools\\MSVC\\1444~1.352\\bin\\Hostx64\\x64\\cl.exe  /nologo /TP -DAGRPC_BOOST_ASIO -DARCUS -DBOOST_ALL_NO_LIB -DBOOST_STACKTRACE_USE_NOOP -DBOOST_STACKTRACE_USE_WINDBG -DBOOST_STACKTRACE_USE_WINDBG_CACHED -DCARES_STATICLIB -DCURA_ENGINE_HASH=\\\"unknown\\\" -DCURA_ENGINE_VERSION=\\\"5.11.0-alpha.0\\\" -DENABLE_PLUGINS -DNOMINMAX -DSENTRY_ENVIRONMENT=\\\"development\\\" -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSTB_TEXTEDIT_KEYTYPE=unsigned -IC:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\include -IC:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\build\\Release -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\arcusee7666f716199\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\proto47a676cb9257b\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\zlibcfb9789dc2a53\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\spdlo2d4b540ab0f22\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\fmtdb20aad6e1bd4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\range0301bf3d76d5d\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\clippf8ddcd4a5d961\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbo34ee9aeb574f9\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbofe72fb50cf7c4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbo1b62b48748b82\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\rapidf7a3355ba53c4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\stb6342cecb318f5\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\boosta79d738920bc3\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\scrip4cfd014ad4660\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\nearg388d58da7a54c\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\curaebb245e64b5ac7\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\asio-c31dc2a380bf0\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\grpc8b6c73b4e5756\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\absei6a15444d60d47\\p\\include -external:W0 /DWIN32 /D_WINDOWS /EHsc /O2 /Ob2 /DNDEBUG -std:c++20 -MD /utf-8 /permissive- /FoCMakeFiles\\_CuraEngine.dir\\src\\progress\\ProgressStageEstimator.cpp.obj /FdCMakeFiles\\_CuraEngine.dir\\_CuraEngine.pdb /FS -c C:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\src\\progress\\ProgressStageEstimator.cpp", "file": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/src/progress/ProgressStageEstimator.cpp", "output": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/build/Release/CMakeFiles/_CuraEngine.dir/src/progress/ProgressStageEstimator.cpp.obj"}, {"directory": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/build/Release", "command": "C:\\PROGRA~1\\MICROS~1\\2022\\COMMUN~1\\VC\\Tools\\MSVC\\1444~1.352\\bin\\Hostx64\\x64\\cl.exe  /nologo /TP -DAGRPC_BOOST_ASIO -DARCUS -DBOOST_ALL_NO_LIB -DBOOST_STACKTRACE_USE_NOOP -DBOOST_STACKTRACE_USE_WINDBG -DBOOST_STACKTRACE_USE_WINDBG_CACHED -DCARES_STATICLIB -DCURA_ENGINE_HASH=\\\"unknown\\\" -DCURA_ENGINE_VERSION=\\\"5.11.0-alpha.0\\\" -DENABLE_PLUGINS -DNOMINMAX -DSENTRY_ENVIRONMENT=\\\"development\\\" -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSTB_TEXTEDIT_KEYTYPE=unsigned -IC:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\include -IC:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\build\\Release -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\arcusee7666f716199\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\proto47a676cb9257b\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\zlibcfb9789dc2a53\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\spdlo2d4b540ab0f22\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\fmtdb20aad6e1bd4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\range0301bf3d76d5d\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\clippf8ddcd4a5d961\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbo34ee9aeb574f9\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbofe72fb50cf7c4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbo1b62b48748b82\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\rapidf7a3355ba53c4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\stb6342cecb318f5\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\boosta79d738920bc3\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\scrip4cfd014ad4660\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\nearg388d58da7a54c\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\curaebb245e64b5ac7\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\asio-c31dc2a380bf0\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\grpc8b6c73b4e5756\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\absei6a15444d60d47\\p\\include -external:W0 /DWIN32 /D_WINDOWS /EHsc /O2 /Ob2 /DNDEBUG -std:c++20 -MD /utf-8 /permissive- /FoCMakeFiles\\_CuraEngine.dir\\src\\settings\\AdaptiveLayerHeights.cpp.obj /FdCMakeFiles\\_CuraEngine.dir\\_CuraEngine.pdb /FS -c C:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\src\\settings\\AdaptiveLayerHeights.cpp", "file": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/src/settings/AdaptiveLayerHeights.cpp", "output": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/build/Release/CMakeFiles/_CuraEngine.dir/src/settings/AdaptiveLayerHeights.cpp.obj"}, {"directory": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/build/Release", "command": "C:\\PROGRA~1\\MICROS~1\\2022\\COMMUN~1\\VC\\Tools\\MSVC\\1444~1.352\\bin\\Hostx64\\x64\\cl.exe  /nologo /TP -DAGRPC_BOOST_ASIO -DARCUS -DBOOST_ALL_NO_LIB -DBOOST_STACKTRACE_USE_NOOP -DBOOST_STACKTRACE_USE_WINDBG -DBOOST_STACKTRACE_USE_WINDBG_CACHED -DCARES_STATICLIB -DCURA_ENGINE_HASH=\\\"unknown\\\" -DCURA_ENGINE_VERSION=\\\"5.11.0-alpha.0\\\" -DENABLE_PLUGINS -DNOMINMAX -DSENTRY_ENVIRONMENT=\\\"development\\\" -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSTB_TEXTEDIT_KEYTYPE=unsigned -IC:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\include -IC:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\build\\Release -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\arcusee7666f716199\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\proto47a676cb9257b\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\zlibcfb9789dc2a53\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\spdlo2d4b540ab0f22\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\fmtdb20aad6e1bd4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\range0301bf3d76d5d\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\clippf8ddcd4a5d961\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbo34ee9aeb574f9\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbofe72fb50cf7c4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbo1b62b48748b82\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\rapidf7a3355ba53c4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\stb6342cecb318f5\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\boosta79d738920bc3\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\scrip4cfd014ad4660\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\nearg388d58da7a54c\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\curaebb245e64b5ac7\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\asio-c31dc2a380bf0\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\grpc8b6c73b4e5756\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\absei6a15444d60d47\\p\\include -external:W0 /DWIN32 /D_WINDOWS /EHsc /O2 /Ob2 /DNDEBUG -std:c++20 -MD /utf-8 /permissive- /FoCMakeFiles\\_CuraEngine.dir\\src\\settings\\FlowTempGraph.cpp.obj /FdCMakeFiles\\_CuraEngine.dir\\_CuraEngine.pdb /FS -c C:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\src\\settings\\FlowTempGraph.cpp", "file": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/src/settings/FlowTempGraph.cpp", "output": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/build/Release/CMakeFiles/_CuraEngine.dir/src/settings/FlowTempGraph.cpp.obj"}, {"directory": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/build/Release", "command": "C:\\PROGRA~1\\MICROS~1\\2022\\COMMUN~1\\VC\\Tools\\MSVC\\1444~1.352\\bin\\Hostx64\\x64\\cl.exe  /nologo /TP -DAGRPC_BOOST_ASIO -DARCUS -DBOOST_ALL_NO_LIB -DBOOST_STACKTRACE_USE_NOOP -DBOOST_STACKTRACE_USE_WINDBG -DBOOST_STACKTRACE_USE_WINDBG_CACHED -DCARES_STATICLIB -DCURA_ENGINE_HASH=\\\"unknown\\\" -DCURA_ENGINE_VERSION=\\\"5.11.0-alpha.0\\\" -DENABLE_PLUGINS -DNOMINMAX -DSENTRY_ENVIRONMENT=\\\"development\\\" -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSTB_TEXTEDIT_KEYTYPE=unsigned -IC:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\include -IC:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\build\\Release -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\arcusee7666f716199\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\proto47a676cb9257b\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\zlibcfb9789dc2a53\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\spdlo2d4b540ab0f22\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\fmtdb20aad6e1bd4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\range0301bf3d76d5d\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\clippf8ddcd4a5d961\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbo34ee9aeb574f9\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbofe72fb50cf7c4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbo1b62b48748b82\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\rapidf7a3355ba53c4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\stb6342cecb318f5\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\boosta79d738920bc3\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\scrip4cfd014ad4660\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\nearg388d58da7a54c\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\curaebb245e64b5ac7\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\asio-c31dc2a380bf0\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\grpc8b6c73b4e5756\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\absei6a15444d60d47\\p\\include -external:W0 /DWIN32 /D_WINDOWS /EHsc /O2 /Ob2 /DNDEBUG -std:c++20 -MD /utf-8 /permissive- /FoCMakeFiles\\_CuraEngine.dir\\src\\settings\\MeshPathConfigs.cpp.obj /FdCMakeFiles\\_CuraEngine.dir\\_CuraEngine.pdb /FS -c C:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\src\\settings\\MeshPathConfigs.cpp", "file": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/src/settings/MeshPathConfigs.cpp", "output": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/build/Release/CMakeFiles/_CuraEngine.dir/src/settings/MeshPathConfigs.cpp.obj"}, {"directory": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/build/Release", "command": "C:\\PROGRA~1\\MICROS~1\\2022\\COMMUN~1\\VC\\Tools\\MSVC\\1444~1.352\\bin\\Hostx64\\x64\\cl.exe  /nologo /TP -DAGRPC_BOOST_ASIO -DARCUS -DBOOST_ALL_NO_LIB -DBOOST_STACKTRACE_USE_NOOP -DBOOST_STACKTRACE_USE_WINDBG -DBOOST_STACKTRACE_USE_WINDBG_CACHED -DCARES_STATICLIB -DCURA_ENGINE_HASH=\\\"unknown\\\" -DCURA_ENGINE_VERSION=\\\"5.11.0-alpha.0\\\" -DENABLE_PLUGINS -DNOMINMAX -DSENTRY_ENVIRONMENT=\\\"development\\\" -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSTB_TEXTEDIT_KEYTYPE=unsigned -IC:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\include -IC:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\build\\Release -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\arcusee7666f716199\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\proto47a676cb9257b\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\zlibcfb9789dc2a53\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\spdlo2d4b540ab0f22\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\fmtdb20aad6e1bd4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\range0301bf3d76d5d\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\clippf8ddcd4a5d961\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbo34ee9aeb574f9\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbofe72fb50cf7c4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbo1b62b48748b82\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\rapidf7a3355ba53c4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\stb6342cecb318f5\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\boosta79d738920bc3\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\scrip4cfd014ad4660\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\nearg388d58da7a54c\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\curaebb245e64b5ac7\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\asio-c31dc2a380bf0\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\grpc8b6c73b4e5756\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\absei6a15444d60d47\\p\\include -external:W0 /DWIN32 /D_WINDOWS /EHsc /O2 /Ob2 /DNDEBUG -std:c++20 -MD /utf-8 /permissive- /FoCMakeFiles\\_CuraEngine.dir\\src\\settings\\PathConfigStorage.cpp.obj /FdCMakeFiles\\_CuraEngine.dir\\_CuraEngine.pdb /FS -c C:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\src\\settings\\PathConfigStorage.cpp", "file": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/src/settings/PathConfigStorage.cpp", "output": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/build/Release/CMakeFiles/_CuraEngine.dir/src/settings/PathConfigStorage.cpp.obj"}, {"directory": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/build/Release", "command": "C:\\PROGRA~1\\MICROS~1\\2022\\COMMUN~1\\VC\\Tools\\MSVC\\1444~1.352\\bin\\Hostx64\\x64\\cl.exe  /nologo /TP -DAGRPC_BOOST_ASIO -DARCUS -DBOOST_ALL_NO_LIB -DBOOST_STACKTRACE_USE_NOOP -DBOOST_STACKTRACE_USE_WINDBG -DBOOST_STACKTRACE_USE_WINDBG_CACHED -DCARES_STATICLIB -DCURA_ENGINE_HASH=\\\"unknown\\\" -DCURA_ENGINE_VERSION=\\\"5.11.0-alpha.0\\\" -DENABLE_PLUGINS -DNOMINMAX -DSENTRY_ENVIRONMENT=\\\"development\\\" -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSTB_TEXTEDIT_KEYTYPE=unsigned -IC:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\include -IC:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\build\\Release -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\arcusee7666f716199\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\proto47a676cb9257b\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\zlibcfb9789dc2a53\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\spdlo2d4b540ab0f22\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\fmtdb20aad6e1bd4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\range0301bf3d76d5d\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\clippf8ddcd4a5d961\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbo34ee9aeb574f9\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbofe72fb50cf7c4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbo1b62b48748b82\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\rapidf7a3355ba53c4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\stb6342cecb318f5\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\boosta79d738920bc3\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\scrip4cfd014ad4660\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\nearg388d58da7a54c\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\curaebb245e64b5ac7\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\asio-c31dc2a380bf0\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\grpc8b6c73b4e5756\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\absei6a15444d60d47\\p\\include -external:W0 /DWIN32 /D_WINDOWS /EHsc /O2 /Ob2 /DNDEBUG -std:c++20 -MD /utf-8 /permissive- /FoCMakeFiles\\_CuraEngine.dir\\src\\settings\\Settings.cpp.obj /FdCMakeFiles\\_CuraEngine.dir\\_CuraEngine.pdb /FS -c C:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\src\\settings\\Settings.cpp", "file": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/src/settings/Settings.cpp", "output": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/build/Release/CMakeFiles/_CuraEngine.dir/src/settings/Settings.cpp.obj"}, {"directory": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/build/Release", "command": "C:\\PROGRA~1\\MICROS~1\\2022\\COMMUN~1\\VC\\Tools\\MSVC\\1444~1.352\\bin\\Hostx64\\x64\\cl.exe  /nologo /TP -DAGRPC_BOOST_ASIO -DARCUS -DBOOST_ALL_NO_LIB -DBOOST_STACKTRACE_USE_NOOP -DBOOST_STACKTRACE_USE_WINDBG -DBOOST_STACKTRACE_USE_WINDBG_CACHED -DCARES_STATICLIB -DCURA_ENGINE_HASH=\\\"unknown\\\" -DCURA_ENGINE_VERSION=\\\"5.11.0-alpha.0\\\" -DENABLE_PLUGINS -DNOMINMAX -DSENTRY_ENVIRONMENT=\\\"development\\\" -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSTB_TEXTEDIT_KEYTYPE=unsigned -IC:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\include -IC:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\build\\Release -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\arcusee7666f716199\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\proto47a676cb9257b\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\zlibcfb9789dc2a53\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\spdlo2d4b540ab0f22\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\fmtdb20aad6e1bd4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\range0301bf3d76d5d\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\clippf8ddcd4a5d961\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbo34ee9aeb574f9\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbofe72fb50cf7c4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbo1b62b48748b82\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\rapidf7a3355ba53c4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\stb6342cecb318f5\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\boosta79d738920bc3\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\scrip4cfd014ad4660\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\nearg388d58da7a54c\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\curaebb245e64b5ac7\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\asio-c31dc2a380bf0\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\grpc8b6c73b4e5756\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\absei6a15444d60d47\\p\\include -external:W0 /DWIN32 /D_WINDOWS /EHsc /O2 /Ob2 /DNDEBUG -std:c++20 -MD /utf-8 /permissive- /FoCMakeFiles\\_CuraEngine.dir\\src\\settings\\ZSeamConfig.cpp.obj /FdCMakeFiles\\_CuraEngine.dir\\_CuraEngine.pdb /FS -c C:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\src\\settings\\ZSeamConfig.cpp", "file": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/src/settings/ZSeamConfig.cpp", "output": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/build/Release/CMakeFiles/_CuraEngine.dir/src/settings/ZSeamConfig.cpp.obj"}, {"directory": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/build/Release", "command": "C:\\PROGRA~1\\MICROS~1\\2022\\COMMUN~1\\VC\\Tools\\MSVC\\1444~1.352\\bin\\Hostx64\\x64\\cl.exe  /nologo /TP -DAGRPC_BOOST_ASIO -DARCUS -DBOOST_ALL_NO_LIB -DBOOST_STACKTRACE_USE_NOOP -DBOOST_STACKTRACE_USE_WINDBG -DBOOST_STACKTRACE_USE_WINDBG_CACHED -DCARES_STATICLIB -DCURA_ENGINE_HASH=\\\"unknown\\\" -DCURA_ENGINE_VERSION=\\\"5.11.0-alpha.0\\\" -DENABLE_PLUGINS -DNOMINMAX -DSENTRY_ENVIRONMENT=\\\"development\\\" -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSTB_TEXTEDIT_KEYTYPE=unsigned -IC:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\include -IC:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\build\\Release -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\arcusee7666f716199\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\proto47a676cb9257b\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\zlibcfb9789dc2a53\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\spdlo2d4b540ab0f22\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\fmtdb20aad6e1bd4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\range0301bf3d76d5d\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\clippf8ddcd4a5d961\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbo34ee9aeb574f9\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbofe72fb50cf7c4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbo1b62b48748b82\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\rapidf7a3355ba53c4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\stb6342cecb318f5\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\boosta79d738920bc3\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\scrip4cfd014ad4660\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\nearg388d58da7a54c\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\curaebb245e64b5ac7\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\asio-c31dc2a380bf0\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\grpc8b6c73b4e5756\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\absei6a15444d60d47\\p\\include -external:W0 /DWIN32 /D_WINDOWS /EHsc /O2 /Ob2 /DNDEBUG -std:c++20 -MD /utf-8 /permissive- /FoCMakeFiles\\_CuraEngine.dir\\src\\utils\\AABB.cpp.obj /FdCMakeFiles\\_CuraEngine.dir\\_CuraEngine.pdb /FS -c C:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\src\\utils\\AABB.cpp", "file": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/src/utils/AABB.cpp", "output": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/build/Release/CMakeFiles/_CuraEngine.dir/src/utils/AABB.cpp.obj"}, {"directory": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/build/Release", "command": "C:\\PROGRA~1\\MICROS~1\\2022\\COMMUN~1\\VC\\Tools\\MSVC\\1444~1.352\\bin\\Hostx64\\x64\\cl.exe  /nologo /TP -DAGRPC_BOOST_ASIO -DARCUS -DBOOST_ALL_NO_LIB -DBOOST_STACKTRACE_USE_NOOP -DBOOST_STACKTRACE_USE_WINDBG -DBOOST_STACKTRACE_USE_WINDBG_CACHED -DCARES_STATICLIB -DCURA_ENGINE_HASH=\\\"unknown\\\" -DCURA_ENGINE_VERSION=\\\"5.11.0-alpha.0\\\" -DENABLE_PLUGINS -DNOMINMAX -DSENTRY_ENVIRONMENT=\\\"development\\\" -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSTB_TEXTEDIT_KEYTYPE=unsigned -IC:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\include -IC:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\build\\Release -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\arcusee7666f716199\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\proto47a676cb9257b\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\zlibcfb9789dc2a53\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\spdlo2d4b540ab0f22\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\fmtdb20aad6e1bd4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\range0301bf3d76d5d\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\clippf8ddcd4a5d961\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbo34ee9aeb574f9\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbofe72fb50cf7c4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbo1b62b48748b82\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\rapidf7a3355ba53c4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\stb6342cecb318f5\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\boosta79d738920bc3\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\scrip4cfd014ad4660\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\nearg388d58da7a54c\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\curaebb245e64b5ac7\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\asio-c31dc2a380bf0\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\grpc8b6c73b4e5756\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\absei6a15444d60d47\\p\\include -external:W0 /DWIN32 /D_WINDOWS /EHsc /O2 /Ob2 /DNDEBUG -std:c++20 -MD /utf-8 /permissive- /FoCMakeFiles\\_CuraEngine.dir\\src\\utils\\AABB3D.cpp.obj /FdCMakeFiles\\_CuraEngine.dir\\_CuraEngine.pdb /FS -c C:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\src\\utils\\AABB3D.cpp", "file": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/src/utils/AABB3D.cpp", "output": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/build/Release/CMakeFiles/_CuraEngine.dir/src/utils/AABB3D.cpp.obj"}, {"directory": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/build/Release", "command": "C:\\PROGRA~1\\MICROS~1\\2022\\COMMUN~1\\VC\\Tools\\MSVC\\1444~1.352\\bin\\Hostx64\\x64\\cl.exe  /nologo /TP -DAGRPC_BOOST_ASIO -DARCUS -DBOOST_ALL_NO_LIB -DBOOST_STACKTRACE_USE_NOOP -DBOOST_STACKTRACE_USE_WINDBG -DBOOST_STACKTRACE_USE_WINDBG_CACHED -DCARES_STATICLIB -DCURA_ENGINE_HASH=\\\"unknown\\\" -DCURA_ENGINE_VERSION=\\\"5.11.0-alpha.0\\\" -DENABLE_PLUGINS -DNOMINMAX -DSENTRY_ENVIRONMENT=\\\"development\\\" -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSTB_TEXTEDIT_KEYTYPE=unsigned -IC:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\include -IC:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\build\\Release -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\arcusee7666f716199\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\proto47a676cb9257b\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\zlibcfb9789dc2a53\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\spdlo2d4b540ab0f22\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\fmtdb20aad6e1bd4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\range0301bf3d76d5d\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\clippf8ddcd4a5d961\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbo34ee9aeb574f9\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbofe72fb50cf7c4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbo1b62b48748b82\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\rapidf7a3355ba53c4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\stb6342cecb318f5\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\boosta79d738920bc3\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\scrip4cfd014ad4660\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\nearg388d58da7a54c\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\curaebb245e64b5ac7\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\asio-c31dc2a380bf0\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\grpc8b6c73b4e5756\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\absei6a15444d60d47\\p\\include -external:W0 /DWIN32 /D_WINDOWS /EHsc /O2 /Ob2 /DNDEBUG -std:c++20 -MD /utf-8 /permissive- /FoCMakeFiles\\_CuraEngine.dir\\src\\utils\\channel.cpp.obj /FdCMakeFiles\\_CuraEngine.dir\\_CuraEngine.pdb /FS -c C:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\src\\utils\\channel.cpp", "file": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/src/utils/channel.cpp", "output": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/build/Release/CMakeFiles/_CuraEngine.dir/src/utils/channel.cpp.obj"}, {"directory": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/build/Release", "command": "C:\\PROGRA~1\\MICROS~1\\2022\\COMMUN~1\\VC\\Tools\\MSVC\\1444~1.352\\bin\\Hostx64\\x64\\cl.exe  /nologo /TP -DAGRPC_BOOST_ASIO -DARCUS -DBOOST_ALL_NO_LIB -DBOOST_STACKTRACE_USE_NOOP -DBOOST_STACKTRACE_USE_WINDBG -DBOOST_STACKTRACE_USE_WINDBG_CACHED -DCARES_STATICLIB -DCURA_ENGINE_HASH=\\\"unknown\\\" -DCURA_ENGINE_VERSION=\\\"5.11.0-alpha.0\\\" -DENABLE_PLUGINS -DNOMINMAX -DSENTRY_ENVIRONMENT=\\\"development\\\" -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSTB_TEXTEDIT_KEYTYPE=unsigned -IC:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\include -IC:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\build\\Release -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\arcusee7666f716199\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\proto47a676cb9257b\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\zlibcfb9789dc2a53\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\spdlo2d4b540ab0f22\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\fmtdb20aad6e1bd4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\range0301bf3d76d5d\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\clippf8ddcd4a5d961\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbo34ee9aeb574f9\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbofe72fb50cf7c4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbo1b62b48748b82\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\rapidf7a3355ba53c4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\stb6342cecb318f5\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\boosta79d738920bc3\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\scrip4cfd014ad4660\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\nearg388d58da7a54c\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\curaebb245e64b5ac7\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\asio-c31dc2a380bf0\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\grpc8b6c73b4e5756\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\absei6a15444d60d47\\p\\include -external:W0 /DWIN32 /D_WINDOWS /EHsc /O2 /Ob2 /DNDEBUG -std:c++20 -MD /utf-8 /permissive- /FoCMakeFiles\\_CuraEngine.dir\\src\\utils\\Date.cpp.obj /FdCMakeFiles\\_CuraEngine.dir\\_CuraEngine.pdb /FS -c C:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\src\\utils\\Date.cpp", "file": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/src/utils/Date.cpp", "output": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/build/Release/CMakeFiles/_CuraEngine.dir/src/utils/Date.cpp.obj"}, {"directory": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/build/Release", "command": "C:\\PROGRA~1\\MICROS~1\\2022\\COMMUN~1\\VC\\Tools\\MSVC\\1444~1.352\\bin\\Hostx64\\x64\\cl.exe  /nologo /TP -DAGRPC_BOOST_ASIO -DARCUS -DBOOST_ALL_NO_LIB -DBOOST_STACKTRACE_USE_NOOP -DBOOST_STACKTRACE_USE_WINDBG -DBOOST_STACKTRACE_USE_WINDBG_CACHED -DCARES_STATICLIB -DCURA_ENGINE_HASH=\\\"unknown\\\" -DCURA_ENGINE_VERSION=\\\"5.11.0-alpha.0\\\" -DENABLE_PLUGINS -DNOMINMAX -DSENTRY_ENVIRONMENT=\\\"development\\\" -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSTB_TEXTEDIT_KEYTYPE=unsigned -IC:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\include -IC:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\build\\Release -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\arcusee7666f716199\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\proto47a676cb9257b\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\zlibcfb9789dc2a53\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\spdlo2d4b540ab0f22\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\fmtdb20aad6e1bd4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\range0301bf3d76d5d\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\clippf8ddcd4a5d961\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbo34ee9aeb574f9\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbofe72fb50cf7c4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbo1b62b48748b82\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\rapidf7a3355ba53c4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\stb6342cecb318f5\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\boosta79d738920bc3\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\scrip4cfd014ad4660\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\nearg388d58da7a54c\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\curaebb245e64b5ac7\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\asio-c31dc2a380bf0\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\grpc8b6c73b4e5756\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\absei6a15444d60d47\\p\\include -external:W0 /DWIN32 /D_WINDOWS /EHsc /O2 /Ob2 /DNDEBUG -std:c++20 -MD /utf-8 /permissive- /FoCMakeFiles\\_CuraEngine.dir\\src\\utils\\ExtrusionJunction.cpp.obj /FdCMakeFiles\\_CuraEngine.dir\\_CuraEngine.pdb /FS -c C:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\src\\utils\\ExtrusionJunction.cpp", "file": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/src/utils/ExtrusionJunction.cpp", "output": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/build/Release/CMakeFiles/_CuraEngine.dir/src/utils/ExtrusionJunction.cpp.obj"}, {"directory": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/build/Release", "command": "C:\\PROGRA~1\\MICROS~1\\2022\\COMMUN~1\\VC\\Tools\\MSVC\\1444~1.352\\bin\\Hostx64\\x64\\cl.exe  /nologo /TP -DAGRPC_BOOST_ASIO -DARCUS -DBOOST_ALL_NO_LIB -DBOOST_STACKTRACE_USE_NOOP -DBOOST_STACKTRACE_USE_WINDBG -DBOOST_STACKTRACE_USE_WINDBG_CACHED -DCARES_STATICLIB -DCURA_ENGINE_HASH=\\\"unknown\\\" -DCURA_ENGINE_VERSION=\\\"5.11.0-alpha.0\\\" -DENABLE_PLUGINS -DNOMINMAX -DSENTRY_ENVIRONMENT=\\\"development\\\" -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSTB_TEXTEDIT_KEYTYPE=unsigned -IC:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\include -IC:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\build\\Release -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\arcusee7666f716199\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\proto47a676cb9257b\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\zlibcfb9789dc2a53\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\spdlo2d4b540ab0f22\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\fmtdb20aad6e1bd4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\range0301bf3d76d5d\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\clippf8ddcd4a5d961\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbo34ee9aeb574f9\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbofe72fb50cf7c4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbo1b62b48748b82\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\rapidf7a3355ba53c4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\stb6342cecb318f5\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\boosta79d738920bc3\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\scrip4cfd014ad4660\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\nearg388d58da7a54c\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\curaebb245e64b5ac7\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\asio-c31dc2a380bf0\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\grpc8b6c73b4e5756\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\absei6a15444d60d47\\p\\include -external:W0 /DWIN32 /D_WINDOWS /EHsc /O2 /Ob2 /DNDEBUG -std:c++20 -MD /utf-8 /permissive- /FoCMakeFiles\\_CuraEngine.dir\\src\\utils\\ExtrusionLine.cpp.obj /FdCMakeFiles\\_CuraEngine.dir\\_CuraEngine.pdb /FS -c C:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\src\\utils\\ExtrusionLine.cpp", "file": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/src/utils/ExtrusionLine.cpp", "output": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/build/Release/CMakeFiles/_CuraEngine.dir/src/utils/ExtrusionLine.cpp.obj"}, {"directory": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/build/Release", "command": "C:\\PROGRA~1\\MICROS~1\\2022\\COMMUN~1\\VC\\Tools\\MSVC\\1444~1.352\\bin\\Hostx64\\x64\\cl.exe  /nologo /TP -DAGRPC_BOOST_ASIO -DARCUS -DBOOST_ALL_NO_LIB -DBOOST_STACKTRACE_USE_NOOP -DBOOST_STACKTRACE_USE_WINDBG -DBOOST_STACKTRACE_USE_WINDBG_CACHED -DCARES_STATICLIB -DCURA_ENGINE_HASH=\\\"unknown\\\" -DCURA_ENGINE_VERSION=\\\"5.11.0-alpha.0\\\" -DENABLE_PLUGINS -DNOMINMAX -DSENTRY_ENVIRONMENT=\\\"development\\\" -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSTB_TEXTEDIT_KEYTYPE=unsigned -IC:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\include -IC:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\build\\Release -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\arcusee7666f716199\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\proto47a676cb9257b\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\zlibcfb9789dc2a53\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\spdlo2d4b540ab0f22\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\fmtdb20aad6e1bd4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\range0301bf3d76d5d\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\clippf8ddcd4a5d961\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbo34ee9aeb574f9\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbofe72fb50cf7c4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbo1b62b48748b82\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\rapidf7a3355ba53c4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\stb6342cecb318f5\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\boosta79d738920bc3\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\scrip4cfd014ad4660\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\nearg388d58da7a54c\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\curaebb245e64b5ac7\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\asio-c31dc2a380bf0\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\grpc8b6c73b4e5756\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\absei6a15444d60d47\\p\\include -external:W0 /DWIN32 /D_WINDOWS /EHsc /O2 /Ob2 /DNDEBUG -std:c++20 -MD /utf-8 /permissive- /FoCMakeFiles\\_CuraEngine.dir\\src\\utils\\ExtrusionSegment.cpp.obj /FdCMakeFiles\\_CuraEngine.dir\\_CuraEngine.pdb /FS -c C:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\src\\utils\\ExtrusionSegment.cpp", "file": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/src/utils/ExtrusionSegment.cpp", "output": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/build/Release/CMakeFiles/_CuraEngine.dir/src/utils/ExtrusionSegment.cpp.obj"}, {"directory": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/build/Release", "command": "C:\\PROGRA~1\\MICROS~1\\2022\\COMMUN~1\\VC\\Tools\\MSVC\\1444~1.352\\bin\\Hostx64\\x64\\cl.exe  /nologo /TP -DAGRPC_BOOST_ASIO -DARCUS -DBOOST_ALL_NO_LIB -DBOOST_STACKTRACE_USE_NOOP -DBOOST_STACKTRACE_USE_WINDBG -DBOOST_STACKTRACE_USE_WINDBG_CACHED -DCARES_STATICLIB -DCURA_ENGINE_HASH=\\\"unknown\\\" -DCURA_ENGINE_VERSION=\\\"5.11.0-alpha.0\\\" -DENABLE_PLUGINS -DNOMINMAX -DSENTRY_ENVIRONMENT=\\\"development\\\" -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSTB_TEXTEDIT_KEYTYPE=unsigned -IC:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\include -IC:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\build\\Release -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\arcusee7666f716199\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\proto47a676cb9257b\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\zlibcfb9789dc2a53\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\spdlo2d4b540ab0f22\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\fmtdb20aad6e1bd4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\range0301bf3d76d5d\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\clippf8ddcd4a5d961\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbo34ee9aeb574f9\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbofe72fb50cf7c4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbo1b62b48748b82\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\rapidf7a3355ba53c4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\stb6342cecb318f5\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\boosta79d738920bc3\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\scrip4cfd014ad4660\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\nearg388d58da7a54c\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\curaebb245e64b5ac7\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\asio-c31dc2a380bf0\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\grpc8b6c73b4e5756\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\absei6a15444d60d47\\p\\include -external:W0 /DWIN32 /D_WINDOWS /EHsc /O2 /Ob2 /DNDEBUG -std:c++20 -MD /utf-8 /permissive- /FoCMakeFiles\\_CuraEngine.dir\\src\\utils\\gettime.cpp.obj /FdCMakeFiles\\_CuraEngine.dir\\_CuraEngine.pdb /FS -c C:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\src\\utils\\gettime.cpp", "file": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/src/utils/gettime.cpp", "output": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/build/Release/CMakeFiles/_CuraEngine.dir/src/utils/gettime.cpp.obj"}, {"directory": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/build/Release", "command": "C:\\PROGRA~1\\MICROS~1\\2022\\COMMUN~1\\VC\\Tools\\MSVC\\1444~1.352\\bin\\Hostx64\\x64\\cl.exe  /nologo /TP -DAGRPC_BOOST_ASIO -DARCUS -DBOOST_ALL_NO_LIB -DBOOST_STACKTRACE_USE_NOOP -DBOOST_STACKTRACE_USE_WINDBG -DBOOST_STACKTRACE_USE_WINDBG_CACHED -DCARES_STATICLIB -DCURA_ENGINE_HASH=\\\"unknown\\\" -DCURA_ENGINE_VERSION=\\\"5.11.0-alpha.0\\\" -DENABLE_PLUGINS -DNOMINMAX -DSENTRY_ENVIRONMENT=\\\"development\\\" -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSTB_TEXTEDIT_KEYTYPE=unsigned -IC:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\include -IC:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\build\\Release -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\arcusee7666f716199\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\proto47a676cb9257b\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\zlibcfb9789dc2a53\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\spdlo2d4b540ab0f22\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\fmtdb20aad6e1bd4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\range0301bf3d76d5d\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\clippf8ddcd4a5d961\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbo34ee9aeb574f9\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbofe72fb50cf7c4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbo1b62b48748b82\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\rapidf7a3355ba53c4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\stb6342cecb318f5\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\boosta79d738920bc3\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\scrip4cfd014ad4660\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\nearg388d58da7a54c\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\curaebb245e64b5ac7\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\asio-c31dc2a380bf0\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\grpc8b6c73b4e5756\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\absei6a15444d60d47\\p\\include -external:W0 /DWIN32 /D_WINDOWS /EHsc /O2 /Ob2 /DNDEBUG -std:c++20 -MD /utf-8 /permissive- /FoCMakeFiles\\_CuraEngine.dir\\src\\utils\\linearAlg2D.cpp.obj /FdCMakeFiles\\_CuraEngine.dir\\_CuraEngine.pdb /FS -c C:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\src\\utils\\linearAlg2D.cpp", "file": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/src/utils/linearAlg2D.cpp", "output": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/build/Release/CMakeFiles/_CuraEngine.dir/src/utils/linearAlg2D.cpp.obj"}, {"directory": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/build/Release", "command": "C:\\PROGRA~1\\MICROS~1\\2022\\COMMUN~1\\VC\\Tools\\MSVC\\1444~1.352\\bin\\Hostx64\\x64\\cl.exe  /nologo /TP -DAGRPC_BOOST_ASIO -DARCUS -DBOOST_ALL_NO_LIB -DBOOST_STACKTRACE_USE_NOOP -DBOOST_STACKTRACE_USE_WINDBG -DBOOST_STACKTRACE_USE_WINDBG_CACHED -DCARES_STATICLIB -DCURA_ENGINE_HASH=\\\"unknown\\\" -DCURA_ENGINE_VERSION=\\\"5.11.0-alpha.0\\\" -DENABLE_PLUGINS -DNOMINMAX -DSENTRY_ENVIRONMENT=\\\"development\\\" -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSTB_TEXTEDIT_KEYTYPE=unsigned -IC:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\include -IC:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\build\\Release -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\arcusee7666f716199\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\proto47a676cb9257b\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\zlibcfb9789dc2a53\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\spdlo2d4b540ab0f22\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\fmtdb20aad6e1bd4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\range0301bf3d76d5d\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\clippf8ddcd4a5d961\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbo34ee9aeb574f9\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbofe72fb50cf7c4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbo1b62b48748b82\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\rapidf7a3355ba53c4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\stb6342cecb318f5\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\boosta79d738920bc3\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\scrip4cfd014ad4660\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\nearg388d58da7a54c\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\curaebb245e64b5ac7\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\asio-c31dc2a380bf0\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\grpc8b6c73b4e5756\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\absei6a15444d60d47\\p\\include -external:W0 /DWIN32 /D_WINDOWS /EHsc /O2 /Ob2 /DNDEBUG -std:c++20 -MD /utf-8 /permissive- /FoCMakeFiles\\_CuraEngine.dir\\src\\utils\\ListPolyIt.cpp.obj /FdCMakeFiles\\_CuraEngine.dir\\_CuraEngine.pdb /FS -c C:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\src\\utils\\ListPolyIt.cpp", "file": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/src/utils/ListPolyIt.cpp", "output": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/build/Release/CMakeFiles/_CuraEngine.dir/src/utils/ListPolyIt.cpp.obj"}, {"directory": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/build/Release", "command": "C:\\PROGRA~1\\MICROS~1\\2022\\COMMUN~1\\VC\\Tools\\MSVC\\1444~1.352\\bin\\Hostx64\\x64\\cl.exe  /nologo /TP -DAGRPC_BOOST_ASIO -DARCUS -DBOOST_ALL_NO_LIB -DBOOST_STACKTRACE_USE_NOOP -DBOOST_STACKTRACE_USE_WINDBG -DBOOST_STACKTRACE_USE_WINDBG_CACHED -DCARES_STATICLIB -DCURA_ENGINE_HASH=\\\"unknown\\\" -DCURA_ENGINE_VERSION=\\\"5.11.0-alpha.0\\\" -DENABLE_PLUGINS -DNOMINMAX -DSENTRY_ENVIRONMENT=\\\"development\\\" -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSTB_TEXTEDIT_KEYTYPE=unsigned -IC:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\include -IC:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\build\\Release -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\arcusee7666f716199\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\proto47a676cb9257b\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\zlibcfb9789dc2a53\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\spdlo2d4b540ab0f22\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\fmtdb20aad6e1bd4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\range0301bf3d76d5d\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\clippf8ddcd4a5d961\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbo34ee9aeb574f9\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbofe72fb50cf7c4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbo1b62b48748b82\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\rapidf7a3355ba53c4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\stb6342cecb318f5\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\boosta79d738920bc3\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\scrip4cfd014ad4660\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\nearg388d58da7a54c\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\curaebb245e64b5ac7\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\asio-c31dc2a380bf0\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\grpc8b6c73b4e5756\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\absei6a15444d60d47\\p\\include -external:W0 /DWIN32 /D_WINDOWS /EHsc /O2 /Ob2 /DNDEBUG -std:c++20 -MD /utf-8 /permissive- /FoCMakeFiles\\_CuraEngine.dir\\src\\utils\\Matrix4x3D.cpp.obj /FdCMakeFiles\\_CuraEngine.dir\\_CuraEngine.pdb /FS -c C:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\src\\utils\\Matrix4x3D.cpp", "file": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/src/utils/Matrix4x3D.cpp", "output": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/build/Release/CMakeFiles/_CuraEngine.dir/src/utils/Matrix4x3D.cpp.obj"}, {"directory": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/build/Release", "command": "C:\\PROGRA~1\\MICROS~1\\2022\\COMMUN~1\\VC\\Tools\\MSVC\\1444~1.352\\bin\\Hostx64\\x64\\cl.exe  /nologo /TP -DAGRPC_BOOST_ASIO -DARCUS -DBOOST_ALL_NO_LIB -DBOOST_STACKTRACE_USE_NOOP -DBOOST_STACKTRACE_USE_WINDBG -DBOOST_STACKTRACE_USE_WINDBG_CACHED -DCARES_STATICLIB -DCURA_ENGINE_HASH=\\\"unknown\\\" -DCURA_ENGINE_VERSION=\\\"5.11.0-alpha.0\\\" -DENABLE_PLUGINS -DNOMINMAX -DSENTRY_ENVIRONMENT=\\\"development\\\" -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSTB_TEXTEDIT_KEYTYPE=unsigned -IC:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\include -IC:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\build\\Release -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\arcusee7666f716199\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\proto47a676cb9257b\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\zlibcfb9789dc2a53\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\spdlo2d4b540ab0f22\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\fmtdb20aad6e1bd4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\range0301bf3d76d5d\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\clippf8ddcd4a5d961\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbo34ee9aeb574f9\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbofe72fb50cf7c4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbo1b62b48748b82\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\rapidf7a3355ba53c4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\stb6342cecb318f5\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\boosta79d738920bc3\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\scrip4cfd014ad4660\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\nearg388d58da7a54c\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\curaebb245e64b5ac7\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\asio-c31dc2a380bf0\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\grpc8b6c73b4e5756\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\absei6a15444d60d47\\p\\include -external:W0 /DWIN32 /D_WINDOWS /EHsc /O2 /Ob2 /DNDEBUG -std:c++20 -MD /utf-8 /permissive- /FoCMakeFiles\\_CuraEngine.dir\\src\\utils\\MinimumSpanningTree.cpp.obj /FdCMakeFiles\\_CuraEngine.dir\\_CuraEngine.pdb /FS -c C:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\src\\utils\\MinimumSpanningTree.cpp", "file": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/src/utils/MinimumSpanningTree.cpp", "output": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/build/Release/CMakeFiles/_CuraEngine.dir/src/utils/MinimumSpanningTree.cpp.obj"}, {"directory": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/build/Release", "command": "C:\\PROGRA~1\\MICROS~1\\2022\\COMMUN~1\\VC\\Tools\\MSVC\\1444~1.352\\bin\\Hostx64\\x64\\cl.exe  /nologo /TP -DAGRPC_BOOST_ASIO -DARCUS -DBOOST_ALL_NO_LIB -DBOOST_STACKTRACE_USE_NOOP -DBOOST_STACKTRACE_USE_WINDBG -DBOOST_STACKTRACE_USE_WINDBG_CACHED -DCARES_STATICLIB -DCURA_ENGINE_HASH=\\\"unknown\\\" -DCURA_ENGINE_VERSION=\\\"5.11.0-alpha.0\\\" -DENABLE_PLUGINS -DNOMINMAX -DSENTRY_ENVIRONMENT=\\\"development\\\" -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSTB_TEXTEDIT_KEYTYPE=unsigned -IC:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\include -IC:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\build\\Release -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\arcusee7666f716199\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\proto47a676cb9257b\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\zlibcfb9789dc2a53\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\spdlo2d4b540ab0f22\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\fmtdb20aad6e1bd4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\range0301bf3d76d5d\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\clippf8ddcd4a5d961\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbo34ee9aeb574f9\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbofe72fb50cf7c4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbo1b62b48748b82\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\rapidf7a3355ba53c4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\stb6342cecb318f5\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\boosta79d738920bc3\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\scrip4cfd014ad4660\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\nearg388d58da7a54c\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\curaebb245e64b5ac7\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\asio-c31dc2a380bf0\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\grpc8b6c73b4e5756\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\absei6a15444d60d47\\p\\include -external:W0 /DWIN32 /D_WINDOWS /EHsc /O2 /Ob2 /DNDEBUG -std:c++20 -MD /utf-8 /permissive- /FoCMakeFiles\\_CuraEngine.dir\\src\\utils\\PolygonConnector.cpp.obj /FdCMakeFiles\\_CuraEngine.dir\\_CuraEngine.pdb /FS -c C:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\src\\utils\\PolygonConnector.cpp", "file": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/src/utils/PolygonConnector.cpp", "output": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/build/Release/CMakeFiles/_CuraEngine.dir/src/utils/PolygonConnector.cpp.obj"}, {"directory": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/build/Release", "command": "C:\\PROGRA~1\\MICROS~1\\2022\\COMMUN~1\\VC\\Tools\\MSVC\\1444~1.352\\bin\\Hostx64\\x64\\cl.exe  /nologo /TP -DAGRPC_BOOST_ASIO -DARCUS -DBOOST_ALL_NO_LIB -DBOOST_STACKTRACE_USE_NOOP -DBOOST_STACKTRACE_USE_WINDBG -DBOOST_STACKTRACE_USE_WINDBG_CACHED -DCARES_STATICLIB -DCURA_ENGINE_HASH=\\\"unknown\\\" -DCURA_ENGINE_VERSION=\\\"5.11.0-alpha.0\\\" -DENABLE_PLUGINS -DNOMINMAX -DSENTRY_ENVIRONMENT=\\\"development\\\" -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSTB_TEXTEDIT_KEYTYPE=unsigned -IC:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\include -IC:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\build\\Release -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\arcusee7666f716199\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\proto47a676cb9257b\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\zlibcfb9789dc2a53\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\spdlo2d4b540ab0f22\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\fmtdb20aad6e1bd4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\range0301bf3d76d5d\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\clippf8ddcd4a5d961\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbo34ee9aeb574f9\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbofe72fb50cf7c4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbo1b62b48748b82\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\rapidf7a3355ba53c4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\stb6342cecb318f5\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\boosta79d738920bc3\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\scrip4cfd014ad4660\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\nearg388d58da7a54c\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\curaebb245e64b5ac7\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\asio-c31dc2a380bf0\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\grpc8b6c73b4e5756\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\absei6a15444d60d47\\p\\include -external:W0 /DWIN32 /D_WINDOWS /EHsc /O2 /Ob2 /DNDEBUG -std:c++20 -MD /utf-8 /permissive- /FoCMakeFiles\\_CuraEngine.dir\\src\\utils\\PolygonsPointIndex.cpp.obj /FdCMakeFiles\\_CuraEngine.dir\\_CuraEngine.pdb /FS -c C:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\src\\utils\\PolygonsPointIndex.cpp", "file": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/src/utils/PolygonsPointIndex.cpp", "output": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/build/Release/CMakeFiles/_CuraEngine.dir/src/utils/PolygonsPointIndex.cpp.obj"}, {"directory": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/build/Release", "command": "C:\\PROGRA~1\\MICROS~1\\2022\\COMMUN~1\\VC\\Tools\\MSVC\\1444~1.352\\bin\\Hostx64\\x64\\cl.exe  /nologo /TP -DAGRPC_BOOST_ASIO -DARCUS -DBOOST_ALL_NO_LIB -DBOOST_STACKTRACE_USE_NOOP -DBOOST_STACKTRACE_USE_WINDBG -DBOOST_STACKTRACE_USE_WINDBG_CACHED -DCARES_STATICLIB -DCURA_ENGINE_HASH=\\\"unknown\\\" -DCURA_ENGINE_VERSION=\\\"5.11.0-alpha.0\\\" -DENABLE_PLUGINS -DNOMINMAX -DSENTRY_ENVIRONMENT=\\\"development\\\" -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSTB_TEXTEDIT_KEYTYPE=unsigned -IC:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\include -IC:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\build\\Release -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\arcusee7666f716199\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\proto47a676cb9257b\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\zlibcfb9789dc2a53\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\spdlo2d4b540ab0f22\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\fmtdb20aad6e1bd4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\range0301bf3d76d5d\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\clippf8ddcd4a5d961\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbo34ee9aeb574f9\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbofe72fb50cf7c4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbo1b62b48748b82\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\rapidf7a3355ba53c4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\stb6342cecb318f5\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\boosta79d738920bc3\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\scrip4cfd014ad4660\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\nearg388d58da7a54c\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\curaebb245e64b5ac7\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\asio-c31dc2a380bf0\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\grpc8b6c73b4e5756\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\absei6a15444d60d47\\p\\include -external:W0 /DWIN32 /D_WINDOWS /EHsc /O2 /Ob2 /DNDEBUG -std:c++20 -MD /utf-8 /permissive- /FoCMakeFiles\\_CuraEngine.dir\\src\\utils\\PolygonsSegmentIndex.cpp.obj /FdCMakeFiles\\_CuraEngine.dir\\_CuraEngine.pdb /FS -c C:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\src\\utils\\PolygonsSegmentIndex.cpp", "file": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/src/utils/PolygonsSegmentIndex.cpp", "output": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/build/Release/CMakeFiles/_CuraEngine.dir/src/utils/PolygonsSegmentIndex.cpp.obj"}, {"directory": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/build/Release", "command": "C:\\PROGRA~1\\MICROS~1\\2022\\COMMUN~1\\VC\\Tools\\MSVC\\1444~1.352\\bin\\Hostx64\\x64\\cl.exe  /nologo /TP -DAGRPC_BOOST_ASIO -DARCUS -DBOOST_ALL_NO_LIB -DBOOST_STACKTRACE_USE_NOOP -DBOOST_STACKTRACE_USE_WINDBG -DBOOST_STACKTRACE_USE_WINDBG_CACHED -DCARES_STATICLIB -DCURA_ENGINE_HASH=\\\"unknown\\\" -DCURA_ENGINE_VERSION=\\\"5.11.0-alpha.0\\\" -DENABLE_PLUGINS -DNOMINMAX -DSENTRY_ENVIRONMENT=\\\"development\\\" -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSTB_TEXTEDIT_KEYTYPE=unsigned -IC:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\include -IC:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\build\\Release -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\arcusee7666f716199\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\proto47a676cb9257b\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\zlibcfb9789dc2a53\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\spdlo2d4b540ab0f22\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\fmtdb20aad6e1bd4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\range0301bf3d76d5d\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\clippf8ddcd4a5d961\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbo34ee9aeb574f9\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbofe72fb50cf7c4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbo1b62b48748b82\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\rapidf7a3355ba53c4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\stb6342cecb318f5\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\boosta79d738920bc3\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\scrip4cfd014ad4660\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\nearg388d58da7a54c\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\curaebb245e64b5ac7\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\asio-c31dc2a380bf0\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\grpc8b6c73b4e5756\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\absei6a15444d60d47\\p\\include -external:W0 /DWIN32 /D_WINDOWS /EHsc /O2 /Ob2 /DNDEBUG -std:c++20 -MD /utf-8 /permissive- /FoCMakeFiles\\_CuraEngine.dir\\src\\utils\\polygonUtils.cpp.obj /FdCMakeFiles\\_CuraEngine.dir\\_CuraEngine.pdb /FS -c C:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\src\\utils\\polygonUtils.cpp", "file": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/src/utils/polygonUtils.cpp", "output": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/build/Release/CMakeFiles/_CuraEngine.dir/src/utils/polygonUtils.cpp.obj"}, {"directory": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/build/Release", "command": "C:\\PROGRA~1\\MICROS~1\\2022\\COMMUN~1\\VC\\Tools\\MSVC\\1444~1.352\\bin\\Hostx64\\x64\\cl.exe  /nologo /TP -DAGRPC_BOOST_ASIO -DARCUS -DBOOST_ALL_NO_LIB -DBOOST_STACKTRACE_USE_NOOP -DBOOST_STACKTRACE_USE_WINDBG -DBOOST_STACKTRACE_USE_WINDBG_CACHED -DCARES_STATICLIB -DCURA_ENGINE_HASH=\\\"unknown\\\" -DCURA_ENGINE_VERSION=\\\"5.11.0-alpha.0\\\" -DENABLE_PLUGINS -DNOMINMAX -DSENTRY_ENVIRONMENT=\\\"development\\\" -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSTB_TEXTEDIT_KEYTYPE=unsigned -IC:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\include -IC:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\build\\Release -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\arcusee7666f716199\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\proto47a676cb9257b\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\zlibcfb9789dc2a53\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\spdlo2d4b540ab0f22\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\fmtdb20aad6e1bd4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\range0301bf3d76d5d\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\clippf8ddcd4a5d961\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbo34ee9aeb574f9\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbofe72fb50cf7c4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbo1b62b48748b82\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\rapidf7a3355ba53c4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\stb6342cecb318f5\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\boosta79d738920bc3\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\scrip4cfd014ad4660\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\nearg388d58da7a54c\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\curaebb245e64b5ac7\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\asio-c31dc2a380bf0\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\grpc8b6c73b4e5756\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\absei6a15444d60d47\\p\\include -external:W0 /DWIN32 /D_WINDOWS /EHsc /O2 /Ob2 /DNDEBUG -std:c++20 -MD /utf-8 /permissive- /FoCMakeFiles\\_CuraEngine.dir\\src\\utils\\PolylineStitcher.cpp.obj /FdCMakeFiles\\_CuraEngine.dir\\_CuraEngine.pdb /FS -c C:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\src\\utils\\PolylineStitcher.cpp", "file": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/src/utils/PolylineStitcher.cpp", "output": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/build/Release/CMakeFiles/_CuraEngine.dir/src/utils/PolylineStitcher.cpp.obj"}, {"directory": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/build/Release", "command": "C:\\PROGRA~1\\MICROS~1\\2022\\COMMUN~1\\VC\\Tools\\MSVC\\1444~1.352\\bin\\Hostx64\\x64\\cl.exe  /nologo /TP -DAGRPC_BOOST_ASIO -DARCUS -DBOOST_ALL_NO_LIB -DBOOST_STACKTRACE_USE_NOOP -DBOOST_STACKTRACE_USE_WINDBG -DBOOST_STACKTRACE_USE_WINDBG_CACHED -DCARES_STATICLIB -DCURA_ENGINE_HASH=\\\"unknown\\\" -DCURA_ENGINE_VERSION=\\\"5.11.0-alpha.0\\\" -DENABLE_PLUGINS -DNOMINMAX -DSENTRY_ENVIRONMENT=\\\"development\\\" -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSTB_TEXTEDIT_KEYTYPE=unsigned -IC:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\include -IC:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\build\\Release -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\arcusee7666f716199\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\proto47a676cb9257b\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\zlibcfb9789dc2a53\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\spdlo2d4b540ab0f22\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\fmtdb20aad6e1bd4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\range0301bf3d76d5d\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\clippf8ddcd4a5d961\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbo34ee9aeb574f9\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbofe72fb50cf7c4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbo1b62b48748b82\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\rapidf7a3355ba53c4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\stb6342cecb318f5\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\boosta79d738920bc3\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\scrip4cfd014ad4660\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\nearg388d58da7a54c\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\curaebb245e64b5ac7\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\asio-c31dc2a380bf0\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\grpc8b6c73b4e5756\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\absei6a15444d60d47\\p\\include -external:W0 /DWIN32 /D_WINDOWS /EHsc /O2 /Ob2 /DNDEBUG -std:c++20 -MD /utf-8 /permissive- /FoCMakeFiles\\_CuraEngine.dir\\src\\utils\\Simplify.cpp.obj /FdCMakeFiles\\_CuraEngine.dir\\_CuraEngine.pdb /FS -c C:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\src\\utils\\Simplify.cpp", "file": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/src/utils/Simplify.cpp", "output": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/build/Release/CMakeFiles/_CuraEngine.dir/src/utils/Simplify.cpp.obj"}, {"directory": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/build/Release", "command": "C:\\PROGRA~1\\MICROS~1\\2022\\COMMUN~1\\VC\\Tools\\MSVC\\1444~1.352\\bin\\Hostx64\\x64\\cl.exe  /nologo /TP -DAGRPC_BOOST_ASIO -DARCUS -DBOOST_ALL_NO_LIB -DBOOST_STACKTRACE_USE_NOOP -DBOOST_STACKTRACE_USE_WINDBG -DBOOST_STACKTRACE_USE_WINDBG_CACHED -DCARES_STATICLIB -DCURA_ENGINE_HASH=\\\"unknown\\\" -DCURA_ENGINE_VERSION=\\\"5.11.0-alpha.0\\\" -DENABLE_PLUGINS -DNOMINMAX -DSENTRY_ENVIRONMENT=\\\"development\\\" -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSTB_TEXTEDIT_KEYTYPE=unsigned -IC:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\include -IC:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\build\\Release -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\arcusee7666f716199\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\proto47a676cb9257b\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\zlibcfb9789dc2a53\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\spdlo2d4b540ab0f22\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\fmtdb20aad6e1bd4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\range0301bf3d76d5d\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\clippf8ddcd4a5d961\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbo34ee9aeb574f9\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbofe72fb50cf7c4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbo1b62b48748b82\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\rapidf7a3355ba53c4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\stb6342cecb318f5\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\boosta79d738920bc3\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\scrip4cfd014ad4660\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\nearg388d58da7a54c\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\curaebb245e64b5ac7\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\asio-c31dc2a380bf0\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\grpc8b6c73b4e5756\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\absei6a15444d60d47\\p\\include -external:W0 /DWIN32 /D_WINDOWS /EHsc /O2 /Ob2 /DNDEBUG -std:c++20 -MD /utf-8 /permissive- /FoCMakeFiles\\_CuraEngine.dir\\src\\utils\\SVG.cpp.obj /FdCMakeFiles\\_CuraEngine.dir\\_CuraEngine.pdb /FS -c C:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\src\\utils\\SVG.cpp", "file": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/src/utils/SVG.cpp", "output": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/build/Release/CMakeFiles/_CuraEngine.dir/src/utils/SVG.cpp.obj"}, {"directory": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/build/Release", "command": "C:\\PROGRA~1\\MICROS~1\\2022\\COMMUN~1\\VC\\Tools\\MSVC\\1444~1.352\\bin\\Hostx64\\x64\\cl.exe  /nologo /TP -DAGRPC_BOOST_ASIO -DARCUS -DBOOST_ALL_NO_LIB -DBOOST_STACKTRACE_USE_NOOP -DBOOST_STACKTRACE_USE_WINDBG -DBOOST_STACKTRACE_USE_WINDBG_CACHED -DCARES_STATICLIB -DCURA_ENGINE_HASH=\\\"unknown\\\" -DCURA_ENGINE_VERSION=\\\"5.11.0-alpha.0\\\" -DENABLE_PLUGINS -DNOMINMAX -DSENTRY_ENVIRONMENT=\\\"development\\\" -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSTB_TEXTEDIT_KEYTYPE=unsigned -IC:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\include -IC:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\build\\Release -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\arcusee7666f716199\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\proto47a676cb9257b\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\zlibcfb9789dc2a53\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\spdlo2d4b540ab0f22\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\fmtdb20aad6e1bd4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\range0301bf3d76d5d\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\clippf8ddcd4a5d961\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbo34ee9aeb574f9\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbofe72fb50cf7c4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbo1b62b48748b82\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\rapidf7a3355ba53c4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\stb6342cecb318f5\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\boosta79d738920bc3\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\scrip4cfd014ad4660\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\nearg388d58da7a54c\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\curaebb245e64b5ac7\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\asio-c31dc2a380bf0\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\grpc8b6c73b4e5756\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\absei6a15444d60d47\\p\\include -external:W0 /DWIN32 /D_WINDOWS /EHsc /O2 /Ob2 /DNDEBUG -std:c++20 -MD /utf-8 /permissive- /FoCMakeFiles\\_CuraEngine.dir\\src\\utils\\SquareGrid.cpp.obj /FdCMakeFiles\\_CuraEngine.dir\\_CuraEngine.pdb /FS -c C:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\src\\utils\\SquareGrid.cpp", "file": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/src/utils/SquareGrid.cpp", "output": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/build/Release/CMakeFiles/_CuraEngine.dir/src/utils/SquareGrid.cpp.obj"}, {"directory": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/build/Release", "command": "C:\\PROGRA~1\\MICROS~1\\2022\\COMMUN~1\\VC\\Tools\\MSVC\\1444~1.352\\bin\\Hostx64\\x64\\cl.exe  /nologo /TP -DAGRPC_BOOST_ASIO -DARCUS -DBOOST_ALL_NO_LIB -DBOOST_STACKTRACE_USE_NOOP -DBOOST_STACKTRACE_USE_WINDBG -DBOOST_STACKTRACE_USE_WINDBG_CACHED -DCARES_STATICLIB -DCURA_ENGINE_HASH=\\\"unknown\\\" -DCURA_ENGINE_VERSION=\\\"5.11.0-alpha.0\\\" -DENABLE_PLUGINS -DNOMINMAX -DSENTRY_ENVIRONMENT=\\\"development\\\" -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSTB_TEXTEDIT_KEYTYPE=unsigned -IC:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\include -IC:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\build\\Release -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\arcusee7666f716199\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\proto47a676cb9257b\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\zlibcfb9789dc2a53\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\spdlo2d4b540ab0f22\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\fmtdb20aad6e1bd4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\range0301bf3d76d5d\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\clippf8ddcd4a5d961\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbo34ee9aeb574f9\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbofe72fb50cf7c4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbo1b62b48748b82\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\rapidf7a3355ba53c4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\stb6342cecb318f5\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\boosta79d738920bc3\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\scrip4cfd014ad4660\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\nearg388d58da7a54c\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\curaebb245e64b5ac7\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\asio-c31dc2a380bf0\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\grpc8b6c73b4e5756\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\absei6a15444d60d47\\p\\include -external:W0 /DWIN32 /D_WINDOWS /EHsc /O2 /Ob2 /DNDEBUG -std:c++20 -MD /utf-8 /permissive- /FoCMakeFiles\\_CuraEngine.dir\\src\\utils\\ThreadPool.cpp.obj /FdCMakeFiles\\_CuraEngine.dir\\_CuraEngine.pdb /FS -c C:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\src\\utils\\ThreadPool.cpp", "file": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/src/utils/ThreadPool.cpp", "output": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/build/Release/CMakeFiles/_CuraEngine.dir/src/utils/ThreadPool.cpp.obj"}, {"directory": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/build/Release", "command": "C:\\PROGRA~1\\MICROS~1\\2022\\COMMUN~1\\VC\\Tools\\MSVC\\1444~1.352\\bin\\Hostx64\\x64\\cl.exe  /nologo /TP -DAGRPC_BOOST_ASIO -DARCUS -DBOOST_ALL_NO_LIB -DBOOST_STACKTRACE_USE_NOOP -DBOOST_STACKTRACE_USE_WINDBG -DBOOST_STACKTRACE_USE_WINDBG_CACHED -DCARES_STATICLIB -DCURA_ENGINE_HASH=\\\"unknown\\\" -DCURA_ENGINE_VERSION=\\\"5.11.0-alpha.0\\\" -DENABLE_PLUGINS -DNOMINMAX -DSENTRY_ENVIRONMENT=\\\"development\\\" -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSTB_TEXTEDIT_KEYTYPE=unsigned -IC:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\include -IC:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\build\\Release -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\arcusee7666f716199\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\proto47a676cb9257b\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\zlibcfb9789dc2a53\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\spdlo2d4b540ab0f22\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\fmtdb20aad6e1bd4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\range0301bf3d76d5d\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\clippf8ddcd4a5d961\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbo34ee9aeb574f9\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbofe72fb50cf7c4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbo1b62b48748b82\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\rapidf7a3355ba53c4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\stb6342cecb318f5\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\boosta79d738920bc3\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\scrip4cfd014ad4660\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\nearg388d58da7a54c\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\curaebb245e64b5ac7\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\asio-c31dc2a380bf0\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\grpc8b6c73b4e5756\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\absei6a15444d60d47\\p\\include -external:W0 /DWIN32 /D_WINDOWS /EHsc /O2 /Ob2 /DNDEBUG -std:c++20 -MD /utf-8 /permissive- /FoCMakeFiles\\_CuraEngine.dir\\src\\utils\\ToolpathVisualizer.cpp.obj /FdCMakeFiles\\_CuraEngine.dir\\_CuraEngine.pdb /FS -c C:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\src\\utils\\ToolpathVisualizer.cpp", "file": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/src/utils/ToolpathVisualizer.cpp", "output": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/build/Release/CMakeFiles/_CuraEngine.dir/src/utils/ToolpathVisualizer.cpp.obj"}, {"directory": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/build/Release", "command": "C:\\PROGRA~1\\MICROS~1\\2022\\COMMUN~1\\VC\\Tools\\MSVC\\1444~1.352\\bin\\Hostx64\\x64\\cl.exe  /nologo /TP -DAGRPC_BOOST_ASIO -DARCUS -DBOOST_ALL_NO_LIB -DBOOST_STACKTRACE_USE_NOOP -DBOOST_STACKTRACE_USE_WINDBG -DBOOST_STACKTRACE_USE_WINDBG_CACHED -DCARES_STATICLIB -DCURA_ENGINE_HASH=\\\"unknown\\\" -DCURA_ENGINE_VERSION=\\\"5.11.0-alpha.0\\\" -DENABLE_PLUGINS -DNOMINMAX -DSENTRY_ENVIRONMENT=\\\"development\\\" -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSTB_TEXTEDIT_KEYTYPE=unsigned -IC:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\include -IC:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\build\\Release -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\arcusee7666f716199\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\proto47a676cb9257b\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\zlibcfb9789dc2a53\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\spdlo2d4b540ab0f22\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\fmtdb20aad6e1bd4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\range0301bf3d76d5d\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\clippf8ddcd4a5d961\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbo34ee9aeb574f9\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbofe72fb50cf7c4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbo1b62b48748b82\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\rapidf7a3355ba53c4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\stb6342cecb318f5\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\boosta79d738920bc3\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\scrip4cfd014ad4660\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\nearg388d58da7a54c\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\curaebb245e64b5ac7\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\asio-c31dc2a380bf0\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\grpc8b6c73b4e5756\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\absei6a15444d60d47\\p\\include -external:W0 /DWIN32 /D_WINDOWS /EHsc /O2 /Ob2 /DNDEBUG -std:c++20 -MD /utf-8 /permissive- /FoCMakeFiles\\_CuraEngine.dir\\src\\utils\\VoronoiUtils.cpp.obj /FdCMakeFiles\\_CuraEngine.dir\\_CuraEngine.pdb /FS -c C:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\src\\utils\\VoronoiUtils.cpp", "file": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/src/utils/VoronoiUtils.cpp", "output": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/build/Release/CMakeFiles/_CuraEngine.dir/src/utils/VoronoiUtils.cpp.obj"}, {"directory": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/build/Release", "command": "C:\\PROGRA~1\\MICROS~1\\2022\\COMMUN~1\\VC\\Tools\\MSVC\\1444~1.352\\bin\\Hostx64\\x64\\cl.exe  /nologo /TP -DAGRPC_BOOST_ASIO -DARCUS -DBOOST_ALL_NO_LIB -DBOOST_STACKTRACE_USE_NOOP -DBOOST_STACKTRACE_USE_WINDBG -DBOOST_STACKTRACE_USE_WINDBG_CACHED -DCARES_STATICLIB -DCURA_ENGINE_HASH=\\\"unknown\\\" -DCURA_ENGINE_VERSION=\\\"5.11.0-alpha.0\\\" -DENABLE_PLUGINS -DNOMINMAX -DSENTRY_ENVIRONMENT=\\\"development\\\" -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSTB_TEXTEDIT_KEYTYPE=unsigned -IC:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\include -IC:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\build\\Release -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\arcusee7666f716199\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\proto47a676cb9257b\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\zlibcfb9789dc2a53\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\spdlo2d4b540ab0f22\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\fmtdb20aad6e1bd4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\range0301bf3d76d5d\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\clippf8ddcd4a5d961\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbo34ee9aeb574f9\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbofe72fb50cf7c4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbo1b62b48748b82\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\rapidf7a3355ba53c4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\stb6342cecb318f5\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\boosta79d738920bc3\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\scrip4cfd014ad4660\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\nearg388d58da7a54c\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\curaebb245e64b5ac7\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\asio-c31dc2a380bf0\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\grpc8b6c73b4e5756\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\absei6a15444d60d47\\p\\include -external:W0 /DWIN32 /D_WINDOWS /EHsc /O2 /Ob2 /DNDEBUG -std:c++20 -MD /utf-8 /permissive- /FoCMakeFiles\\_CuraEngine.dir\\src\\utils\\VoxelUtils.cpp.obj /FdCMakeFiles\\_CuraEngine.dir\\_CuraEngine.pdb /FS -c C:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\src\\utils\\VoxelUtils.cpp", "file": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/src/utils/VoxelUtils.cpp", "output": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/build/Release/CMakeFiles/_CuraEngine.dir/src/utils/VoxelUtils.cpp.obj"}, {"directory": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/build/Release", "command": "C:\\PROGRA~1\\MICROS~1\\2022\\COMMUN~1\\VC\\Tools\\MSVC\\1444~1.352\\bin\\Hostx64\\x64\\cl.exe  /nologo /TP -DAGRPC_BOOST_ASIO -DARCUS -DBOOST_ALL_NO_LIB -DBOOST_STACKTRACE_USE_NOOP -DBOOST_STACKTRACE_USE_WINDBG -DBOOST_STACKTRACE_USE_WINDBG_CACHED -DCARES_STATICLIB -DCURA_ENGINE_HASH=\\\"unknown\\\" -DCURA_ENGINE_VERSION=\\\"5.11.0-alpha.0\\\" -DENABLE_PLUGINS -DNOMINMAX -DSENTRY_ENVIRONMENT=\\\"development\\\" -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSTB_TEXTEDIT_KEYTYPE=unsigned -IC:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\include -IC:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\build\\Release -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\arcusee7666f716199\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\proto47a676cb9257b\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\zlibcfb9789dc2a53\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\spdlo2d4b540ab0f22\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\fmtdb20aad6e1bd4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\range0301bf3d76d5d\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\clippf8ddcd4a5d961\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbo34ee9aeb574f9\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbofe72fb50cf7c4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbo1b62b48748b82\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\rapidf7a3355ba53c4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\stb6342cecb318f5\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\boosta79d738920bc3\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\scrip4cfd014ad4660\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\nearg388d58da7a54c\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\curaebb245e64b5ac7\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\asio-c31dc2a380bf0\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\grpc8b6c73b4e5756\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\absei6a15444d60d47\\p\\include -external:W0 /DWIN32 /D_WINDOWS /EHsc /O2 /Ob2 /DNDEBUG -std:c++20 -MD /utf-8 /permissive- /FoCMakeFiles\\_CuraEngine.dir\\src\\utils\\MixedPolylineStitcher.cpp.obj /FdCMakeFiles\\_CuraEngine.dir\\_CuraEngine.pdb /FS -c C:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\src\\utils\\MixedPolylineStitcher.cpp", "file": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/src/utils/MixedPolylineStitcher.cpp", "output": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/build/Release/CMakeFiles/_CuraEngine.dir/src/utils/MixedPolylineStitcher.cpp.obj"}, {"directory": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/build/Release", "command": "C:\\PROGRA~1\\MICROS~1\\2022\\COMMUN~1\\VC\\Tools\\MSVC\\1444~1.352\\bin\\Hostx64\\x64\\cl.exe  /nologo /TP -DAGRPC_BOOST_ASIO -DARCUS -DBOOST_ALL_NO_LIB -DBOOST_STACKTRACE_USE_NOOP -DBOOST_STACKTRACE_USE_WINDBG -DBOOST_STACKTRACE_USE_WINDBG_CACHED -DCARES_STATICLIB -DCURA_ENGINE_HASH=\\\"unknown\\\" -DCURA_ENGINE_VERSION=\\\"5.11.0-alpha.0\\\" -DENABLE_PLUGINS -DNOMINMAX -DSENTRY_ENVIRONMENT=\\\"development\\\" -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSTB_TEXTEDIT_KEYTYPE=unsigned -IC:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\include -IC:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\build\\Release -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\arcusee7666f716199\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\proto47a676cb9257b\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\zlibcfb9789dc2a53\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\spdlo2d4b540ab0f22\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\fmtdb20aad6e1bd4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\range0301bf3d76d5d\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\clippf8ddcd4a5d961\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbo34ee9aeb574f9\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbofe72fb50cf7c4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbo1b62b48748b82\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\rapidf7a3355ba53c4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\stb6342cecb318f5\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\boosta79d738920bc3\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\scrip4cfd014ad4660\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\nearg388d58da7a54c\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\curaebb245e64b5ac7\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\asio-c31dc2a380bf0\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\grpc8b6c73b4e5756\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\absei6a15444d60d47\\p\\include -external:W0 /DWIN32 /D_WINDOWS /EHsc /O2 /Ob2 /DNDEBUG -std:c++20 -MD /utf-8 /permissive- /FoCMakeFiles\\_CuraEngine.dir\\src\\utils\\scoring\\BestElementFinder.cpp.obj /FdCMakeFiles\\_CuraEngine.dir\\_CuraEngine.pdb /FS -c C:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\src\\utils\\scoring\\BestElementFinder.cpp", "file": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/src/utils/scoring/BestElementFinder.cpp", "output": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/build/Release/CMakeFiles/_CuraEngine.dir/src/utils/scoring/BestElementFinder.cpp.obj"}, {"directory": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/build/Release", "command": "C:\\PROGRA~1\\MICROS~1\\2022\\COMMUN~1\\VC\\Tools\\MSVC\\1444~1.352\\bin\\Hostx64\\x64\\cl.exe  /nologo /TP -DAGRPC_BOOST_ASIO -DARCUS -DBOOST_ALL_NO_LIB -DBOOST_STACKTRACE_USE_NOOP -DBOOST_STACKTRACE_USE_WINDBG -DBOOST_STACKTRACE_USE_WINDBG_CACHED -DCARES_STATICLIB -DCURA_ENGINE_HASH=\\\"unknown\\\" -DCURA_ENGINE_VERSION=\\\"5.11.0-alpha.0\\\" -DENABLE_PLUGINS -DNOMINMAX -DSENTRY_ENVIRONMENT=\\\"development\\\" -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSTB_TEXTEDIT_KEYTYPE=unsigned -IC:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\include -IC:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\build\\Release -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\arcusee7666f716199\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\proto47a676cb9257b\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\zlibcfb9789dc2a53\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\spdlo2d4b540ab0f22\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\fmtdb20aad6e1bd4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\range0301bf3d76d5d\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\clippf8ddcd4a5d961\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbo34ee9aeb574f9\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbofe72fb50cf7c4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbo1b62b48748b82\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\rapidf7a3355ba53c4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\stb6342cecb318f5\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\boosta79d738920bc3\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\scrip4cfd014ad4660\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\nearg388d58da7a54c\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\curaebb245e64b5ac7\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\asio-c31dc2a380bf0\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\grpc8b6c73b4e5756\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\absei6a15444d60d47\\p\\include -external:W0 /DWIN32 /D_WINDOWS /EHsc /O2 /Ob2 /DNDEBUG -std:c++20 -MD /utf-8 /permissive- /FoCMakeFiles\\_CuraEngine.dir\\src\\utils\\scoring\\CornerScoringCriterion.cpp.obj /FdCMakeFiles\\_CuraEngine.dir\\_CuraEngine.pdb /FS -c C:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\src\\utils\\scoring\\CornerScoringCriterion.cpp", "file": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/src/utils/scoring/CornerScoringCriterion.cpp", "output": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/build/Release/CMakeFiles/_CuraEngine.dir/src/utils/scoring/CornerScoringCriterion.cpp.obj"}, {"directory": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/build/Release", "command": "C:\\PROGRA~1\\MICROS~1\\2022\\COMMUN~1\\VC\\Tools\\MSVC\\1444~1.352\\bin\\Hostx64\\x64\\cl.exe  /nologo /TP -DAGRPC_BOOST_ASIO -DARCUS -DBOOST_ALL_NO_LIB -DBOOST_STACKTRACE_USE_NOOP -DBOOST_STACKTRACE_USE_WINDBG -DBOOST_STACKTRACE_USE_WINDBG_CACHED -DCARES_STATICLIB -DCURA_ENGINE_HASH=\\\"unknown\\\" -DCURA_ENGINE_VERSION=\\\"5.11.0-alpha.0\\\" -DENABLE_PLUGINS -DNOMINMAX -DSENTRY_ENVIRONMENT=\\\"development\\\" -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSTB_TEXTEDIT_KEYTYPE=unsigned -IC:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\include -IC:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\build\\Release -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\arcusee7666f716199\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\proto47a676cb9257b\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\zlibcfb9789dc2a53\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\spdlo2d4b540ab0f22\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\fmtdb20aad6e1bd4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\range0301bf3d76d5d\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\clippf8ddcd4a5d961\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbo34ee9aeb574f9\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbofe72fb50cf7c4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbo1b62b48748b82\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\rapidf7a3355ba53c4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\stb6342cecb318f5\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\boosta79d738920bc3\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\scrip4cfd014ad4660\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\nearg388d58da7a54c\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\curaebb245e64b5ac7\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\asio-c31dc2a380bf0\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\grpc8b6c73b4e5756\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\absei6a15444d60d47\\p\\include -external:W0 /DWIN32 /D_WINDOWS /EHsc /O2 /Ob2 /DNDEBUG -std:c++20 -MD /utf-8 /permissive- /FoCMakeFiles\\_CuraEngine.dir\\src\\utils\\scoring\\DistanceScoringCriterion.cpp.obj /FdCMakeFiles\\_CuraEngine.dir\\_CuraEngine.pdb /FS -c C:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\src\\utils\\scoring\\DistanceScoringCriterion.cpp", "file": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/src/utils/scoring/DistanceScoringCriterion.cpp", "output": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/build/Release/CMakeFiles/_CuraEngine.dir/src/utils/scoring/DistanceScoringCriterion.cpp.obj"}, {"directory": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/build/Release", "command": "C:\\PROGRA~1\\MICROS~1\\2022\\COMMUN~1\\VC\\Tools\\MSVC\\1444~1.352\\bin\\Hostx64\\x64\\cl.exe  /nologo /TP -DAGRPC_BOOST_ASIO -DARCUS -DBOOST_ALL_NO_LIB -DBOOST_STACKTRACE_USE_NOOP -DBOOST_STACKTRACE_USE_WINDBG -DBOOST_STACKTRACE_USE_WINDBG_CACHED -DCARES_STATICLIB -DCURA_ENGINE_HASH=\\\"unknown\\\" -DCURA_ENGINE_VERSION=\\\"5.11.0-alpha.0\\\" -DENABLE_PLUGINS -DNOMINMAX -DSENTRY_ENVIRONMENT=\\\"development\\\" -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSTB_TEXTEDIT_KEYTYPE=unsigned -IC:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\include -IC:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\build\\Release -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\arcusee7666f716199\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\proto47a676cb9257b\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\zlibcfb9789dc2a53\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\spdlo2d4b540ab0f22\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\fmtdb20aad6e1bd4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\range0301bf3d76d5d\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\clippf8ddcd4a5d961\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbo34ee9aeb574f9\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbofe72fb50cf7c4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbo1b62b48748b82\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\rapidf7a3355ba53c4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\stb6342cecb318f5\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\boosta79d738920bc3\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\scrip4cfd014ad4660\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\nearg388d58da7a54c\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\curaebb245e64b5ac7\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\asio-c31dc2a380bf0\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\grpc8b6c73b4e5756\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\absei6a15444d60d47\\p\\include -external:W0 /DWIN32 /D_WINDOWS /EHsc /O2 /Ob2 /DNDEBUG -std:c++20 -MD /utf-8 /permissive- /FoCMakeFiles\\_CuraEngine.dir\\src\\utils\\scoring\\ExclusionAreaScoringCriterion.cpp.obj /FdCMakeFiles\\_CuraEngine.dir\\_CuraEngine.pdb /FS -c C:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\src\\utils\\scoring\\ExclusionAreaScoringCriterion.cpp", "file": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/src/utils/scoring/ExclusionAreaScoringCriterion.cpp", "output": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/build/Release/CMakeFiles/_CuraEngine.dir/src/utils/scoring/ExclusionAreaScoringCriterion.cpp.obj"}, {"directory": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/build/Release", "command": "C:\\PROGRA~1\\MICROS~1\\2022\\COMMUN~1\\VC\\Tools\\MSVC\\1444~1.352\\bin\\Hostx64\\x64\\cl.exe  /nologo /TP -DAGRPC_BOOST_ASIO -DARCUS -DBOOST_ALL_NO_LIB -DBOOST_STACKTRACE_USE_NOOP -DBOOST_STACKTRACE_USE_WINDBG -DBOOST_STACKTRACE_USE_WINDBG_CACHED -DCARES_STATICLIB -DCURA_ENGINE_HASH=\\\"unknown\\\" -DCURA_ENGINE_VERSION=\\\"5.11.0-alpha.0\\\" -DENABLE_PLUGINS -DNOMINMAX -DSENTRY_ENVIRONMENT=\\\"development\\\" -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSTB_TEXTEDIT_KEYTYPE=unsigned -IC:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\include -IC:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\build\\Release -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\arcusee7666f716199\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\proto47a676cb9257b\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\zlibcfb9789dc2a53\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\spdlo2d4b540ab0f22\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\fmtdb20aad6e1bd4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\range0301bf3d76d5d\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\clippf8ddcd4a5d961\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbo34ee9aeb574f9\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbofe72fb50cf7c4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbo1b62b48748b82\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\rapidf7a3355ba53c4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\stb6342cecb318f5\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\boosta79d738920bc3\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\scrip4cfd014ad4660\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\nearg388d58da7a54c\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\curaebb245e64b5ac7\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\asio-c31dc2a380bf0\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\grpc8b6c73b4e5756\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\absei6a15444d60d47\\p\\include -external:W0 /DWIN32 /D_WINDOWS /EHsc /O2 /Ob2 /DNDEBUG -std:c++20 -MD /utf-8 /permissive- /FoCMakeFiles\\_CuraEngine.dir\\src\\utils\\scoring\\RandomScoringCriterion.cpp.obj /FdCMakeFiles\\_CuraEngine.dir\\_CuraEngine.pdb /FS -c C:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\src\\utils\\scoring\\RandomScoringCriterion.cpp", "file": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/src/utils/scoring/RandomScoringCriterion.cpp", "output": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/build/Release/CMakeFiles/_CuraEngine.dir/src/utils/scoring/RandomScoringCriterion.cpp.obj"}, {"directory": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/build/Release", "command": "C:\\PROGRA~1\\MICROS~1\\2022\\COMMUN~1\\VC\\Tools\\MSVC\\1444~1.352\\bin\\Hostx64\\x64\\cl.exe  /nologo /TP -DAGRPC_BOOST_ASIO -DARCUS -DBOOST_ALL_NO_LIB -DBOOST_STACKTRACE_USE_NOOP -DBOOST_STACKTRACE_USE_WINDBG -DBOOST_STACKTRACE_USE_WINDBG_CACHED -DCARES_STATICLIB -DCURA_ENGINE_HASH=\\\"unknown\\\" -DCURA_ENGINE_VERSION=\\\"5.11.0-alpha.0\\\" -DENABLE_PLUGINS -DNOMINMAX -DSENTRY_ENVIRONMENT=\\\"development\\\" -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSTB_TEXTEDIT_KEYTYPE=unsigned -IC:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\include -IC:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\build\\Release -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\arcusee7666f716199\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\proto47a676cb9257b\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\zlibcfb9789dc2a53\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\spdlo2d4b540ab0f22\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\fmtdb20aad6e1bd4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\range0301bf3d76d5d\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\clippf8ddcd4a5d961\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbo34ee9aeb574f9\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbofe72fb50cf7c4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbo1b62b48748b82\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\rapidf7a3355ba53c4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\stb6342cecb318f5\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\boosta79d738920bc3\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\scrip4cfd014ad4660\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\nearg388d58da7a54c\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\curaebb245e64b5ac7\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\asio-c31dc2a380bf0\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\grpc8b6c73b4e5756\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\absei6a15444d60d47\\p\\include -external:W0 /DWIN32 /D_WINDOWS /EHsc /O2 /Ob2 /DNDEBUG -std:c++20 -MD /utf-8 /permissive- /FoCMakeFiles\\_CuraEngine.dir\\src\\geometry\\Point2LL.cpp.obj /FdCMakeFiles\\_CuraEngine.dir\\_CuraEngine.pdb /FS -c C:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\src\\geometry\\Point2LL.cpp", "file": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/src/geometry/Point2LL.cpp", "output": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/build/Release/CMakeFiles/_CuraEngine.dir/src/geometry/Point2LL.cpp.obj"}, {"directory": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/build/Release", "command": "C:\\PROGRA~1\\MICROS~1\\2022\\COMMUN~1\\VC\\Tools\\MSVC\\1444~1.352\\bin\\Hostx64\\x64\\cl.exe  /nologo /TP -DAGRPC_BOOST_ASIO -DARCUS -DBOOST_ALL_NO_LIB -DBOOST_STACKTRACE_USE_NOOP -DBOOST_STACKTRACE_USE_WINDBG -DBOOST_STACKTRACE_USE_WINDBG_CACHED -DCARES_STATICLIB -DCURA_ENGINE_HASH=\\\"unknown\\\" -DCURA_ENGINE_VERSION=\\\"5.11.0-alpha.0\\\" -DENABLE_PLUGINS -DNOMINMAX -DSENTRY_ENVIRONMENT=\\\"development\\\" -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSTB_TEXTEDIT_KEYTYPE=unsigned -IC:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\include -IC:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\build\\Release -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\arcusee7666f716199\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\proto47a676cb9257b\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\zlibcfb9789dc2a53\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\spdlo2d4b540ab0f22\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\fmtdb20aad6e1bd4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\range0301bf3d76d5d\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\clippf8ddcd4a5d961\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbo34ee9aeb574f9\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbofe72fb50cf7c4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbo1b62b48748b82\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\rapidf7a3355ba53c4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\stb6342cecb318f5\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\boosta79d738920bc3\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\scrip4cfd014ad4660\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\nearg388d58da7a54c\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\curaebb245e64b5ac7\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\asio-c31dc2a380bf0\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\grpc8b6c73b4e5756\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\absei6a15444d60d47\\p\\include -external:W0 /DWIN32 /D_WINDOWS /EHsc /O2 /Ob2 /DNDEBUG -std:c++20 -MD /utf-8 /permissive- /FoCMakeFiles\\_CuraEngine.dir\\src\\geometry\\Point3LL.cpp.obj /FdCMakeFiles\\_CuraEngine.dir\\_CuraEngine.pdb /FS -c C:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\src\\geometry\\Point3LL.cpp", "file": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/src/geometry/Point3LL.cpp", "output": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/build/Release/CMakeFiles/_CuraEngine.dir/src/geometry/Point3LL.cpp.obj"}, {"directory": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/build/Release", "command": "C:\\PROGRA~1\\MICROS~1\\2022\\COMMUN~1\\VC\\Tools\\MSVC\\1444~1.352\\bin\\Hostx64\\x64\\cl.exe  /nologo /TP -DAGRPC_BOOST_ASIO -DARCUS -DBOOST_ALL_NO_LIB -DBOOST_STACKTRACE_USE_NOOP -DBOOST_STACKTRACE_USE_WINDBG -DBOOST_STACKTRACE_USE_WINDBG_CACHED -DCARES_STATICLIB -DCURA_ENGINE_HASH=\\\"unknown\\\" -DCURA_ENGINE_VERSION=\\\"5.11.0-alpha.0\\\" -DENABLE_PLUGINS -DNOMINMAX -DSENTRY_ENVIRONMENT=\\\"development\\\" -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSTB_TEXTEDIT_KEYTYPE=unsigned -IC:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\include -IC:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\build\\Release -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\arcusee7666f716199\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\proto47a676cb9257b\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\zlibcfb9789dc2a53\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\spdlo2d4b540ab0f22\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\fmtdb20aad6e1bd4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\range0301bf3d76d5d\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\clippf8ddcd4a5d961\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbo34ee9aeb574f9\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbofe72fb50cf7c4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbo1b62b48748b82\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\rapidf7a3355ba53c4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\stb6342cecb318f5\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\boosta79d738920bc3\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\scrip4cfd014ad4660\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\nearg388d58da7a54c\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\curaebb245e64b5ac7\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\asio-c31dc2a380bf0\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\grpc8b6c73b4e5756\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\absei6a15444d60d47\\p\\include -external:W0 /DWIN32 /D_WINDOWS /EHsc /O2 /Ob2 /DNDEBUG -std:c++20 -MD /utf-8 /permissive- /FoCMakeFiles\\_CuraEngine.dir\\src\\geometry\\Polygon.cpp.obj /FdCMakeFiles\\_CuraEngine.dir\\_CuraEngine.pdb /FS -c C:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\src\\geometry\\Polygon.cpp", "file": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/src/geometry/Polygon.cpp", "output": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/build/Release/CMakeFiles/_CuraEngine.dir/src/geometry/Polygon.cpp.obj"}, {"directory": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/build/Release", "command": "C:\\PROGRA~1\\MICROS~1\\2022\\COMMUN~1\\VC\\Tools\\MSVC\\1444~1.352\\bin\\Hostx64\\x64\\cl.exe  /nologo /TP -DAGRPC_BOOST_ASIO -DARCUS -DBOOST_ALL_NO_LIB -DBOOST_STACKTRACE_USE_NOOP -DBOOST_STACKTRACE_USE_WINDBG -DBOOST_STACKTRACE_USE_WINDBG_CACHED -DCARES_STATICLIB -DCURA_ENGINE_HASH=\\\"unknown\\\" -DCURA_ENGINE_VERSION=\\\"5.11.0-alpha.0\\\" -DENABLE_PLUGINS -DNOMINMAX -DSENTRY_ENVIRONMENT=\\\"development\\\" -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSTB_TEXTEDIT_KEYTYPE=unsigned -IC:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\include -IC:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\build\\Release -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\arcusee7666f716199\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\proto47a676cb9257b\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\zlibcfb9789dc2a53\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\spdlo2d4b540ab0f22\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\fmtdb20aad6e1bd4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\range0301bf3d76d5d\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\clippf8ddcd4a5d961\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbo34ee9aeb574f9\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbofe72fb50cf7c4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbo1b62b48748b82\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\rapidf7a3355ba53c4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\stb6342cecb318f5\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\boosta79d738920bc3\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\scrip4cfd014ad4660\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\nearg388d58da7a54c\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\curaebb245e64b5ac7\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\asio-c31dc2a380bf0\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\grpc8b6c73b4e5756\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\absei6a15444d60d47\\p\\include -external:W0 /DWIN32 /D_WINDOWS /EHsc /O2 /Ob2 /DNDEBUG -std:c++20 -MD /utf-8 /permissive- /FoCMakeFiles\\_CuraEngine.dir\\src\\geometry\\Shape.cpp.obj /FdCMakeFiles\\_CuraEngine.dir\\_CuraEngine.pdb /FS -c C:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\src\\geometry\\Shape.cpp", "file": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/src/geometry/Shape.cpp", "output": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/build/Release/CMakeFiles/_CuraEngine.dir/src/geometry/Shape.cpp.obj"}, {"directory": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/build/Release", "command": "C:\\PROGRA~1\\MICROS~1\\2022\\COMMUN~1\\VC\\Tools\\MSVC\\1444~1.352\\bin\\Hostx64\\x64\\cl.exe  /nologo /TP -DAGRPC_BOOST_ASIO -DARCUS -DBOOST_ALL_NO_LIB -DBOOST_STACKTRACE_USE_NOOP -DBOOST_STACKTRACE_USE_WINDBG -DBOOST_STACKTRACE_USE_WINDBG_CACHED -DCARES_STATICLIB -DCURA_ENGINE_HASH=\\\"unknown\\\" -DCURA_ENGINE_VERSION=\\\"5.11.0-alpha.0\\\" -DENABLE_PLUGINS -DNOMINMAX -DSENTRY_ENVIRONMENT=\\\"development\\\" -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSTB_TEXTEDIT_KEYTYPE=unsigned -IC:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\include -IC:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\build\\Release -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\arcusee7666f716199\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\proto47a676cb9257b\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\zlibcfb9789dc2a53\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\spdlo2d4b540ab0f22\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\fmtdb20aad6e1bd4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\range0301bf3d76d5d\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\clippf8ddcd4a5d961\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbo34ee9aeb574f9\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbofe72fb50cf7c4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbo1b62b48748b82\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\rapidf7a3355ba53c4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\stb6342cecb318f5\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\boosta79d738920bc3\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\scrip4cfd014ad4660\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\nearg388d58da7a54c\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\curaebb245e64b5ac7\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\asio-c31dc2a380bf0\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\grpc8b6c73b4e5756\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\absei6a15444d60d47\\p\\include -external:W0 /DWIN32 /D_WINDOWS /EHsc /O2 /Ob2 /DNDEBUG -std:c++20 -MD /utf-8 /permissive- /FoCMakeFiles\\_CuraEngine.dir\\src\\geometry\\PointsSet.cpp.obj /FdCMakeFiles\\_CuraEngine.dir\\_CuraEngine.pdb /FS -c C:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\src\\geometry\\PointsSet.cpp", "file": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/src/geometry/PointsSet.cpp", "output": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/build/Release/CMakeFiles/_CuraEngine.dir/src/geometry/PointsSet.cpp.obj"}, {"directory": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/build/Release", "command": "C:\\PROGRA~1\\MICROS~1\\2022\\COMMUN~1\\VC\\Tools\\MSVC\\1444~1.352\\bin\\Hostx64\\x64\\cl.exe  /nologo /TP -DAGRPC_BOOST_ASIO -DARCUS -DBOOST_ALL_NO_LIB -DBOOST_STACKTRACE_USE_NOOP -DBOOST_STACKTRACE_USE_WINDBG -DBOOST_STACKTRACE_USE_WINDBG_CACHED -DCARES_STATICLIB -DCURA_ENGINE_HASH=\\\"unknown\\\" -DCURA_ENGINE_VERSION=\\\"5.11.0-alpha.0\\\" -DENABLE_PLUGINS -DNOMINMAX -DSENTRY_ENVIRONMENT=\\\"development\\\" -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSTB_TEXTEDIT_KEYTYPE=unsigned -IC:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\include -IC:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\build\\Release -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\arcusee7666f716199\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\proto47a676cb9257b\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\zlibcfb9789dc2a53\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\spdlo2d4b540ab0f22\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\fmtdb20aad6e1bd4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\range0301bf3d76d5d\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\clippf8ddcd4a5d961\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbo34ee9aeb574f9\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbofe72fb50cf7c4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbo1b62b48748b82\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\rapidf7a3355ba53c4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\stb6342cecb318f5\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\boosta79d738920bc3\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\scrip4cfd014ad4660\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\nearg388d58da7a54c\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\curaebb245e64b5ac7\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\asio-c31dc2a380bf0\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\grpc8b6c73b4e5756\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\absei6a15444d60d47\\p\\include -external:W0 /DWIN32 /D_WINDOWS /EHsc /O2 /Ob2 /DNDEBUG -std:c++20 -MD /utf-8 /permissive- /FoCMakeFiles\\_CuraEngine.dir\\src\\geometry\\SingleShape.cpp.obj /FdCMakeFiles\\_CuraEngine.dir\\_CuraEngine.pdb /FS -c C:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\src\\geometry\\SingleShape.cpp", "file": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/src/geometry/SingleShape.cpp", "output": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/build/Release/CMakeFiles/_CuraEngine.dir/src/geometry/SingleShape.cpp.obj"}, {"directory": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/build/Release", "command": "C:\\PROGRA~1\\MICROS~1\\2022\\COMMUN~1\\VC\\Tools\\MSVC\\1444~1.352\\bin\\Hostx64\\x64\\cl.exe  /nologo /TP -DAGRPC_BOOST_ASIO -DARCUS -DBOOST_ALL_NO_LIB -DBOOST_STACKTRACE_USE_NOOP -DBOOST_STACKTRACE_USE_WINDBG -DBOOST_STACKTRACE_USE_WINDBG_CACHED -DCARES_STATICLIB -DCURA_ENGINE_HASH=\\\"unknown\\\" -DCURA_ENGINE_VERSION=\\\"5.11.0-alpha.0\\\" -DENABLE_PLUGINS -DNOMINMAX -DSENTRY_ENVIRONMENT=\\\"development\\\" -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSTB_TEXTEDIT_KEYTYPE=unsigned -IC:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\include -IC:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\build\\Release -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\arcusee7666f716199\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\proto47a676cb9257b\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\zlibcfb9789dc2a53\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\spdlo2d4b540ab0f22\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\fmtdb20aad6e1bd4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\range0301bf3d76d5d\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\clippf8ddcd4a5d961\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbo34ee9aeb574f9\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbofe72fb50cf7c4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbo1b62b48748b82\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\rapidf7a3355ba53c4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\stb6342cecb318f5\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\boosta79d738920bc3\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\scrip4cfd014ad4660\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\nearg388d58da7a54c\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\curaebb245e64b5ac7\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\asio-c31dc2a380bf0\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\grpc8b6c73b4e5756\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\absei6a15444d60d47\\p\\include -external:W0 /DWIN32 /D_WINDOWS /EHsc /O2 /Ob2 /DNDEBUG -std:c++20 -MD /utf-8 /permissive- /FoCMakeFiles\\_CuraEngine.dir\\src\\geometry\\PartsView.cpp.obj /FdCMakeFiles\\_CuraEngine.dir\\_CuraEngine.pdb /FS -c C:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\src\\geometry\\PartsView.cpp", "file": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/src/geometry/PartsView.cpp", "output": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/build/Release/CMakeFiles/_CuraEngine.dir/src/geometry/PartsView.cpp.obj"}, {"directory": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/build/Release", "command": "C:\\PROGRA~1\\MICROS~1\\2022\\COMMUN~1\\VC\\Tools\\MSVC\\1444~1.352\\bin\\Hostx64\\x64\\cl.exe  /nologo /TP -DAGRPC_BOOST_ASIO -DARCUS -DBOOST_ALL_NO_LIB -DBOOST_STACKTRACE_USE_NOOP -DBOOST_STACKTRACE_USE_WINDBG -DBOOST_STACKTRACE_USE_WINDBG_CACHED -DCARES_STATICLIB -DCURA_ENGINE_HASH=\\\"unknown\\\" -DCURA_ENGINE_VERSION=\\\"5.11.0-alpha.0\\\" -DENABLE_PLUGINS -DNOMINMAX -DSENTRY_ENVIRONMENT=\\\"development\\\" -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSTB_TEXTEDIT_KEYTYPE=unsigned -IC:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\include -IC:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\build\\Release -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\arcusee7666f716199\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\proto47a676cb9257b\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\zlibcfb9789dc2a53\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\spdlo2d4b540ab0f22\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\fmtdb20aad6e1bd4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\range0301bf3d76d5d\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\clippf8ddcd4a5d961\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbo34ee9aeb574f9\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbofe72fb50cf7c4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbo1b62b48748b82\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\rapidf7a3355ba53c4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\stb6342cecb318f5\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\boosta79d738920bc3\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\scrip4cfd014ad4660\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\nearg388d58da7a54c\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\curaebb245e64b5ac7\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\asio-c31dc2a380bf0\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\grpc8b6c73b4e5756\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\absei6a15444d60d47\\p\\include -external:W0 /DWIN32 /D_WINDOWS /EHsc /O2 /Ob2 /DNDEBUG -std:c++20 -MD /utf-8 /permissive- /FoCMakeFiles\\_CuraEngine.dir\\src\\geometry\\LinesSet.cpp.obj /FdCMakeFiles\\_CuraEngine.dir\\_CuraEngine.pdb /FS -c C:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\src\\geometry\\LinesSet.cpp", "file": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/src/geometry/LinesSet.cpp", "output": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/build/Release/CMakeFiles/_CuraEngine.dir/src/geometry/LinesSet.cpp.obj"}, {"directory": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/build/Release", "command": "C:\\PROGRA~1\\MICROS~1\\2022\\COMMUN~1\\VC\\Tools\\MSVC\\1444~1.352\\bin\\Hostx64\\x64\\cl.exe  /nologo /TP -DAGRPC_BOOST_ASIO -DARCUS -DBOOST_ALL_NO_LIB -DBOOST_STACKTRACE_USE_NOOP -DBOOST_STACKTRACE_USE_WINDBG -DBOOST_STACKTRACE_USE_WINDBG_CACHED -DCARES_STATICLIB -DCURA_ENGINE_HASH=\\\"unknown\\\" -DCURA_ENGINE_VERSION=\\\"5.11.0-alpha.0\\\" -DENABLE_PLUGINS -DNOMINMAX -DSENTRY_ENVIRONMENT=\\\"development\\\" -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSTB_TEXTEDIT_KEYTYPE=unsigned -IC:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\include -IC:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\build\\Release -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\arcusee7666f716199\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\proto47a676cb9257b\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\zlibcfb9789dc2a53\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\spdlo2d4b540ab0f22\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\fmtdb20aad6e1bd4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\range0301bf3d76d5d\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\clippf8ddcd4a5d961\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbo34ee9aeb574f9\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbofe72fb50cf7c4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbo1b62b48748b82\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\rapidf7a3355ba53c4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\stb6342cecb318f5\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\boosta79d738920bc3\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\scrip4cfd014ad4660\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\nearg388d58da7a54c\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\curaebb245e64b5ac7\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\asio-c31dc2a380bf0\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\grpc8b6c73b4e5756\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\absei6a15444d60d47\\p\\include -external:W0 /DWIN32 /D_WINDOWS /EHsc /O2 /Ob2 /DNDEBUG -std:c++20 -MD /utf-8 /permissive- /FoCMakeFiles\\_CuraEngine.dir\\src\\geometry\\Polyline.cpp.obj /FdCMakeFiles\\_CuraEngine.dir\\_CuraEngine.pdb /FS -c C:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\src\\geometry\\Polyline.cpp", "file": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/src/geometry/Polyline.cpp", "output": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/build/Release/CMakeFiles/_CuraEngine.dir/src/geometry/Polyline.cpp.obj"}, {"directory": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/build/Release", "command": "C:\\PROGRA~1\\MICROS~1\\2022\\COMMUN~1\\VC\\Tools\\MSVC\\1444~1.352\\bin\\Hostx64\\x64\\cl.exe  /nologo /TP -DAGRPC_BOOST_ASIO -DARCUS -DBOOST_ALL_NO_LIB -DBOOST_STACKTRACE_USE_NOOP -DBOOST_STACKTRACE_USE_WINDBG -DBOOST_STACKTRACE_USE_WINDBG_CACHED -DCARES_STATICLIB -DCURA_ENGINE_HASH=\\\"unknown\\\" -DCURA_ENGINE_VERSION=\\\"5.11.0-alpha.0\\\" -DENABLE_PLUGINS -DNOMINMAX -DSENTRY_ENVIRONMENT=\\\"development\\\" -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSTB_TEXTEDIT_KEYTYPE=unsigned -IC:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\include -IC:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\build\\Release -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\arcusee7666f716199\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\proto47a676cb9257b\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\zlibcfb9789dc2a53\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\spdlo2d4b540ab0f22\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\fmtdb20aad6e1bd4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\range0301bf3d76d5d\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\clippf8ddcd4a5d961\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbo34ee9aeb574f9\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbofe72fb50cf7c4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbo1b62b48748b82\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\rapidf7a3355ba53c4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\stb6342cecb318f5\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\boosta79d738920bc3\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\scrip4cfd014ad4660\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\nearg388d58da7a54c\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\curaebb245e64b5ac7\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\asio-c31dc2a380bf0\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\grpc8b6c73b4e5756\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\absei6a15444d60d47\\p\\include -external:W0 /DWIN32 /D_WINDOWS /EHsc /O2 /Ob2 /DNDEBUG -std:c++20 -MD /utf-8 /permissive- /FoCMakeFiles\\_CuraEngine.dir\\src\\geometry\\ClosedPolyline.cpp.obj /FdCMakeFiles\\_CuraEngine.dir\\_CuraEngine.pdb /FS -c C:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\src\\geometry\\ClosedPolyline.cpp", "file": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/src/geometry/ClosedPolyline.cpp", "output": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/build/Release/CMakeFiles/_CuraEngine.dir/src/geometry/ClosedPolyline.cpp.obj"}, {"directory": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/build/Release", "command": "C:\\PROGRA~1\\MICROS~1\\2022\\COMMUN~1\\VC\\Tools\\MSVC\\1444~1.352\\bin\\Hostx64\\x64\\cl.exe  /nologo /TP -DAGRPC_BOOST_ASIO -DARCUS -DBOOST_ALL_NO_LIB -DBOOST_STACKTRACE_USE_NOOP -DBOOST_STACKTRACE_USE_WINDBG -DBOOST_STACKTRACE_USE_WINDBG_CACHED -DCARES_STATICLIB -DCURA_ENGINE_HASH=\\\"unknown\\\" -DCURA_ENGINE_VERSION=\\\"5.11.0-alpha.0\\\" -DENABLE_PLUGINS -DNOMINMAX -DSENTRY_ENVIRONMENT=\\\"development\\\" -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSTB_TEXTEDIT_KEYTYPE=unsigned -IC:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\include -IC:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\build\\Release -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\arcusee7666f716199\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\proto47a676cb9257b\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\zlibcfb9789dc2a53\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\spdlo2d4b540ab0f22\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\fmtdb20aad6e1bd4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\range0301bf3d76d5d\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\clippf8ddcd4a5d961\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbo34ee9aeb574f9\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbofe72fb50cf7c4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbo1b62b48748b82\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\rapidf7a3355ba53c4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\stb6342cecb318f5\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\boosta79d738920bc3\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\scrip4cfd014ad4660\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\nearg388d58da7a54c\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\curaebb245e64b5ac7\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\asio-c31dc2a380bf0\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\grpc8b6c73b4e5756\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\absei6a15444d60d47\\p\\include -external:W0 /DWIN32 /D_WINDOWS /EHsc /O2 /Ob2 /DNDEBUG -std:c++20 -MD /utf-8 /permissive- /FoCMakeFiles\\_CuraEngine.dir\\src\\geometry\\MixedLinesSet.cpp.obj /FdCMakeFiles\\_CuraEngine.dir\\_CuraEngine.pdb /FS -c C:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\src\\geometry\\MixedLinesSet.cpp", "file": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/src/geometry/MixedLinesSet.cpp", "output": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/build/Release/CMakeFiles/_CuraEngine.dir/src/geometry/MixedLinesSet.cpp.obj"}, {"directory": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/build/Release", "command": "C:\\PROGRA~1\\MICROS~1\\2022\\COMMUN~1\\VC\\Tools\\MSVC\\1444~1.352\\bin\\Hostx64\\x64\\cl.exe  /nologo /TP -DAGRPC_BOOST_ASIO -DARCUS -DBOOST_ALL_NO_LIB -DBOOST_STACKTRACE_USE_NOOP -DBOOST_STACKTRACE_USE_WINDBG -DBOOST_STACKTRACE_USE_WINDBG_CACHED -DCARES_STATICLIB -DCURA_ENGINE_HASH=\\\"unknown\\\" -DCURA_ENGINE_VERSION=\\\"5.11.0-alpha.0\\\" -DENABLE_PLUGINS -DNOMINMAX -DSENTRY_ENVIRONMENT=\\\"development\\\" -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSTB_TEXTEDIT_KEYTYPE=unsigned -IC:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\include -IC:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\build\\Release -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\arcusee7666f716199\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\proto47a676cb9257b\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\zlibcfb9789dc2a53\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\spdlo2d4b540ab0f22\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\fmtdb20aad6e1bd4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\range0301bf3d76d5d\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\clippf8ddcd4a5d961\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbo34ee9aeb574f9\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbofe72fb50cf7c4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbo1b62b48748b82\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\rapidf7a3355ba53c4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\stb6342cecb318f5\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\boosta79d738920bc3\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\scrip4cfd014ad4660\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\nearg388d58da7a54c\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\curaebb245e64b5ac7\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\asio-c31dc2a380bf0\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\grpc8b6c73b4e5756\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\absei6a15444d60d47\\p\\include -external:W0 /DWIN32 /D_WINDOWS /EHsc /O2 /Ob2 /DNDEBUG -std:c++20 -MD /utf-8 /permissive- /FoCMakeFiles\\_CuraEngine.dir\\src\\boost_exception_fix.cpp.obj /FdCMakeFiles\\_CuraEngine.dir\\_CuraEngine.pdb /FS -c C:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\src\\boost_exception_fix.cpp", "file": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/src/boost_exception_fix.cpp", "output": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/build/Release/CMakeFiles/_CuraEngine.dir/src/boost_exception_fix.cpp.obj"}, {"directory": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/build/Release", "command": "C:\\PROGRA~1\\MICROS~1\\2022\\COMMUN~1\\VC\\Tools\\MSVC\\1444~1.352\\bin\\Hostx64\\x64\\cl.exe  /nologo /TP -DAGRPC_BOOST_ASIO -DARCUS -DBOOST_ALL_NO_LIB -DBOOST_STACKTRACE_USE_NOOP -DBOOST_STACKTRACE_USE_WINDBG -DBOOST_STACKTRACE_USE_WINDBG_CACHED -DCARES_STATICLIB -DCURA_ENGINE_HASH=\\\"unknown\\\" -DCURA_ENGINE_VERSION=\\\"5.11.0-alpha.0\\\" -DENABLE_PLUGINS -DNOMINMAX -DSENTRY_ENVIRONMENT=\\\"development\\\" -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSTB_TEXTEDIT_KEYTYPE=unsigned -IC:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\include -IC:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\build\\Release -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\arcusee7666f716199\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\proto47a676cb9257b\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\zlibcfb9789dc2a53\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\spdlo2d4b540ab0f22\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\fmtdb20aad6e1bd4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\range0301bf3d76d5d\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\clippf8ddcd4a5d961\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbo34ee9aeb574f9\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbofe72fb50cf7c4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbo1b62b48748b82\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\rapidf7a3355ba53c4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\stb6342cecb318f5\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\boosta79d738920bc3\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\scrip4cfd014ad4660\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\nearg388d58da7a54c\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\curaebb245e64b5ac7\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\asio-c31dc2a380bf0\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\grpc8b6c73b4e5756\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\absei6a15444d60d47\\p\\include -external:W0 /DWIN32 /D_WINDOWS /EHsc /O2 /Ob2 /DNDEBUG -std:c++20 -MD /utf-8 /permissive- /FoCMakeFiles\\_CuraEngine.dir\\Cura.pb.cc.obj /FdCMakeFiles\\_CuraEngine.dir\\_CuraEngine.pdb /FS -c C:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\build\\Release\\Cura.pb.cc", "file": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/build/Release/Cura.pb.cc", "output": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/build/Release/CMakeFiles/_CuraEngine.dir/Cura.pb.cc.obj"}, {"directory": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/build/Release", "command": "C:\\PROGRA~1\\MICROS~1\\2022\\COMMUN~1\\VC\\Tools\\MSVC\\1444~1.352\\bin\\Hostx64\\x64\\cl.exe  /nologo /TP -DAGRPC_BOOST_ASIO -DARCUS -DBOOST_ALL_NO_LIB -DBOOST_STACKTRACE_USE_NOOP -DBOOST_STACKTRACE_USE_WINDBG -DBOOST_STACKTRACE_USE_WINDBG_CACHED -DCARES_STATICLIB -DCURA_ENGINE_HASH=\\\"unknown\\\" -DCURA_ENGINE_VERSION=\\\"5.11.0-alpha.0\\\" -DENABLE_PLUGINS -DSENTRY_ENVIRONMENT=\\\"development\\\" -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSTB_TEXTEDIT_KEYTYPE=unsigned -DVERSION=\\\"5.11.0-alpha.0\\\" -IC:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\arcusee7666f716199\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\proto47a676cb9257b\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\zlibcfb9789dc2a53\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\spdlo2d4b540ab0f22\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\fmtdb20aad6e1bd4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\range0301bf3d76d5d\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\clippf8ddcd4a5d961\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbo34ee9aeb574f9\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbofe72fb50cf7c4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\mapbo1b62b48748b82\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\rapidf7a3355ba53c4\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\stb6342cecb318f5\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\boosta79d738920bc3\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\scrip4cfd014ad4660\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\nearg388d58da7a54c\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\b\\curaebb245e64b5ac7\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\asio-c31dc2a380bf0\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\grpc8b6c73b4e5756\\p\\include -external:IC:\\Users\\<USER>\\.conan2\\p\\absei6a15444d60d47\\p\\include -external:W0 /DWIN32 /D_WINDOWS /EHsc /O2 /Ob2 /DNDEBUG -std:c++20 -MD /utf-8 /permissive- /FoCMakeFiles\\CuraEngine.dir\\src\\main.cpp.obj /FdCMakeFiles\\CuraEngine.dir\\ /FS -c C:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\src\\main.cpp", "file": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/src/main.cpp", "output": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/build/Release/CMakeFiles/CuraEngine.dir/src/main.cpp.obj"}, {"directory": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/build/Release", "command": "C:\\PROGRA~2\\WI3CF2~1\\10\\bin\\100261~1.0\\x64\\rc.exe -DAGRPC_BOOST_ASIO -DARCUS -DBOOST_ALL_NO_LIB -DBOOST_STACKTRACE_USE_NOOP -DBOOST_STACKTRACE_USE_WINDBG -DBOOST_STACKTRACE_USE_WINDBG_CACHED -DCARES_STATICLIB -DCURA_ENGINE_HASH=\\\"unknown\\\" -DCURA_ENGINE_VERSION=\\\"5.11.0-alpha.0\\\" -DENABLE_PLUGINS -DSENTRY_ENVIRONMENT=\\\"development\\\" -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSTB_TEXTEDIT_KEYTYPE=unsigned -DVERSION=\\\"5.11.0-alpha.0\\\" -I C:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\include -I C:\\Users\\<USER>\\.conan2\\p\\b\\arcusee7666f716199\\p\\include -I C:\\Users\\<USER>\\.conan2\\p\\proto47a676cb9257b\\p\\include -I C:\\Users\\<USER>\\.conan2\\p\\zlibcfb9789dc2a53\\p\\include -I C:\\Users\\<USER>\\.conan2\\p\\spdlo2d4b540ab0f22\\p\\include -I C:\\Users\\<USER>\\.conan2\\p\\fmtdb20aad6e1bd4\\p\\include -I C:\\Users\\<USER>\\.conan2\\p\\range0301bf3d76d5d\\p\\include -I C:\\Users\\<USER>\\.conan2\\p\\b\\clippf8ddcd4a5d961\\p\\include -I C:\\Users\\<USER>\\.conan2\\p\\mapbo34ee9aeb574f9\\p\\include -I C:\\Users\\<USER>\\.conan2\\p\\mapbofe72fb50cf7c4\\p\\include -I C:\\Users\\<USER>\\.conan2\\p\\mapbo1b62b48748b82\\p\\include -I C:\\Users\\<USER>\\.conan2\\p\\rapidf7a3355ba53c4\\p\\include -I C:\\Users\\<USER>\\.conan2\\p\\stb6342cecb318f5\\p\\include -I C:\\Users\\<USER>\\.conan2\\p\\boosta79d738920bc3\\p\\include -I C:\\Users\\<USER>\\.conan2\\p\\scrip4cfd014ad4660\\p\\include -I C:\\Users\\<USER>\\.conan2\\p\\nearg388d58da7a54c\\p\\include -I C:\\Users\\<USER>\\.conan2\\p\\b\\curaebb245e64b5ac7\\p\\include -I C:\\Users\\<USER>\\.conan2\\p\\asio-c31dc2a380bf0\\p\\include -I C:\\Users\\<USER>\\.conan2\\p\\grpc8b6c73b4e5756\\p\\include -I C:\\Users\\<USER>\\.conan2\\p\\absei6a15444d60d47\\p\\include -DWIN32 /fo CMakeFiles\\CuraEngine.dir\\CuraEngine.rc.res C:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\CuraEngine.rc", "file": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/CuraEngine.rc", "output": "C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/build/Release/CMakeFiles/CuraEngine.dir/CuraEngine.rc.res"}]
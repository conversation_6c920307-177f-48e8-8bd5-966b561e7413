@echo off
echo ========================================
echo CuraEngine Quick Build Script
echo (Use this when dependencies are already installed)
echo ========================================

REM Check if we are in the correct directory
if not exist "conanfile.py" (
    echo ERROR: Please run this script from CuraEngine root directory
    pause
    exit /b 1
)

echo Setting up Visual Studio 2022 environment...
call "C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\Build\vcvars64.bat"

echo Building CuraEngine...
cmake --build build\Release --config Release

if %errorlevel% neq 0 (
    echo ERROR: Build failed
    pause
    exit /b 1
)

echo ========================================
echo Build completed successfully!
echo CuraEngine.exe location: build\Release\CuraEngine.exe
echo ========================================

REM Test the executable
echo Testing CuraEngine...
build\Release\CuraEngine.exe --help

pause

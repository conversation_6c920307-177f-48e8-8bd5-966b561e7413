// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: Cura.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_Cura_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_Cura_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3021000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3021012 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_bases.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata_lite.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/generated_enum_reflection.h>
#include <google/protobuf/unknown_field_set.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_Cura_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_Cura_2eproto {
  static const uint32_t offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_Cura_2eproto;
namespace cura {
namespace proto {
class EnginePlugin;
struct EnginePluginDefaultTypeInternal;
extern EnginePluginDefaultTypeInternal _EnginePlugin_default_instance_;
class Extruder;
struct ExtruderDefaultTypeInternal;
extern ExtruderDefaultTypeInternal _Extruder_default_instance_;
class GCodeLayer;
struct GCodeLayerDefaultTypeInternal;
extern GCodeLayerDefaultTypeInternal _GCodeLayer_default_instance_;
class GCodePrefix;
struct GCodePrefixDefaultTypeInternal;
extern GCodePrefixDefaultTypeInternal _GCodePrefix_default_instance_;
class Layer;
struct LayerDefaultTypeInternal;
extern LayerDefaultTypeInternal _Layer_default_instance_;
class LayerOptimized;
struct LayerOptimizedDefaultTypeInternal;
extern LayerOptimizedDefaultTypeInternal _LayerOptimized_default_instance_;
class MaterialEstimates;
struct MaterialEstimatesDefaultTypeInternal;
extern MaterialEstimatesDefaultTypeInternal _MaterialEstimates_default_instance_;
class Object;
struct ObjectDefaultTypeInternal;
extern ObjectDefaultTypeInternal _Object_default_instance_;
class ObjectList;
struct ObjectListDefaultTypeInternal;
extern ObjectListDefaultTypeInternal _ObjectList_default_instance_;
class PathSegment;
struct PathSegmentDefaultTypeInternal;
extern PathSegmentDefaultTypeInternal _PathSegment_default_instance_;
class Polygon;
struct PolygonDefaultTypeInternal;
extern PolygonDefaultTypeInternal _Polygon_default_instance_;
class PrintTimeMaterialEstimates;
struct PrintTimeMaterialEstimatesDefaultTypeInternal;
extern PrintTimeMaterialEstimatesDefaultTypeInternal _PrintTimeMaterialEstimates_default_instance_;
class Progress;
struct ProgressDefaultTypeInternal;
extern ProgressDefaultTypeInternal _Progress_default_instance_;
class Setting;
struct SettingDefaultTypeInternal;
extern SettingDefaultTypeInternal _Setting_default_instance_;
class SettingExtruder;
struct SettingExtruderDefaultTypeInternal;
extern SettingExtruderDefaultTypeInternal _SettingExtruder_default_instance_;
class SettingList;
struct SettingListDefaultTypeInternal;
extern SettingListDefaultTypeInternal _SettingList_default_instance_;
class Slice;
struct SliceDefaultTypeInternal;
extern SliceDefaultTypeInternal _Slice_default_instance_;
class SliceUUID;
struct SliceUUIDDefaultTypeInternal;
extern SliceUUIDDefaultTypeInternal _SliceUUID_default_instance_;
class SlicingFinished;
struct SlicingFinishedDefaultTypeInternal;
extern SlicingFinishedDefaultTypeInternal _SlicingFinished_default_instance_;
}  // namespace proto
}  // namespace cura
PROTOBUF_NAMESPACE_OPEN
template<> ::cura::proto::EnginePlugin* Arena::CreateMaybeMessage<::cura::proto::EnginePlugin>(Arena*);
template<> ::cura::proto::Extruder* Arena::CreateMaybeMessage<::cura::proto::Extruder>(Arena*);
template<> ::cura::proto::GCodeLayer* Arena::CreateMaybeMessage<::cura::proto::GCodeLayer>(Arena*);
template<> ::cura::proto::GCodePrefix* Arena::CreateMaybeMessage<::cura::proto::GCodePrefix>(Arena*);
template<> ::cura::proto::Layer* Arena::CreateMaybeMessage<::cura::proto::Layer>(Arena*);
template<> ::cura::proto::LayerOptimized* Arena::CreateMaybeMessage<::cura::proto::LayerOptimized>(Arena*);
template<> ::cura::proto::MaterialEstimates* Arena::CreateMaybeMessage<::cura::proto::MaterialEstimates>(Arena*);
template<> ::cura::proto::Object* Arena::CreateMaybeMessage<::cura::proto::Object>(Arena*);
template<> ::cura::proto::ObjectList* Arena::CreateMaybeMessage<::cura::proto::ObjectList>(Arena*);
template<> ::cura::proto::PathSegment* Arena::CreateMaybeMessage<::cura::proto::PathSegment>(Arena*);
template<> ::cura::proto::Polygon* Arena::CreateMaybeMessage<::cura::proto::Polygon>(Arena*);
template<> ::cura::proto::PrintTimeMaterialEstimates* Arena::CreateMaybeMessage<::cura::proto::PrintTimeMaterialEstimates>(Arena*);
template<> ::cura::proto::Progress* Arena::CreateMaybeMessage<::cura::proto::Progress>(Arena*);
template<> ::cura::proto::Setting* Arena::CreateMaybeMessage<::cura::proto::Setting>(Arena*);
template<> ::cura::proto::SettingExtruder* Arena::CreateMaybeMessage<::cura::proto::SettingExtruder>(Arena*);
template<> ::cura::proto::SettingList* Arena::CreateMaybeMessage<::cura::proto::SettingList>(Arena*);
template<> ::cura::proto::Slice* Arena::CreateMaybeMessage<::cura::proto::Slice>(Arena*);
template<> ::cura::proto::SliceUUID* Arena::CreateMaybeMessage<::cura::proto::SliceUUID>(Arena*);
template<> ::cura::proto::SlicingFinished* Arena::CreateMaybeMessage<::cura::proto::SlicingFinished>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace cura {
namespace proto {

enum Polygon_Type : int {
  Polygon_Type_NoneType = 0,
  Polygon_Type_Inset0Type = 1,
  Polygon_Type_InsetXType = 2,
  Polygon_Type_SkinType = 3,
  Polygon_Type_SupportType = 4,
  Polygon_Type_SkirtType = 5,
  Polygon_Type_InfillType = 6,
  Polygon_Type_SupportInfillType = 7,
  Polygon_Type_MoveUnretracted = 8,
  Polygon_Type_MoveRetracted = 9,
  Polygon_Type_SupportInterfaceType = 10,
  Polygon_Type_PrimeTowerType = 11,
  Polygon_Type_MoveWhileRetracting = 12,
  Polygon_Type_MoveWhileUnretracting = 13,
  Polygon_Type_StationaryRetractUnretract = 14,
  Polygon_Type_NumPrintFeatureTypes = 15,
  Polygon_Type_Polygon_Type_INT_MIN_SENTINEL_DO_NOT_USE_ = std::numeric_limits<int32_t>::min(),
  Polygon_Type_Polygon_Type_INT_MAX_SENTINEL_DO_NOT_USE_ = std::numeric_limits<int32_t>::max()
};
bool Polygon_Type_IsValid(int value);
constexpr Polygon_Type Polygon_Type_Type_MIN = Polygon_Type_NoneType;
constexpr Polygon_Type Polygon_Type_Type_MAX = Polygon_Type_NumPrintFeatureTypes;
constexpr int Polygon_Type_Type_ARRAYSIZE = Polygon_Type_Type_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* Polygon_Type_descriptor();
template<typename T>
inline const std::string& Polygon_Type_Name(T enum_t_value) {
  static_assert(::std::is_same<T, Polygon_Type>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function Polygon_Type_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    Polygon_Type_descriptor(), enum_t_value);
}
inline bool Polygon_Type_Parse(
    ::PROTOBUF_NAMESPACE_ID::ConstStringParam name, Polygon_Type* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<Polygon_Type>(
    Polygon_Type_descriptor(), name, value);
}
enum PathSegment_PointType : int {
  PathSegment_PointType_Point2D = 0,
  PathSegment_PointType_Point3D = 1,
  PathSegment_PointType_PathSegment_PointType_INT_MIN_SENTINEL_DO_NOT_USE_ = std::numeric_limits<int32_t>::min(),
  PathSegment_PointType_PathSegment_PointType_INT_MAX_SENTINEL_DO_NOT_USE_ = std::numeric_limits<int32_t>::max()
};
bool PathSegment_PointType_IsValid(int value);
constexpr PathSegment_PointType PathSegment_PointType_PointType_MIN = PathSegment_PointType_Point2D;
constexpr PathSegment_PointType PathSegment_PointType_PointType_MAX = PathSegment_PointType_Point3D;
constexpr int PathSegment_PointType_PointType_ARRAYSIZE = PathSegment_PointType_PointType_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* PathSegment_PointType_descriptor();
template<typename T>
inline const std::string& PathSegment_PointType_Name(T enum_t_value) {
  static_assert(::std::is_same<T, PathSegment_PointType>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function PathSegment_PointType_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    PathSegment_PointType_descriptor(), enum_t_value);
}
inline bool PathSegment_PointType_Parse(
    ::PROTOBUF_NAMESPACE_ID::ConstStringParam name, PathSegment_PointType* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<PathSegment_PointType>(
    PathSegment_PointType_descriptor(), name, value);
}
enum SlotID : int {
  SETTINGS_BROADCAST = 0,
  SIMPLIFY_MODIFY = 100,
  POSTPROCESS_MODIFY = 101,
  INFILL_MODIFY = 102,
  GCODE_PATHS_MODIFY = 103,
  INFILL_GENERATE = 200,
  SlotID_INT_MIN_SENTINEL_DO_NOT_USE_ = std::numeric_limits<int32_t>::min(),
  SlotID_INT_MAX_SENTINEL_DO_NOT_USE_ = std::numeric_limits<int32_t>::max()
};
bool SlotID_IsValid(int value);
constexpr SlotID SlotID_MIN = SETTINGS_BROADCAST;
constexpr SlotID SlotID_MAX = INFILL_GENERATE;
constexpr int SlotID_ARRAYSIZE = SlotID_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* SlotID_descriptor();
template<typename T>
inline const std::string& SlotID_Name(T enum_t_value) {
  static_assert(::std::is_same<T, SlotID>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function SlotID_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    SlotID_descriptor(), enum_t_value);
}
inline bool SlotID_Parse(
    ::PROTOBUF_NAMESPACE_ID::ConstStringParam name, SlotID* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<SlotID>(
    SlotID_descriptor(), name, value);
}
// ===================================================================

class ObjectList final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:cura.proto.ObjectList) */ {
 public:
  inline ObjectList() : ObjectList(nullptr) {}
  ~ObjectList() override;
  explicit PROTOBUF_CONSTEXPR ObjectList(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  ObjectList(const ObjectList& from);
  ObjectList(ObjectList&& from) noexcept
    : ObjectList() {
    *this = ::std::move(from);
  }

  inline ObjectList& operator=(const ObjectList& from) {
    CopyFrom(from);
    return *this;
  }
  inline ObjectList& operator=(ObjectList&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const ObjectList& default_instance() {
    return *internal_default_instance();
  }
  static inline const ObjectList* internal_default_instance() {
    return reinterpret_cast<const ObjectList*>(
               &_ObjectList_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(ObjectList& a, ObjectList& b) {
    a.Swap(&b);
  }
  inline void Swap(ObjectList* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(ObjectList* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  ObjectList* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<ObjectList>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const ObjectList& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const ObjectList& from) {
    ObjectList::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(ObjectList* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "cura.proto.ObjectList";
  }
  protected:
  explicit ObjectList(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kObjectsFieldNumber = 1,
    kSettingsFieldNumber = 2,
  };
  // repeated .cura.proto.Object objects = 1;
  int objects_size() const;
  private:
  int _internal_objects_size() const;
  public:
  void clear_objects();
  ::cura::proto::Object* mutable_objects(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::cura::proto::Object >*
      mutable_objects();
  private:
  const ::cura::proto::Object& _internal_objects(int index) const;
  ::cura::proto::Object* _internal_add_objects();
  public:
  const ::cura::proto::Object& objects(int index) const;
  ::cura::proto::Object* add_objects();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::cura::proto::Object >&
      objects() const;

  // repeated .cura.proto.Setting settings = 2;
  int settings_size() const;
  private:
  int _internal_settings_size() const;
  public:
  void clear_settings();
  ::cura::proto::Setting* mutable_settings(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::cura::proto::Setting >*
      mutable_settings();
  private:
  const ::cura::proto::Setting& _internal_settings(int index) const;
  ::cura::proto::Setting* _internal_add_settings();
  public:
  const ::cura::proto::Setting& settings(int index) const;
  ::cura::proto::Setting* add_settings();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::cura::proto::Setting >&
      settings() const;

  // @@protoc_insertion_point(class_scope:cura.proto.ObjectList)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::cura::proto::Object > objects_;
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::cura::proto::Setting > settings_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_Cura_2eproto;
};
// -------------------------------------------------------------------

class EnginePlugin final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:cura.proto.EnginePlugin) */ {
 public:
  inline EnginePlugin() : EnginePlugin(nullptr) {}
  ~EnginePlugin() override;
  explicit PROTOBUF_CONSTEXPR EnginePlugin(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  EnginePlugin(const EnginePlugin& from);
  EnginePlugin(EnginePlugin&& from) noexcept
    : EnginePlugin() {
    *this = ::std::move(from);
  }

  inline EnginePlugin& operator=(const EnginePlugin& from) {
    CopyFrom(from);
    return *this;
  }
  inline EnginePlugin& operator=(EnginePlugin&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const EnginePlugin& default_instance() {
    return *internal_default_instance();
  }
  static inline const EnginePlugin* internal_default_instance() {
    return reinterpret_cast<const EnginePlugin*>(
               &_EnginePlugin_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    1;

  friend void swap(EnginePlugin& a, EnginePlugin& b) {
    a.Swap(&b);
  }
  inline void Swap(EnginePlugin* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(EnginePlugin* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  EnginePlugin* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<EnginePlugin>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const EnginePlugin& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const EnginePlugin& from) {
    EnginePlugin::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(EnginePlugin* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "cura.proto.EnginePlugin";
  }
  protected:
  explicit EnginePlugin(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kAddressFieldNumber = 2,
    kPluginNameFieldNumber = 4,
    kPluginVersionFieldNumber = 5,
    kIdFieldNumber = 1,
    kPortFieldNumber = 3,
  };
  // string address = 2;
  void clear_address();
  const std::string& address() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_address(ArgT0&& arg0, ArgT... args);
  std::string* mutable_address();
  PROTOBUF_NODISCARD std::string* release_address();
  void set_allocated_address(std::string* address);
  private:
  const std::string& _internal_address() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_address(const std::string& value);
  std::string* _internal_mutable_address();
  public:

  // string plugin_name = 4;
  void clear_plugin_name();
  const std::string& plugin_name() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_plugin_name(ArgT0&& arg0, ArgT... args);
  std::string* mutable_plugin_name();
  PROTOBUF_NODISCARD std::string* release_plugin_name();
  void set_allocated_plugin_name(std::string* plugin_name);
  private:
  const std::string& _internal_plugin_name() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_plugin_name(const std::string& value);
  std::string* _internal_mutable_plugin_name();
  public:

  // string plugin_version = 5;
  void clear_plugin_version();
  const std::string& plugin_version() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_plugin_version(ArgT0&& arg0, ArgT... args);
  std::string* mutable_plugin_version();
  PROTOBUF_NODISCARD std::string* release_plugin_version();
  void set_allocated_plugin_version(std::string* plugin_version);
  private:
  const std::string& _internal_plugin_version() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_plugin_version(const std::string& value);
  std::string* _internal_mutable_plugin_version();
  public:

  // .cura.proto.SlotID id = 1;
  void clear_id();
  ::cura::proto::SlotID id() const;
  void set_id(::cura::proto::SlotID value);
  private:
  ::cura::proto::SlotID _internal_id() const;
  void _internal_set_id(::cura::proto::SlotID value);
  public:

  // uint32 port = 3;
  void clear_port();
  uint32_t port() const;
  void set_port(uint32_t value);
  private:
  uint32_t _internal_port() const;
  void _internal_set_port(uint32_t value);
  public:

  // @@protoc_insertion_point(class_scope:cura.proto.EnginePlugin)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr address_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr plugin_name_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr plugin_version_;
    int id_;
    uint32_t port_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_Cura_2eproto;
};
// -------------------------------------------------------------------

class Slice final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:cura.proto.Slice) */ {
 public:
  inline Slice() : Slice(nullptr) {}
  ~Slice() override;
  explicit PROTOBUF_CONSTEXPR Slice(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  Slice(const Slice& from);
  Slice(Slice&& from) noexcept
    : Slice() {
    *this = ::std::move(from);
  }

  inline Slice& operator=(const Slice& from) {
    CopyFrom(from);
    return *this;
  }
  inline Slice& operator=(Slice&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const Slice& default_instance() {
    return *internal_default_instance();
  }
  static inline const Slice* internal_default_instance() {
    return reinterpret_cast<const Slice*>(
               &_Slice_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    2;

  friend void swap(Slice& a, Slice& b) {
    a.Swap(&b);
  }
  inline void Swap(Slice* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(Slice* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  Slice* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<Slice>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const Slice& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const Slice& from) {
    Slice::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(Slice* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "cura.proto.Slice";
  }
  protected:
  explicit Slice(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kObjectListsFieldNumber = 1,
    kExtrudersFieldNumber = 3,
    kLimitToExtruderFieldNumber = 4,
    kEnginePluginsFieldNumber = 5,
    kSentryIdFieldNumber = 6,
    kCuraVersionFieldNumber = 7,
    kProjectNameFieldNumber = 8,
    kUserNameFieldNumber = 9,
    kGlobalSettingsFieldNumber = 2,
  };
  // repeated .cura.proto.ObjectList object_lists = 1;
  int object_lists_size() const;
  private:
  int _internal_object_lists_size() const;
  public:
  void clear_object_lists();
  ::cura::proto::ObjectList* mutable_object_lists(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::cura::proto::ObjectList >*
      mutable_object_lists();
  private:
  const ::cura::proto::ObjectList& _internal_object_lists(int index) const;
  ::cura::proto::ObjectList* _internal_add_object_lists();
  public:
  const ::cura::proto::ObjectList& object_lists(int index) const;
  ::cura::proto::ObjectList* add_object_lists();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::cura::proto::ObjectList >&
      object_lists() const;

  // repeated .cura.proto.Extruder extruders = 3;
  int extruders_size() const;
  private:
  int _internal_extruders_size() const;
  public:
  void clear_extruders();
  ::cura::proto::Extruder* mutable_extruders(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::cura::proto::Extruder >*
      mutable_extruders();
  private:
  const ::cura::proto::Extruder& _internal_extruders(int index) const;
  ::cura::proto::Extruder* _internal_add_extruders();
  public:
  const ::cura::proto::Extruder& extruders(int index) const;
  ::cura::proto::Extruder* add_extruders();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::cura::proto::Extruder >&
      extruders() const;

  // repeated .cura.proto.SettingExtruder limit_to_extruder = 4;
  int limit_to_extruder_size() const;
  private:
  int _internal_limit_to_extruder_size() const;
  public:
  void clear_limit_to_extruder();
  ::cura::proto::SettingExtruder* mutable_limit_to_extruder(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::cura::proto::SettingExtruder >*
      mutable_limit_to_extruder();
  private:
  const ::cura::proto::SettingExtruder& _internal_limit_to_extruder(int index) const;
  ::cura::proto::SettingExtruder* _internal_add_limit_to_extruder();
  public:
  const ::cura::proto::SettingExtruder& limit_to_extruder(int index) const;
  ::cura::proto::SettingExtruder* add_limit_to_extruder();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::cura::proto::SettingExtruder >&
      limit_to_extruder() const;

  // repeated .cura.proto.EnginePlugin engine_plugins = 5;
  int engine_plugins_size() const;
  private:
  int _internal_engine_plugins_size() const;
  public:
  void clear_engine_plugins();
  ::cura::proto::EnginePlugin* mutable_engine_plugins(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::cura::proto::EnginePlugin >*
      mutable_engine_plugins();
  private:
  const ::cura::proto::EnginePlugin& _internal_engine_plugins(int index) const;
  ::cura::proto::EnginePlugin* _internal_add_engine_plugins();
  public:
  const ::cura::proto::EnginePlugin& engine_plugins(int index) const;
  ::cura::proto::EnginePlugin* add_engine_plugins();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::cura::proto::EnginePlugin >&
      engine_plugins() const;

  // string sentry_id = 6;
  void clear_sentry_id();
  const std::string& sentry_id() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_sentry_id(ArgT0&& arg0, ArgT... args);
  std::string* mutable_sentry_id();
  PROTOBUF_NODISCARD std::string* release_sentry_id();
  void set_allocated_sentry_id(std::string* sentry_id);
  private:
  const std::string& _internal_sentry_id() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_sentry_id(const std::string& value);
  std::string* _internal_mutable_sentry_id();
  public:

  // string cura_version = 7;
  void clear_cura_version();
  const std::string& cura_version() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_cura_version(ArgT0&& arg0, ArgT... args);
  std::string* mutable_cura_version();
  PROTOBUF_NODISCARD std::string* release_cura_version();
  void set_allocated_cura_version(std::string* cura_version);
  private:
  const std::string& _internal_cura_version() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_cura_version(const std::string& value);
  std::string* _internal_mutable_cura_version();
  public:

  // optional string project_name = 8;
  bool has_project_name() const;
  private:
  bool _internal_has_project_name() const;
  public:
  void clear_project_name();
  const std::string& project_name() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_project_name(ArgT0&& arg0, ArgT... args);
  std::string* mutable_project_name();
  PROTOBUF_NODISCARD std::string* release_project_name();
  void set_allocated_project_name(std::string* project_name);
  private:
  const std::string& _internal_project_name() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_project_name(const std::string& value);
  std::string* _internal_mutable_project_name();
  public:

  // optional string user_name = 9;
  bool has_user_name() const;
  private:
  bool _internal_has_user_name() const;
  public:
  void clear_user_name();
  const std::string& user_name() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_user_name(ArgT0&& arg0, ArgT... args);
  std::string* mutable_user_name();
  PROTOBUF_NODISCARD std::string* release_user_name();
  void set_allocated_user_name(std::string* user_name);
  private:
  const std::string& _internal_user_name() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_user_name(const std::string& value);
  std::string* _internal_mutable_user_name();
  public:

  // .cura.proto.SettingList global_settings = 2;
  bool has_global_settings() const;
  private:
  bool _internal_has_global_settings() const;
  public:
  void clear_global_settings();
  const ::cura::proto::SettingList& global_settings() const;
  PROTOBUF_NODISCARD ::cura::proto::SettingList* release_global_settings();
  ::cura::proto::SettingList* mutable_global_settings();
  void set_allocated_global_settings(::cura::proto::SettingList* global_settings);
  private:
  const ::cura::proto::SettingList& _internal_global_settings() const;
  ::cura::proto::SettingList* _internal_mutable_global_settings();
  public:
  void unsafe_arena_set_allocated_global_settings(
      ::cura::proto::SettingList* global_settings);
  ::cura::proto::SettingList* unsafe_arena_release_global_settings();

  // @@protoc_insertion_point(class_scope:cura.proto.Slice)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::internal::HasBits<1> _has_bits_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::cura::proto::ObjectList > object_lists_;
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::cura::proto::Extruder > extruders_;
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::cura::proto::SettingExtruder > limit_to_extruder_;
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::cura::proto::EnginePlugin > engine_plugins_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr sentry_id_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr cura_version_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr project_name_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr user_name_;
    ::cura::proto::SettingList* global_settings_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_Cura_2eproto;
};
// -------------------------------------------------------------------

class Extruder final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:cura.proto.Extruder) */ {
 public:
  inline Extruder() : Extruder(nullptr) {}
  ~Extruder() override;
  explicit PROTOBUF_CONSTEXPR Extruder(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  Extruder(const Extruder& from);
  Extruder(Extruder&& from) noexcept
    : Extruder() {
    *this = ::std::move(from);
  }

  inline Extruder& operator=(const Extruder& from) {
    CopyFrom(from);
    return *this;
  }
  inline Extruder& operator=(Extruder&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const Extruder& default_instance() {
    return *internal_default_instance();
  }
  static inline const Extruder* internal_default_instance() {
    return reinterpret_cast<const Extruder*>(
               &_Extruder_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    3;

  friend void swap(Extruder& a, Extruder& b) {
    a.Swap(&b);
  }
  inline void Swap(Extruder* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(Extruder* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  Extruder* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<Extruder>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const Extruder& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const Extruder& from) {
    Extruder::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(Extruder* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "cura.proto.Extruder";
  }
  protected:
  explicit Extruder(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kSettingsFieldNumber = 2,
    kIdFieldNumber = 1,
  };
  // .cura.proto.SettingList settings = 2;
  bool has_settings() const;
  private:
  bool _internal_has_settings() const;
  public:
  void clear_settings();
  const ::cura::proto::SettingList& settings() const;
  PROTOBUF_NODISCARD ::cura::proto::SettingList* release_settings();
  ::cura::proto::SettingList* mutable_settings();
  void set_allocated_settings(::cura::proto::SettingList* settings);
  private:
  const ::cura::proto::SettingList& _internal_settings() const;
  ::cura::proto::SettingList* _internal_mutable_settings();
  public:
  void unsafe_arena_set_allocated_settings(
      ::cura::proto::SettingList* settings);
  ::cura::proto::SettingList* unsafe_arena_release_settings();

  // int32 id = 1;
  void clear_id();
  int32_t id() const;
  void set_id(int32_t value);
  private:
  int32_t _internal_id() const;
  void _internal_set_id(int32_t value);
  public:

  // @@protoc_insertion_point(class_scope:cura.proto.Extruder)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::cura::proto::SettingList* settings_;
    int32_t id_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_Cura_2eproto;
};
// -------------------------------------------------------------------

class Object final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:cura.proto.Object) */ {
 public:
  inline Object() : Object(nullptr) {}
  ~Object() override;
  explicit PROTOBUF_CONSTEXPR Object(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  Object(const Object& from);
  Object(Object&& from) noexcept
    : Object() {
    *this = ::std::move(from);
  }

  inline Object& operator=(const Object& from) {
    CopyFrom(from);
    return *this;
  }
  inline Object& operator=(Object&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const Object& default_instance() {
    return *internal_default_instance();
  }
  static inline const Object* internal_default_instance() {
    return reinterpret_cast<const Object*>(
               &_Object_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    4;

  friend void swap(Object& a, Object& b) {
    a.Swap(&b);
  }
  inline void Swap(Object* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(Object* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  Object* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<Object>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const Object& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const Object& from) {
    Object::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(Object* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "cura.proto.Object";
  }
  protected:
  explicit Object(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kSettingsFieldNumber = 5,
    kVerticesFieldNumber = 2,
    kNormalsFieldNumber = 3,
    kIndicesFieldNumber = 4,
    kNameFieldNumber = 6,
    kIdFieldNumber = 1,
  };
  // repeated .cura.proto.Setting settings = 5;
  int settings_size() const;
  private:
  int _internal_settings_size() const;
  public:
  void clear_settings();
  ::cura::proto::Setting* mutable_settings(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::cura::proto::Setting >*
      mutable_settings();
  private:
  const ::cura::proto::Setting& _internal_settings(int index) const;
  ::cura::proto::Setting* _internal_add_settings();
  public:
  const ::cura::proto::Setting& settings(int index) const;
  ::cura::proto::Setting* add_settings();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::cura::proto::Setting >&
      settings() const;

  // bytes vertices = 2;
  void clear_vertices();
  const std::string& vertices() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_vertices(ArgT0&& arg0, ArgT... args);
  std::string* mutable_vertices();
  PROTOBUF_NODISCARD std::string* release_vertices();
  void set_allocated_vertices(std::string* vertices);
  private:
  const std::string& _internal_vertices() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_vertices(const std::string& value);
  std::string* _internal_mutable_vertices();
  public:

  // bytes normals = 3;
  void clear_normals();
  const std::string& normals() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_normals(ArgT0&& arg0, ArgT... args);
  std::string* mutable_normals();
  PROTOBUF_NODISCARD std::string* release_normals();
  void set_allocated_normals(std::string* normals);
  private:
  const std::string& _internal_normals() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_normals(const std::string& value);
  std::string* _internal_mutable_normals();
  public:

  // bytes indices = 4;
  void clear_indices();
  const std::string& indices() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_indices(ArgT0&& arg0, ArgT... args);
  std::string* mutable_indices();
  PROTOBUF_NODISCARD std::string* release_indices();
  void set_allocated_indices(std::string* indices);
  private:
  const std::string& _internal_indices() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_indices(const std::string& value);
  std::string* _internal_mutable_indices();
  public:

  // string name = 6;
  void clear_name();
  const std::string& name() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_name(ArgT0&& arg0, ArgT... args);
  std::string* mutable_name();
  PROTOBUF_NODISCARD std::string* release_name();
  void set_allocated_name(std::string* name);
  private:
  const std::string& _internal_name() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_name(const std::string& value);
  std::string* _internal_mutable_name();
  public:

  // int64 id = 1;
  void clear_id();
  int64_t id() const;
  void set_id(int64_t value);
  private:
  int64_t _internal_id() const;
  void _internal_set_id(int64_t value);
  public:

  // @@protoc_insertion_point(class_scope:cura.proto.Object)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::cura::proto::Setting > settings_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr vertices_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr normals_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr indices_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr name_;
    int64_t id_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_Cura_2eproto;
};
// -------------------------------------------------------------------

class Progress final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:cura.proto.Progress) */ {
 public:
  inline Progress() : Progress(nullptr) {}
  ~Progress() override;
  explicit PROTOBUF_CONSTEXPR Progress(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  Progress(const Progress& from);
  Progress(Progress&& from) noexcept
    : Progress() {
    *this = ::std::move(from);
  }

  inline Progress& operator=(const Progress& from) {
    CopyFrom(from);
    return *this;
  }
  inline Progress& operator=(Progress&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const Progress& default_instance() {
    return *internal_default_instance();
  }
  static inline const Progress* internal_default_instance() {
    return reinterpret_cast<const Progress*>(
               &_Progress_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    5;

  friend void swap(Progress& a, Progress& b) {
    a.Swap(&b);
  }
  inline void Swap(Progress* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(Progress* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  Progress* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<Progress>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const Progress& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const Progress& from) {
    Progress::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(Progress* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "cura.proto.Progress";
  }
  protected:
  explicit Progress(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kAmountFieldNumber = 1,
  };
  // float amount = 1;
  void clear_amount();
  float amount() const;
  void set_amount(float value);
  private:
  float _internal_amount() const;
  void _internal_set_amount(float value);
  public:

  // @@protoc_insertion_point(class_scope:cura.proto.Progress)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    float amount_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_Cura_2eproto;
};
// -------------------------------------------------------------------

class Layer final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:cura.proto.Layer) */ {
 public:
  inline Layer() : Layer(nullptr) {}
  ~Layer() override;
  explicit PROTOBUF_CONSTEXPR Layer(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  Layer(const Layer& from);
  Layer(Layer&& from) noexcept
    : Layer() {
    *this = ::std::move(from);
  }

  inline Layer& operator=(const Layer& from) {
    CopyFrom(from);
    return *this;
  }
  inline Layer& operator=(Layer&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const Layer& default_instance() {
    return *internal_default_instance();
  }
  static inline const Layer* internal_default_instance() {
    return reinterpret_cast<const Layer*>(
               &_Layer_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    6;

  friend void swap(Layer& a, Layer& b) {
    a.Swap(&b);
  }
  inline void Swap(Layer* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(Layer* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  Layer* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<Layer>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const Layer& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const Layer& from) {
    Layer::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(Layer* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "cura.proto.Layer";
  }
  protected:
  explicit Layer(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kPolygonsFieldNumber = 4,
    kIdFieldNumber = 1,
    kHeightFieldNumber = 2,
    kThicknessFieldNumber = 3,
  };
  // repeated .cura.proto.Polygon polygons = 4;
  int polygons_size() const;
  private:
  int _internal_polygons_size() const;
  public:
  void clear_polygons();
  ::cura::proto::Polygon* mutable_polygons(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::cura::proto::Polygon >*
      mutable_polygons();
  private:
  const ::cura::proto::Polygon& _internal_polygons(int index) const;
  ::cura::proto::Polygon* _internal_add_polygons();
  public:
  const ::cura::proto::Polygon& polygons(int index) const;
  ::cura::proto::Polygon* add_polygons();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::cura::proto::Polygon >&
      polygons() const;

  // int32 id = 1;
  void clear_id();
  int32_t id() const;
  void set_id(int32_t value);
  private:
  int32_t _internal_id() const;
  void _internal_set_id(int32_t value);
  public:

  // float height = 2;
  void clear_height();
  float height() const;
  void set_height(float value);
  private:
  float _internal_height() const;
  void _internal_set_height(float value);
  public:

  // float thickness = 3;
  void clear_thickness();
  float thickness() const;
  void set_thickness(float value);
  private:
  float _internal_thickness() const;
  void _internal_set_thickness(float value);
  public:

  // @@protoc_insertion_point(class_scope:cura.proto.Layer)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::cura::proto::Polygon > polygons_;
    int32_t id_;
    float height_;
    float thickness_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_Cura_2eproto;
};
// -------------------------------------------------------------------

class Polygon final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:cura.proto.Polygon) */ {
 public:
  inline Polygon() : Polygon(nullptr) {}
  ~Polygon() override;
  explicit PROTOBUF_CONSTEXPR Polygon(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  Polygon(const Polygon& from);
  Polygon(Polygon&& from) noexcept
    : Polygon() {
    *this = ::std::move(from);
  }

  inline Polygon& operator=(const Polygon& from) {
    CopyFrom(from);
    return *this;
  }
  inline Polygon& operator=(Polygon&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const Polygon& default_instance() {
    return *internal_default_instance();
  }
  static inline const Polygon* internal_default_instance() {
    return reinterpret_cast<const Polygon*>(
               &_Polygon_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    7;

  friend void swap(Polygon& a, Polygon& b) {
    a.Swap(&b);
  }
  inline void Swap(Polygon* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(Polygon* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  Polygon* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<Polygon>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const Polygon& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const Polygon& from) {
    Polygon::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(Polygon* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "cura.proto.Polygon";
  }
  protected:
  explicit Polygon(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  typedef Polygon_Type Type;
  static constexpr Type NoneType =
    Polygon_Type_NoneType;
  static constexpr Type Inset0Type =
    Polygon_Type_Inset0Type;
  static constexpr Type InsetXType =
    Polygon_Type_InsetXType;
  static constexpr Type SkinType =
    Polygon_Type_SkinType;
  static constexpr Type SupportType =
    Polygon_Type_SupportType;
  static constexpr Type SkirtType =
    Polygon_Type_SkirtType;
  static constexpr Type InfillType =
    Polygon_Type_InfillType;
  static constexpr Type SupportInfillType =
    Polygon_Type_SupportInfillType;
  static constexpr Type MoveUnretracted =
    Polygon_Type_MoveUnretracted;
  static constexpr Type MoveRetracted =
    Polygon_Type_MoveRetracted;
  static constexpr Type SupportInterfaceType =
    Polygon_Type_SupportInterfaceType;
  static constexpr Type PrimeTowerType =
    Polygon_Type_PrimeTowerType;
  static constexpr Type MoveWhileRetracting =
    Polygon_Type_MoveWhileRetracting;
  static constexpr Type MoveWhileUnretracting =
    Polygon_Type_MoveWhileUnretracting;
  static constexpr Type StationaryRetractUnretract =
    Polygon_Type_StationaryRetractUnretract;
  static constexpr Type NumPrintFeatureTypes =
    Polygon_Type_NumPrintFeatureTypes;
  static inline bool Type_IsValid(int value) {
    return Polygon_Type_IsValid(value);
  }
  static constexpr Type Type_MIN =
    Polygon_Type_Type_MIN;
  static constexpr Type Type_MAX =
    Polygon_Type_Type_MAX;
  static constexpr int Type_ARRAYSIZE =
    Polygon_Type_Type_ARRAYSIZE;
  static inline const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor*
  Type_descriptor() {
    return Polygon_Type_descriptor();
  }
  template<typename T>
  static inline const std::string& Type_Name(T enum_t_value) {
    static_assert(::std::is_same<T, Type>::value ||
      ::std::is_integral<T>::value,
      "Incorrect type passed to function Type_Name.");
    return Polygon_Type_Name(enum_t_value);
  }
  static inline bool Type_Parse(::PROTOBUF_NAMESPACE_ID::ConstStringParam name,
      Type* value) {
    return Polygon_Type_Parse(name, value);
  }

  // accessors -------------------------------------------------------

  enum : int {
    kPointsFieldNumber = 2,
    kTypeFieldNumber = 1,
    kLineWidthFieldNumber = 3,
    kLineThicknessFieldNumber = 4,
    kLineFeedrateFieldNumber = 5,
  };
  // bytes points = 2;
  void clear_points();
  const std::string& points() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_points(ArgT0&& arg0, ArgT... args);
  std::string* mutable_points();
  PROTOBUF_NODISCARD std::string* release_points();
  void set_allocated_points(std::string* points);
  private:
  const std::string& _internal_points() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_points(const std::string& value);
  std::string* _internal_mutable_points();
  public:

  // .cura.proto.Polygon.Type type = 1;
  void clear_type();
  ::cura::proto::Polygon_Type type() const;
  void set_type(::cura::proto::Polygon_Type value);
  private:
  ::cura::proto::Polygon_Type _internal_type() const;
  void _internal_set_type(::cura::proto::Polygon_Type value);
  public:

  // float line_width = 3;
  void clear_line_width();
  float line_width() const;
  void set_line_width(float value);
  private:
  float _internal_line_width() const;
  void _internal_set_line_width(float value);
  public:

  // float line_thickness = 4;
  void clear_line_thickness();
  float line_thickness() const;
  void set_line_thickness(float value);
  private:
  float _internal_line_thickness() const;
  void _internal_set_line_thickness(float value);
  public:

  // float line_feedrate = 5;
  void clear_line_feedrate();
  float line_feedrate() const;
  void set_line_feedrate(float value);
  private:
  float _internal_line_feedrate() const;
  void _internal_set_line_feedrate(float value);
  public:

  // @@protoc_insertion_point(class_scope:cura.proto.Polygon)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr points_;
    int type_;
    float line_width_;
    float line_thickness_;
    float line_feedrate_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_Cura_2eproto;
};
// -------------------------------------------------------------------

class LayerOptimized final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:cura.proto.LayerOptimized) */ {
 public:
  inline LayerOptimized() : LayerOptimized(nullptr) {}
  ~LayerOptimized() override;
  explicit PROTOBUF_CONSTEXPR LayerOptimized(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  LayerOptimized(const LayerOptimized& from);
  LayerOptimized(LayerOptimized&& from) noexcept
    : LayerOptimized() {
    *this = ::std::move(from);
  }

  inline LayerOptimized& operator=(const LayerOptimized& from) {
    CopyFrom(from);
    return *this;
  }
  inline LayerOptimized& operator=(LayerOptimized&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const LayerOptimized& default_instance() {
    return *internal_default_instance();
  }
  static inline const LayerOptimized* internal_default_instance() {
    return reinterpret_cast<const LayerOptimized*>(
               &_LayerOptimized_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    8;

  friend void swap(LayerOptimized& a, LayerOptimized& b) {
    a.Swap(&b);
  }
  inline void Swap(LayerOptimized* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(LayerOptimized* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  LayerOptimized* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<LayerOptimized>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const LayerOptimized& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const LayerOptimized& from) {
    LayerOptimized::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(LayerOptimized* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "cura.proto.LayerOptimized";
  }
  protected:
  explicit LayerOptimized(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kPathSegmentFieldNumber = 4,
    kIdFieldNumber = 1,
    kHeightFieldNumber = 2,
    kThicknessFieldNumber = 3,
  };
  // repeated .cura.proto.PathSegment path_segment = 4;
  int path_segment_size() const;
  private:
  int _internal_path_segment_size() const;
  public:
  void clear_path_segment();
  ::cura::proto::PathSegment* mutable_path_segment(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::cura::proto::PathSegment >*
      mutable_path_segment();
  private:
  const ::cura::proto::PathSegment& _internal_path_segment(int index) const;
  ::cura::proto::PathSegment* _internal_add_path_segment();
  public:
  const ::cura::proto::PathSegment& path_segment(int index) const;
  ::cura::proto::PathSegment* add_path_segment();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::cura::proto::PathSegment >&
      path_segment() const;

  // int32 id = 1;
  void clear_id();
  int32_t id() const;
  void set_id(int32_t value);
  private:
  int32_t _internal_id() const;
  void _internal_set_id(int32_t value);
  public:

  // float height = 2;
  void clear_height();
  float height() const;
  void set_height(float value);
  private:
  float _internal_height() const;
  void _internal_set_height(float value);
  public:

  // float thickness = 3;
  void clear_thickness();
  float thickness() const;
  void set_thickness(float value);
  private:
  float _internal_thickness() const;
  void _internal_set_thickness(float value);
  public:

  // @@protoc_insertion_point(class_scope:cura.proto.LayerOptimized)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::cura::proto::PathSegment > path_segment_;
    int32_t id_;
    float height_;
    float thickness_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_Cura_2eproto;
};
// -------------------------------------------------------------------

class PathSegment final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:cura.proto.PathSegment) */ {
 public:
  inline PathSegment() : PathSegment(nullptr) {}
  ~PathSegment() override;
  explicit PROTOBUF_CONSTEXPR PathSegment(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  PathSegment(const PathSegment& from);
  PathSegment(PathSegment&& from) noexcept
    : PathSegment() {
    *this = ::std::move(from);
  }

  inline PathSegment& operator=(const PathSegment& from) {
    CopyFrom(from);
    return *this;
  }
  inline PathSegment& operator=(PathSegment&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const PathSegment& default_instance() {
    return *internal_default_instance();
  }
  static inline const PathSegment* internal_default_instance() {
    return reinterpret_cast<const PathSegment*>(
               &_PathSegment_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    9;

  friend void swap(PathSegment& a, PathSegment& b) {
    a.Swap(&b);
  }
  inline void Swap(PathSegment* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(PathSegment* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  PathSegment* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<PathSegment>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const PathSegment& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const PathSegment& from) {
    PathSegment::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(PathSegment* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "cura.proto.PathSegment";
  }
  protected:
  explicit PathSegment(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  typedef PathSegment_PointType PointType;
  static constexpr PointType Point2D =
    PathSegment_PointType_Point2D;
  static constexpr PointType Point3D =
    PathSegment_PointType_Point3D;
  static inline bool PointType_IsValid(int value) {
    return PathSegment_PointType_IsValid(value);
  }
  static constexpr PointType PointType_MIN =
    PathSegment_PointType_PointType_MIN;
  static constexpr PointType PointType_MAX =
    PathSegment_PointType_PointType_MAX;
  static constexpr int PointType_ARRAYSIZE =
    PathSegment_PointType_PointType_ARRAYSIZE;
  static inline const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor*
  PointType_descriptor() {
    return PathSegment_PointType_descriptor();
  }
  template<typename T>
  static inline const std::string& PointType_Name(T enum_t_value) {
    static_assert(::std::is_same<T, PointType>::value ||
      ::std::is_integral<T>::value,
      "Incorrect type passed to function PointType_Name.");
    return PathSegment_PointType_Name(enum_t_value);
  }
  static inline bool PointType_Parse(::PROTOBUF_NAMESPACE_ID::ConstStringParam name,
      PointType* value) {
    return PathSegment_PointType_Parse(name, value);
  }

  // accessors -------------------------------------------------------

  enum : int {
    kPointsFieldNumber = 3,
    kLineTypeFieldNumber = 4,
    kLineWidthFieldNumber = 5,
    kLineThicknessFieldNumber = 6,
    kLineFeedrateFieldNumber = 7,
    kExtruderFieldNumber = 1,
    kPointTypeFieldNumber = 2,
  };
  // bytes points = 3;
  void clear_points();
  const std::string& points() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_points(ArgT0&& arg0, ArgT... args);
  std::string* mutable_points();
  PROTOBUF_NODISCARD std::string* release_points();
  void set_allocated_points(std::string* points);
  private:
  const std::string& _internal_points() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_points(const std::string& value);
  std::string* _internal_mutable_points();
  public:

  // bytes line_type = 4;
  void clear_line_type();
  const std::string& line_type() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_line_type(ArgT0&& arg0, ArgT... args);
  std::string* mutable_line_type();
  PROTOBUF_NODISCARD std::string* release_line_type();
  void set_allocated_line_type(std::string* line_type);
  private:
  const std::string& _internal_line_type() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_line_type(const std::string& value);
  std::string* _internal_mutable_line_type();
  public:

  // bytes line_width = 5;
  void clear_line_width();
  const std::string& line_width() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_line_width(ArgT0&& arg0, ArgT... args);
  std::string* mutable_line_width();
  PROTOBUF_NODISCARD std::string* release_line_width();
  void set_allocated_line_width(std::string* line_width);
  private:
  const std::string& _internal_line_width() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_line_width(const std::string& value);
  std::string* _internal_mutable_line_width();
  public:

  // bytes line_thickness = 6;
  void clear_line_thickness();
  const std::string& line_thickness() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_line_thickness(ArgT0&& arg0, ArgT... args);
  std::string* mutable_line_thickness();
  PROTOBUF_NODISCARD std::string* release_line_thickness();
  void set_allocated_line_thickness(std::string* line_thickness);
  private:
  const std::string& _internal_line_thickness() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_line_thickness(const std::string& value);
  std::string* _internal_mutable_line_thickness();
  public:

  // bytes line_feedrate = 7;
  void clear_line_feedrate();
  const std::string& line_feedrate() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_line_feedrate(ArgT0&& arg0, ArgT... args);
  std::string* mutable_line_feedrate();
  PROTOBUF_NODISCARD std::string* release_line_feedrate();
  void set_allocated_line_feedrate(std::string* line_feedrate);
  private:
  const std::string& _internal_line_feedrate() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_line_feedrate(const std::string& value);
  std::string* _internal_mutable_line_feedrate();
  public:

  // int32 extruder = 1;
  void clear_extruder();
  int32_t extruder() const;
  void set_extruder(int32_t value);
  private:
  int32_t _internal_extruder() const;
  void _internal_set_extruder(int32_t value);
  public:

  // .cura.proto.PathSegment.PointType point_type = 2;
  void clear_point_type();
  ::cura::proto::PathSegment_PointType point_type() const;
  void set_point_type(::cura::proto::PathSegment_PointType value);
  private:
  ::cura::proto::PathSegment_PointType _internal_point_type() const;
  void _internal_set_point_type(::cura::proto::PathSegment_PointType value);
  public:

  // @@protoc_insertion_point(class_scope:cura.proto.PathSegment)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr points_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr line_type_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr line_width_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr line_thickness_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr line_feedrate_;
    int32_t extruder_;
    int point_type_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_Cura_2eproto;
};
// -------------------------------------------------------------------

class GCodeLayer final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:cura.proto.GCodeLayer) */ {
 public:
  inline GCodeLayer() : GCodeLayer(nullptr) {}
  ~GCodeLayer() override;
  explicit PROTOBUF_CONSTEXPR GCodeLayer(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  GCodeLayer(const GCodeLayer& from);
  GCodeLayer(GCodeLayer&& from) noexcept
    : GCodeLayer() {
    *this = ::std::move(from);
  }

  inline GCodeLayer& operator=(const GCodeLayer& from) {
    CopyFrom(from);
    return *this;
  }
  inline GCodeLayer& operator=(GCodeLayer&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const GCodeLayer& default_instance() {
    return *internal_default_instance();
  }
  static inline const GCodeLayer* internal_default_instance() {
    return reinterpret_cast<const GCodeLayer*>(
               &_GCodeLayer_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    10;

  friend void swap(GCodeLayer& a, GCodeLayer& b) {
    a.Swap(&b);
  }
  inline void Swap(GCodeLayer* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(GCodeLayer* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  GCodeLayer* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<GCodeLayer>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const GCodeLayer& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const GCodeLayer& from) {
    GCodeLayer::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(GCodeLayer* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "cura.proto.GCodeLayer";
  }
  protected:
  explicit GCodeLayer(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kDataFieldNumber = 2,
  };
  // bytes data = 2;
  void clear_data();
  const std::string& data() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_data(ArgT0&& arg0, ArgT... args);
  std::string* mutable_data();
  PROTOBUF_NODISCARD std::string* release_data();
  void set_allocated_data(std::string* data);
  private:
  const std::string& _internal_data() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_data(const std::string& value);
  std::string* _internal_mutable_data();
  public:

  // @@protoc_insertion_point(class_scope:cura.proto.GCodeLayer)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr data_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_Cura_2eproto;
};
// -------------------------------------------------------------------

class PrintTimeMaterialEstimates final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:cura.proto.PrintTimeMaterialEstimates) */ {
 public:
  inline PrintTimeMaterialEstimates() : PrintTimeMaterialEstimates(nullptr) {}
  ~PrintTimeMaterialEstimates() override;
  explicit PROTOBUF_CONSTEXPR PrintTimeMaterialEstimates(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  PrintTimeMaterialEstimates(const PrintTimeMaterialEstimates& from);
  PrintTimeMaterialEstimates(PrintTimeMaterialEstimates&& from) noexcept
    : PrintTimeMaterialEstimates() {
    *this = ::std::move(from);
  }

  inline PrintTimeMaterialEstimates& operator=(const PrintTimeMaterialEstimates& from) {
    CopyFrom(from);
    return *this;
  }
  inline PrintTimeMaterialEstimates& operator=(PrintTimeMaterialEstimates&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const PrintTimeMaterialEstimates& default_instance() {
    return *internal_default_instance();
  }
  static inline const PrintTimeMaterialEstimates* internal_default_instance() {
    return reinterpret_cast<const PrintTimeMaterialEstimates*>(
               &_PrintTimeMaterialEstimates_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    11;

  friend void swap(PrintTimeMaterialEstimates& a, PrintTimeMaterialEstimates& b) {
    a.Swap(&b);
  }
  inline void Swap(PrintTimeMaterialEstimates* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(PrintTimeMaterialEstimates* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  PrintTimeMaterialEstimates* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<PrintTimeMaterialEstimates>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const PrintTimeMaterialEstimates& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const PrintTimeMaterialEstimates& from) {
    PrintTimeMaterialEstimates::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(PrintTimeMaterialEstimates* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "cura.proto.PrintTimeMaterialEstimates";
  }
  protected:
  explicit PrintTimeMaterialEstimates(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kMaterialEstimatesFieldNumber = 13,
    kTimeNoneFieldNumber = 1,
    kTimeInset0FieldNumber = 2,
    kTimeInsetXFieldNumber = 3,
    kTimeSkinFieldNumber = 4,
    kTimeSupportFieldNumber = 5,
    kTimeSkirtFieldNumber = 6,
    kTimeInfillFieldNumber = 7,
    kTimeSupportInfillFieldNumber = 8,
    kTimeTravelFieldNumber = 9,
    kTimeRetractFieldNumber = 10,
    kTimeSupportInterfaceFieldNumber = 11,
    kTimePrimeTowerFieldNumber = 12,
  };
  // repeated .cura.proto.MaterialEstimates materialEstimates = 13;
  int materialestimates_size() const;
  private:
  int _internal_materialestimates_size() const;
  public:
  void clear_materialestimates();
  ::cura::proto::MaterialEstimates* mutable_materialestimates(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::cura::proto::MaterialEstimates >*
      mutable_materialestimates();
  private:
  const ::cura::proto::MaterialEstimates& _internal_materialestimates(int index) const;
  ::cura::proto::MaterialEstimates* _internal_add_materialestimates();
  public:
  const ::cura::proto::MaterialEstimates& materialestimates(int index) const;
  ::cura::proto::MaterialEstimates* add_materialestimates();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::cura::proto::MaterialEstimates >&
      materialestimates() const;

  // float time_none = 1;
  void clear_time_none();
  float time_none() const;
  void set_time_none(float value);
  private:
  float _internal_time_none() const;
  void _internal_set_time_none(float value);
  public:

  // float time_inset_0 = 2;
  void clear_time_inset_0();
  float time_inset_0() const;
  void set_time_inset_0(float value);
  private:
  float _internal_time_inset_0() const;
  void _internal_set_time_inset_0(float value);
  public:

  // float time_inset_x = 3;
  void clear_time_inset_x();
  float time_inset_x() const;
  void set_time_inset_x(float value);
  private:
  float _internal_time_inset_x() const;
  void _internal_set_time_inset_x(float value);
  public:

  // float time_skin = 4;
  void clear_time_skin();
  float time_skin() const;
  void set_time_skin(float value);
  private:
  float _internal_time_skin() const;
  void _internal_set_time_skin(float value);
  public:

  // float time_support = 5;
  void clear_time_support();
  float time_support() const;
  void set_time_support(float value);
  private:
  float _internal_time_support() const;
  void _internal_set_time_support(float value);
  public:

  // float time_skirt = 6;
  void clear_time_skirt();
  float time_skirt() const;
  void set_time_skirt(float value);
  private:
  float _internal_time_skirt() const;
  void _internal_set_time_skirt(float value);
  public:

  // float time_infill = 7;
  void clear_time_infill();
  float time_infill() const;
  void set_time_infill(float value);
  private:
  float _internal_time_infill() const;
  void _internal_set_time_infill(float value);
  public:

  // float time_support_infill = 8;
  void clear_time_support_infill();
  float time_support_infill() const;
  void set_time_support_infill(float value);
  private:
  float _internal_time_support_infill() const;
  void _internal_set_time_support_infill(float value);
  public:

  // float time_travel = 9;
  void clear_time_travel();
  float time_travel() const;
  void set_time_travel(float value);
  private:
  float _internal_time_travel() const;
  void _internal_set_time_travel(float value);
  public:

  // float time_retract = 10;
  void clear_time_retract();
  float time_retract() const;
  void set_time_retract(float value);
  private:
  float _internal_time_retract() const;
  void _internal_set_time_retract(float value);
  public:

  // float time_support_interface = 11;
  void clear_time_support_interface();
  float time_support_interface() const;
  void set_time_support_interface(float value);
  private:
  float _internal_time_support_interface() const;
  void _internal_set_time_support_interface(float value);
  public:

  // float time_prime_tower = 12;
  void clear_time_prime_tower();
  float time_prime_tower() const;
  void set_time_prime_tower(float value);
  private:
  float _internal_time_prime_tower() const;
  void _internal_set_time_prime_tower(float value);
  public:

  // @@protoc_insertion_point(class_scope:cura.proto.PrintTimeMaterialEstimates)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::cura::proto::MaterialEstimates > materialestimates_;
    float time_none_;
    float time_inset_0_;
    float time_inset_x_;
    float time_skin_;
    float time_support_;
    float time_skirt_;
    float time_infill_;
    float time_support_infill_;
    float time_travel_;
    float time_retract_;
    float time_support_interface_;
    float time_prime_tower_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_Cura_2eproto;
};
// -------------------------------------------------------------------

class MaterialEstimates final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:cura.proto.MaterialEstimates) */ {
 public:
  inline MaterialEstimates() : MaterialEstimates(nullptr) {}
  ~MaterialEstimates() override;
  explicit PROTOBUF_CONSTEXPR MaterialEstimates(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  MaterialEstimates(const MaterialEstimates& from);
  MaterialEstimates(MaterialEstimates&& from) noexcept
    : MaterialEstimates() {
    *this = ::std::move(from);
  }

  inline MaterialEstimates& operator=(const MaterialEstimates& from) {
    CopyFrom(from);
    return *this;
  }
  inline MaterialEstimates& operator=(MaterialEstimates&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const MaterialEstimates& default_instance() {
    return *internal_default_instance();
  }
  static inline const MaterialEstimates* internal_default_instance() {
    return reinterpret_cast<const MaterialEstimates*>(
               &_MaterialEstimates_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    12;

  friend void swap(MaterialEstimates& a, MaterialEstimates& b) {
    a.Swap(&b);
  }
  inline void Swap(MaterialEstimates* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(MaterialEstimates* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  MaterialEstimates* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<MaterialEstimates>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const MaterialEstimates& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const MaterialEstimates& from) {
    MaterialEstimates::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(MaterialEstimates* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "cura.proto.MaterialEstimates";
  }
  protected:
  explicit MaterialEstimates(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kIdFieldNumber = 1,
    kMaterialAmountFieldNumber = 2,
  };
  // int64 id = 1;
  void clear_id();
  int64_t id() const;
  void set_id(int64_t value);
  private:
  int64_t _internal_id() const;
  void _internal_set_id(int64_t value);
  public:

  // float material_amount = 2;
  void clear_material_amount();
  float material_amount() const;
  void set_material_amount(float value);
  private:
  float _internal_material_amount() const;
  void _internal_set_material_amount(float value);
  public:

  // @@protoc_insertion_point(class_scope:cura.proto.MaterialEstimates)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    int64_t id_;
    float material_amount_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_Cura_2eproto;
};
// -------------------------------------------------------------------

class SettingList final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:cura.proto.SettingList) */ {
 public:
  inline SettingList() : SettingList(nullptr) {}
  ~SettingList() override;
  explicit PROTOBUF_CONSTEXPR SettingList(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  SettingList(const SettingList& from);
  SettingList(SettingList&& from) noexcept
    : SettingList() {
    *this = ::std::move(from);
  }

  inline SettingList& operator=(const SettingList& from) {
    CopyFrom(from);
    return *this;
  }
  inline SettingList& operator=(SettingList&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const SettingList& default_instance() {
    return *internal_default_instance();
  }
  static inline const SettingList* internal_default_instance() {
    return reinterpret_cast<const SettingList*>(
               &_SettingList_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    13;

  friend void swap(SettingList& a, SettingList& b) {
    a.Swap(&b);
  }
  inline void Swap(SettingList* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(SettingList* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  SettingList* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<SettingList>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const SettingList& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const SettingList& from) {
    SettingList::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(SettingList* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "cura.proto.SettingList";
  }
  protected:
  explicit SettingList(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kSettingsFieldNumber = 1,
  };
  // repeated .cura.proto.Setting settings = 1;
  int settings_size() const;
  private:
  int _internal_settings_size() const;
  public:
  void clear_settings();
  ::cura::proto::Setting* mutable_settings(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::cura::proto::Setting >*
      mutable_settings();
  private:
  const ::cura::proto::Setting& _internal_settings(int index) const;
  ::cura::proto::Setting* _internal_add_settings();
  public:
  const ::cura::proto::Setting& settings(int index) const;
  ::cura::proto::Setting* add_settings();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::cura::proto::Setting >&
      settings() const;

  // @@protoc_insertion_point(class_scope:cura.proto.SettingList)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::cura::proto::Setting > settings_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_Cura_2eproto;
};
// -------------------------------------------------------------------

class Setting final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:cura.proto.Setting) */ {
 public:
  inline Setting() : Setting(nullptr) {}
  ~Setting() override;
  explicit PROTOBUF_CONSTEXPR Setting(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  Setting(const Setting& from);
  Setting(Setting&& from) noexcept
    : Setting() {
    *this = ::std::move(from);
  }

  inline Setting& operator=(const Setting& from) {
    CopyFrom(from);
    return *this;
  }
  inline Setting& operator=(Setting&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const Setting& default_instance() {
    return *internal_default_instance();
  }
  static inline const Setting* internal_default_instance() {
    return reinterpret_cast<const Setting*>(
               &_Setting_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    14;

  friend void swap(Setting& a, Setting& b) {
    a.Swap(&b);
  }
  inline void Swap(Setting* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(Setting* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  Setting* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<Setting>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const Setting& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const Setting& from) {
    Setting::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(Setting* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "cura.proto.Setting";
  }
  protected:
  explicit Setting(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kNameFieldNumber = 1,
    kValueFieldNumber = 2,
  };
  // string name = 1;
  void clear_name();
  const std::string& name() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_name(ArgT0&& arg0, ArgT... args);
  std::string* mutable_name();
  PROTOBUF_NODISCARD std::string* release_name();
  void set_allocated_name(std::string* name);
  private:
  const std::string& _internal_name() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_name(const std::string& value);
  std::string* _internal_mutable_name();
  public:

  // bytes value = 2;
  void clear_value();
  const std::string& value() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_value(ArgT0&& arg0, ArgT... args);
  std::string* mutable_value();
  PROTOBUF_NODISCARD std::string* release_value();
  void set_allocated_value(std::string* value);
  private:
  const std::string& _internal_value() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_value(const std::string& value);
  std::string* _internal_mutable_value();
  public:

  // @@protoc_insertion_point(class_scope:cura.proto.Setting)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr name_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr value_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_Cura_2eproto;
};
// -------------------------------------------------------------------

class SettingExtruder final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:cura.proto.SettingExtruder) */ {
 public:
  inline SettingExtruder() : SettingExtruder(nullptr) {}
  ~SettingExtruder() override;
  explicit PROTOBUF_CONSTEXPR SettingExtruder(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  SettingExtruder(const SettingExtruder& from);
  SettingExtruder(SettingExtruder&& from) noexcept
    : SettingExtruder() {
    *this = ::std::move(from);
  }

  inline SettingExtruder& operator=(const SettingExtruder& from) {
    CopyFrom(from);
    return *this;
  }
  inline SettingExtruder& operator=(SettingExtruder&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const SettingExtruder& default_instance() {
    return *internal_default_instance();
  }
  static inline const SettingExtruder* internal_default_instance() {
    return reinterpret_cast<const SettingExtruder*>(
               &_SettingExtruder_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    15;

  friend void swap(SettingExtruder& a, SettingExtruder& b) {
    a.Swap(&b);
  }
  inline void Swap(SettingExtruder* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(SettingExtruder* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  SettingExtruder* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<SettingExtruder>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const SettingExtruder& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const SettingExtruder& from) {
    SettingExtruder::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(SettingExtruder* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "cura.proto.SettingExtruder";
  }
  protected:
  explicit SettingExtruder(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kNameFieldNumber = 1,
    kExtruderFieldNumber = 2,
  };
  // string name = 1;
  void clear_name();
  const std::string& name() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_name(ArgT0&& arg0, ArgT... args);
  std::string* mutable_name();
  PROTOBUF_NODISCARD std::string* release_name();
  void set_allocated_name(std::string* name);
  private:
  const std::string& _internal_name() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_name(const std::string& value);
  std::string* _internal_mutable_name();
  public:

  // int32 extruder = 2;
  void clear_extruder();
  int32_t extruder() const;
  void set_extruder(int32_t value);
  private:
  int32_t _internal_extruder() const;
  void _internal_set_extruder(int32_t value);
  public:

  // @@protoc_insertion_point(class_scope:cura.proto.SettingExtruder)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr name_;
    int32_t extruder_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_Cura_2eproto;
};
// -------------------------------------------------------------------

class GCodePrefix final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:cura.proto.GCodePrefix) */ {
 public:
  inline GCodePrefix() : GCodePrefix(nullptr) {}
  ~GCodePrefix() override;
  explicit PROTOBUF_CONSTEXPR GCodePrefix(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  GCodePrefix(const GCodePrefix& from);
  GCodePrefix(GCodePrefix&& from) noexcept
    : GCodePrefix() {
    *this = ::std::move(from);
  }

  inline GCodePrefix& operator=(const GCodePrefix& from) {
    CopyFrom(from);
    return *this;
  }
  inline GCodePrefix& operator=(GCodePrefix&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const GCodePrefix& default_instance() {
    return *internal_default_instance();
  }
  static inline const GCodePrefix* internal_default_instance() {
    return reinterpret_cast<const GCodePrefix*>(
               &_GCodePrefix_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    16;

  friend void swap(GCodePrefix& a, GCodePrefix& b) {
    a.Swap(&b);
  }
  inline void Swap(GCodePrefix* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(GCodePrefix* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  GCodePrefix* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<GCodePrefix>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const GCodePrefix& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const GCodePrefix& from) {
    GCodePrefix::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(GCodePrefix* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "cura.proto.GCodePrefix";
  }
  protected:
  explicit GCodePrefix(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kDataFieldNumber = 2,
  };
  // bytes data = 2;
  void clear_data();
  const std::string& data() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_data(ArgT0&& arg0, ArgT... args);
  std::string* mutable_data();
  PROTOBUF_NODISCARD std::string* release_data();
  void set_allocated_data(std::string* data);
  private:
  const std::string& _internal_data() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_data(const std::string& value);
  std::string* _internal_mutable_data();
  public:

  // @@protoc_insertion_point(class_scope:cura.proto.GCodePrefix)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr data_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_Cura_2eproto;
};
// -------------------------------------------------------------------

class SliceUUID final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:cura.proto.SliceUUID) */ {
 public:
  inline SliceUUID() : SliceUUID(nullptr) {}
  ~SliceUUID() override;
  explicit PROTOBUF_CONSTEXPR SliceUUID(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  SliceUUID(const SliceUUID& from);
  SliceUUID(SliceUUID&& from) noexcept
    : SliceUUID() {
    *this = ::std::move(from);
  }

  inline SliceUUID& operator=(const SliceUUID& from) {
    CopyFrom(from);
    return *this;
  }
  inline SliceUUID& operator=(SliceUUID&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const SliceUUID& default_instance() {
    return *internal_default_instance();
  }
  static inline const SliceUUID* internal_default_instance() {
    return reinterpret_cast<const SliceUUID*>(
               &_SliceUUID_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    17;

  friend void swap(SliceUUID& a, SliceUUID& b) {
    a.Swap(&b);
  }
  inline void Swap(SliceUUID* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(SliceUUID* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  SliceUUID* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<SliceUUID>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const SliceUUID& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const SliceUUID& from) {
    SliceUUID::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(SliceUUID* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "cura.proto.SliceUUID";
  }
  protected:
  explicit SliceUUID(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kSliceUuidFieldNumber = 1,
  };
  // string slice_uuid = 1;
  void clear_slice_uuid();
  const std::string& slice_uuid() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_slice_uuid(ArgT0&& arg0, ArgT... args);
  std::string* mutable_slice_uuid();
  PROTOBUF_NODISCARD std::string* release_slice_uuid();
  void set_allocated_slice_uuid(std::string* slice_uuid);
  private:
  const std::string& _internal_slice_uuid() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_slice_uuid(const std::string& value);
  std::string* _internal_mutable_slice_uuid();
  public:

  // @@protoc_insertion_point(class_scope:cura.proto.SliceUUID)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr slice_uuid_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_Cura_2eproto;
};
// -------------------------------------------------------------------

class SlicingFinished final :
    public ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase /* @@protoc_insertion_point(class_definition:cura.proto.SlicingFinished) */ {
 public:
  inline SlicingFinished() : SlicingFinished(nullptr) {}
  explicit PROTOBUF_CONSTEXPR SlicingFinished(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  SlicingFinished(const SlicingFinished& from);
  SlicingFinished(SlicingFinished&& from) noexcept
    : SlicingFinished() {
    *this = ::std::move(from);
  }

  inline SlicingFinished& operator=(const SlicingFinished& from) {
    CopyFrom(from);
    return *this;
  }
  inline SlicingFinished& operator=(SlicingFinished&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const SlicingFinished& default_instance() {
    return *internal_default_instance();
  }
  static inline const SlicingFinished* internal_default_instance() {
    return reinterpret_cast<const SlicingFinished*>(
               &_SlicingFinished_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    18;

  friend void swap(SlicingFinished& a, SlicingFinished& b) {
    a.Swap(&b);
  }
  inline void Swap(SlicingFinished* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(SlicingFinished* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  SlicingFinished* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<SlicingFinished>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::CopyFrom;
  inline void CopyFrom(const SlicingFinished& from) {
    ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::CopyImpl(*this, from);
  }
  using ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::MergeFrom;
  void MergeFrom(const SlicingFinished& from) {
    ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::MergeImpl(*this, from);
  }
  public:

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "cura.proto.SlicingFinished";
  }
  protected:
  explicit SlicingFinished(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // @@protoc_insertion_point(class_scope:cura.proto.SlicingFinished)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
  };
  friend struct ::TableStruct_Cura_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// ObjectList

// repeated .cura.proto.Object objects = 1;
inline int ObjectList::_internal_objects_size() const {
  return _impl_.objects_.size();
}
inline int ObjectList::objects_size() const {
  return _internal_objects_size();
}
inline void ObjectList::clear_objects() {
  _impl_.objects_.Clear();
}
inline ::cura::proto::Object* ObjectList::mutable_objects(int index) {
  // @@protoc_insertion_point(field_mutable:cura.proto.ObjectList.objects)
  return _impl_.objects_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::cura::proto::Object >*
ObjectList::mutable_objects() {
  // @@protoc_insertion_point(field_mutable_list:cura.proto.ObjectList.objects)
  return &_impl_.objects_;
}
inline const ::cura::proto::Object& ObjectList::_internal_objects(int index) const {
  return _impl_.objects_.Get(index);
}
inline const ::cura::proto::Object& ObjectList::objects(int index) const {
  // @@protoc_insertion_point(field_get:cura.proto.ObjectList.objects)
  return _internal_objects(index);
}
inline ::cura::proto::Object* ObjectList::_internal_add_objects() {
  return _impl_.objects_.Add();
}
inline ::cura::proto::Object* ObjectList::add_objects() {
  ::cura::proto::Object* _add = _internal_add_objects();
  // @@protoc_insertion_point(field_add:cura.proto.ObjectList.objects)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::cura::proto::Object >&
ObjectList::objects() const {
  // @@protoc_insertion_point(field_list:cura.proto.ObjectList.objects)
  return _impl_.objects_;
}

// repeated .cura.proto.Setting settings = 2;
inline int ObjectList::_internal_settings_size() const {
  return _impl_.settings_.size();
}
inline int ObjectList::settings_size() const {
  return _internal_settings_size();
}
inline void ObjectList::clear_settings() {
  _impl_.settings_.Clear();
}
inline ::cura::proto::Setting* ObjectList::mutable_settings(int index) {
  // @@protoc_insertion_point(field_mutable:cura.proto.ObjectList.settings)
  return _impl_.settings_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::cura::proto::Setting >*
ObjectList::mutable_settings() {
  // @@protoc_insertion_point(field_mutable_list:cura.proto.ObjectList.settings)
  return &_impl_.settings_;
}
inline const ::cura::proto::Setting& ObjectList::_internal_settings(int index) const {
  return _impl_.settings_.Get(index);
}
inline const ::cura::proto::Setting& ObjectList::settings(int index) const {
  // @@protoc_insertion_point(field_get:cura.proto.ObjectList.settings)
  return _internal_settings(index);
}
inline ::cura::proto::Setting* ObjectList::_internal_add_settings() {
  return _impl_.settings_.Add();
}
inline ::cura::proto::Setting* ObjectList::add_settings() {
  ::cura::proto::Setting* _add = _internal_add_settings();
  // @@protoc_insertion_point(field_add:cura.proto.ObjectList.settings)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::cura::proto::Setting >&
ObjectList::settings() const {
  // @@protoc_insertion_point(field_list:cura.proto.ObjectList.settings)
  return _impl_.settings_;
}

// -------------------------------------------------------------------

// EnginePlugin

// .cura.proto.SlotID id = 1;
inline void EnginePlugin::clear_id() {
  _impl_.id_ = 0;
}
inline ::cura::proto::SlotID EnginePlugin::_internal_id() const {
  return static_cast< ::cura::proto::SlotID >(_impl_.id_);
}
inline ::cura::proto::SlotID EnginePlugin::id() const {
  // @@protoc_insertion_point(field_get:cura.proto.EnginePlugin.id)
  return _internal_id();
}
inline void EnginePlugin::_internal_set_id(::cura::proto::SlotID value) {
  
  _impl_.id_ = value;
}
inline void EnginePlugin::set_id(::cura::proto::SlotID value) {
  _internal_set_id(value);
  // @@protoc_insertion_point(field_set:cura.proto.EnginePlugin.id)
}

// string address = 2;
inline void EnginePlugin::clear_address() {
  _impl_.address_.ClearToEmpty();
}
inline const std::string& EnginePlugin::address() const {
  // @@protoc_insertion_point(field_get:cura.proto.EnginePlugin.address)
  return _internal_address();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void EnginePlugin::set_address(ArgT0&& arg0, ArgT... args) {
 
 _impl_.address_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:cura.proto.EnginePlugin.address)
}
inline std::string* EnginePlugin::mutable_address() {
  std::string* _s = _internal_mutable_address();
  // @@protoc_insertion_point(field_mutable:cura.proto.EnginePlugin.address)
  return _s;
}
inline const std::string& EnginePlugin::_internal_address() const {
  return _impl_.address_.Get();
}
inline void EnginePlugin::_internal_set_address(const std::string& value) {
  
  _impl_.address_.Set(value, GetArenaForAllocation());
}
inline std::string* EnginePlugin::_internal_mutable_address() {
  
  return _impl_.address_.Mutable(GetArenaForAllocation());
}
inline std::string* EnginePlugin::release_address() {
  // @@protoc_insertion_point(field_release:cura.proto.EnginePlugin.address)
  return _impl_.address_.Release();
}
inline void EnginePlugin::set_allocated_address(std::string* address) {
  if (address != nullptr) {
    
  } else {
    
  }
  _impl_.address_.SetAllocated(address, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.address_.IsDefault()) {
    _impl_.address_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:cura.proto.EnginePlugin.address)
}

// uint32 port = 3;
inline void EnginePlugin::clear_port() {
  _impl_.port_ = 0u;
}
inline uint32_t EnginePlugin::_internal_port() const {
  return _impl_.port_;
}
inline uint32_t EnginePlugin::port() const {
  // @@protoc_insertion_point(field_get:cura.proto.EnginePlugin.port)
  return _internal_port();
}
inline void EnginePlugin::_internal_set_port(uint32_t value) {
  
  _impl_.port_ = value;
}
inline void EnginePlugin::set_port(uint32_t value) {
  _internal_set_port(value);
  // @@protoc_insertion_point(field_set:cura.proto.EnginePlugin.port)
}

// string plugin_name = 4;
inline void EnginePlugin::clear_plugin_name() {
  _impl_.plugin_name_.ClearToEmpty();
}
inline const std::string& EnginePlugin::plugin_name() const {
  // @@protoc_insertion_point(field_get:cura.proto.EnginePlugin.plugin_name)
  return _internal_plugin_name();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void EnginePlugin::set_plugin_name(ArgT0&& arg0, ArgT... args) {
 
 _impl_.plugin_name_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:cura.proto.EnginePlugin.plugin_name)
}
inline std::string* EnginePlugin::mutable_plugin_name() {
  std::string* _s = _internal_mutable_plugin_name();
  // @@protoc_insertion_point(field_mutable:cura.proto.EnginePlugin.plugin_name)
  return _s;
}
inline const std::string& EnginePlugin::_internal_plugin_name() const {
  return _impl_.plugin_name_.Get();
}
inline void EnginePlugin::_internal_set_plugin_name(const std::string& value) {
  
  _impl_.plugin_name_.Set(value, GetArenaForAllocation());
}
inline std::string* EnginePlugin::_internal_mutable_plugin_name() {
  
  return _impl_.plugin_name_.Mutable(GetArenaForAllocation());
}
inline std::string* EnginePlugin::release_plugin_name() {
  // @@protoc_insertion_point(field_release:cura.proto.EnginePlugin.plugin_name)
  return _impl_.plugin_name_.Release();
}
inline void EnginePlugin::set_allocated_plugin_name(std::string* plugin_name) {
  if (plugin_name != nullptr) {
    
  } else {
    
  }
  _impl_.plugin_name_.SetAllocated(plugin_name, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.plugin_name_.IsDefault()) {
    _impl_.plugin_name_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:cura.proto.EnginePlugin.plugin_name)
}

// string plugin_version = 5;
inline void EnginePlugin::clear_plugin_version() {
  _impl_.plugin_version_.ClearToEmpty();
}
inline const std::string& EnginePlugin::plugin_version() const {
  // @@protoc_insertion_point(field_get:cura.proto.EnginePlugin.plugin_version)
  return _internal_plugin_version();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void EnginePlugin::set_plugin_version(ArgT0&& arg0, ArgT... args) {
 
 _impl_.plugin_version_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:cura.proto.EnginePlugin.plugin_version)
}
inline std::string* EnginePlugin::mutable_plugin_version() {
  std::string* _s = _internal_mutable_plugin_version();
  // @@protoc_insertion_point(field_mutable:cura.proto.EnginePlugin.plugin_version)
  return _s;
}
inline const std::string& EnginePlugin::_internal_plugin_version() const {
  return _impl_.plugin_version_.Get();
}
inline void EnginePlugin::_internal_set_plugin_version(const std::string& value) {
  
  _impl_.plugin_version_.Set(value, GetArenaForAllocation());
}
inline std::string* EnginePlugin::_internal_mutable_plugin_version() {
  
  return _impl_.plugin_version_.Mutable(GetArenaForAllocation());
}
inline std::string* EnginePlugin::release_plugin_version() {
  // @@protoc_insertion_point(field_release:cura.proto.EnginePlugin.plugin_version)
  return _impl_.plugin_version_.Release();
}
inline void EnginePlugin::set_allocated_plugin_version(std::string* plugin_version) {
  if (plugin_version != nullptr) {
    
  } else {
    
  }
  _impl_.plugin_version_.SetAllocated(plugin_version, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.plugin_version_.IsDefault()) {
    _impl_.plugin_version_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:cura.proto.EnginePlugin.plugin_version)
}

// -------------------------------------------------------------------

// Slice

// repeated .cura.proto.ObjectList object_lists = 1;
inline int Slice::_internal_object_lists_size() const {
  return _impl_.object_lists_.size();
}
inline int Slice::object_lists_size() const {
  return _internal_object_lists_size();
}
inline void Slice::clear_object_lists() {
  _impl_.object_lists_.Clear();
}
inline ::cura::proto::ObjectList* Slice::mutable_object_lists(int index) {
  // @@protoc_insertion_point(field_mutable:cura.proto.Slice.object_lists)
  return _impl_.object_lists_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::cura::proto::ObjectList >*
Slice::mutable_object_lists() {
  // @@protoc_insertion_point(field_mutable_list:cura.proto.Slice.object_lists)
  return &_impl_.object_lists_;
}
inline const ::cura::proto::ObjectList& Slice::_internal_object_lists(int index) const {
  return _impl_.object_lists_.Get(index);
}
inline const ::cura::proto::ObjectList& Slice::object_lists(int index) const {
  // @@protoc_insertion_point(field_get:cura.proto.Slice.object_lists)
  return _internal_object_lists(index);
}
inline ::cura::proto::ObjectList* Slice::_internal_add_object_lists() {
  return _impl_.object_lists_.Add();
}
inline ::cura::proto::ObjectList* Slice::add_object_lists() {
  ::cura::proto::ObjectList* _add = _internal_add_object_lists();
  // @@protoc_insertion_point(field_add:cura.proto.Slice.object_lists)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::cura::proto::ObjectList >&
Slice::object_lists() const {
  // @@protoc_insertion_point(field_list:cura.proto.Slice.object_lists)
  return _impl_.object_lists_;
}

// .cura.proto.SettingList global_settings = 2;
inline bool Slice::_internal_has_global_settings() const {
  return this != internal_default_instance() && _impl_.global_settings_ != nullptr;
}
inline bool Slice::has_global_settings() const {
  return _internal_has_global_settings();
}
inline void Slice::clear_global_settings() {
  if (GetArenaForAllocation() == nullptr && _impl_.global_settings_ != nullptr) {
    delete _impl_.global_settings_;
  }
  _impl_.global_settings_ = nullptr;
}
inline const ::cura::proto::SettingList& Slice::_internal_global_settings() const {
  const ::cura::proto::SettingList* p = _impl_.global_settings_;
  return p != nullptr ? *p : reinterpret_cast<const ::cura::proto::SettingList&>(
      ::cura::proto::_SettingList_default_instance_);
}
inline const ::cura::proto::SettingList& Slice::global_settings() const {
  // @@protoc_insertion_point(field_get:cura.proto.Slice.global_settings)
  return _internal_global_settings();
}
inline void Slice::unsafe_arena_set_allocated_global_settings(
    ::cura::proto::SettingList* global_settings) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.global_settings_);
  }
  _impl_.global_settings_ = global_settings;
  if (global_settings) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:cura.proto.Slice.global_settings)
}
inline ::cura::proto::SettingList* Slice::release_global_settings() {
  
  ::cura::proto::SettingList* temp = _impl_.global_settings_;
  _impl_.global_settings_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::cura::proto::SettingList* Slice::unsafe_arena_release_global_settings() {
  // @@protoc_insertion_point(field_release:cura.proto.Slice.global_settings)
  
  ::cura::proto::SettingList* temp = _impl_.global_settings_;
  _impl_.global_settings_ = nullptr;
  return temp;
}
inline ::cura::proto::SettingList* Slice::_internal_mutable_global_settings() {
  
  if (_impl_.global_settings_ == nullptr) {
    auto* p = CreateMaybeMessage<::cura::proto::SettingList>(GetArenaForAllocation());
    _impl_.global_settings_ = p;
  }
  return _impl_.global_settings_;
}
inline ::cura::proto::SettingList* Slice::mutable_global_settings() {
  ::cura::proto::SettingList* _msg = _internal_mutable_global_settings();
  // @@protoc_insertion_point(field_mutable:cura.proto.Slice.global_settings)
  return _msg;
}
inline void Slice::set_allocated_global_settings(::cura::proto::SettingList* global_settings) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete _impl_.global_settings_;
  }
  if (global_settings) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(global_settings);
    if (message_arena != submessage_arena) {
      global_settings = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, global_settings, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.global_settings_ = global_settings;
  // @@protoc_insertion_point(field_set_allocated:cura.proto.Slice.global_settings)
}

// repeated .cura.proto.Extruder extruders = 3;
inline int Slice::_internal_extruders_size() const {
  return _impl_.extruders_.size();
}
inline int Slice::extruders_size() const {
  return _internal_extruders_size();
}
inline void Slice::clear_extruders() {
  _impl_.extruders_.Clear();
}
inline ::cura::proto::Extruder* Slice::mutable_extruders(int index) {
  // @@protoc_insertion_point(field_mutable:cura.proto.Slice.extruders)
  return _impl_.extruders_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::cura::proto::Extruder >*
Slice::mutable_extruders() {
  // @@protoc_insertion_point(field_mutable_list:cura.proto.Slice.extruders)
  return &_impl_.extruders_;
}
inline const ::cura::proto::Extruder& Slice::_internal_extruders(int index) const {
  return _impl_.extruders_.Get(index);
}
inline const ::cura::proto::Extruder& Slice::extruders(int index) const {
  // @@protoc_insertion_point(field_get:cura.proto.Slice.extruders)
  return _internal_extruders(index);
}
inline ::cura::proto::Extruder* Slice::_internal_add_extruders() {
  return _impl_.extruders_.Add();
}
inline ::cura::proto::Extruder* Slice::add_extruders() {
  ::cura::proto::Extruder* _add = _internal_add_extruders();
  // @@protoc_insertion_point(field_add:cura.proto.Slice.extruders)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::cura::proto::Extruder >&
Slice::extruders() const {
  // @@protoc_insertion_point(field_list:cura.proto.Slice.extruders)
  return _impl_.extruders_;
}

// repeated .cura.proto.SettingExtruder limit_to_extruder = 4;
inline int Slice::_internal_limit_to_extruder_size() const {
  return _impl_.limit_to_extruder_.size();
}
inline int Slice::limit_to_extruder_size() const {
  return _internal_limit_to_extruder_size();
}
inline void Slice::clear_limit_to_extruder() {
  _impl_.limit_to_extruder_.Clear();
}
inline ::cura::proto::SettingExtruder* Slice::mutable_limit_to_extruder(int index) {
  // @@protoc_insertion_point(field_mutable:cura.proto.Slice.limit_to_extruder)
  return _impl_.limit_to_extruder_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::cura::proto::SettingExtruder >*
Slice::mutable_limit_to_extruder() {
  // @@protoc_insertion_point(field_mutable_list:cura.proto.Slice.limit_to_extruder)
  return &_impl_.limit_to_extruder_;
}
inline const ::cura::proto::SettingExtruder& Slice::_internal_limit_to_extruder(int index) const {
  return _impl_.limit_to_extruder_.Get(index);
}
inline const ::cura::proto::SettingExtruder& Slice::limit_to_extruder(int index) const {
  // @@protoc_insertion_point(field_get:cura.proto.Slice.limit_to_extruder)
  return _internal_limit_to_extruder(index);
}
inline ::cura::proto::SettingExtruder* Slice::_internal_add_limit_to_extruder() {
  return _impl_.limit_to_extruder_.Add();
}
inline ::cura::proto::SettingExtruder* Slice::add_limit_to_extruder() {
  ::cura::proto::SettingExtruder* _add = _internal_add_limit_to_extruder();
  // @@protoc_insertion_point(field_add:cura.proto.Slice.limit_to_extruder)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::cura::proto::SettingExtruder >&
Slice::limit_to_extruder() const {
  // @@protoc_insertion_point(field_list:cura.proto.Slice.limit_to_extruder)
  return _impl_.limit_to_extruder_;
}

// repeated .cura.proto.EnginePlugin engine_plugins = 5;
inline int Slice::_internal_engine_plugins_size() const {
  return _impl_.engine_plugins_.size();
}
inline int Slice::engine_plugins_size() const {
  return _internal_engine_plugins_size();
}
inline void Slice::clear_engine_plugins() {
  _impl_.engine_plugins_.Clear();
}
inline ::cura::proto::EnginePlugin* Slice::mutable_engine_plugins(int index) {
  // @@protoc_insertion_point(field_mutable:cura.proto.Slice.engine_plugins)
  return _impl_.engine_plugins_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::cura::proto::EnginePlugin >*
Slice::mutable_engine_plugins() {
  // @@protoc_insertion_point(field_mutable_list:cura.proto.Slice.engine_plugins)
  return &_impl_.engine_plugins_;
}
inline const ::cura::proto::EnginePlugin& Slice::_internal_engine_plugins(int index) const {
  return _impl_.engine_plugins_.Get(index);
}
inline const ::cura::proto::EnginePlugin& Slice::engine_plugins(int index) const {
  // @@protoc_insertion_point(field_get:cura.proto.Slice.engine_plugins)
  return _internal_engine_plugins(index);
}
inline ::cura::proto::EnginePlugin* Slice::_internal_add_engine_plugins() {
  return _impl_.engine_plugins_.Add();
}
inline ::cura::proto::EnginePlugin* Slice::add_engine_plugins() {
  ::cura::proto::EnginePlugin* _add = _internal_add_engine_plugins();
  // @@protoc_insertion_point(field_add:cura.proto.Slice.engine_plugins)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::cura::proto::EnginePlugin >&
Slice::engine_plugins() const {
  // @@protoc_insertion_point(field_list:cura.proto.Slice.engine_plugins)
  return _impl_.engine_plugins_;
}

// string sentry_id = 6;
inline void Slice::clear_sentry_id() {
  _impl_.sentry_id_.ClearToEmpty();
}
inline const std::string& Slice::sentry_id() const {
  // @@protoc_insertion_point(field_get:cura.proto.Slice.sentry_id)
  return _internal_sentry_id();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void Slice::set_sentry_id(ArgT0&& arg0, ArgT... args) {
 
 _impl_.sentry_id_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:cura.proto.Slice.sentry_id)
}
inline std::string* Slice::mutable_sentry_id() {
  std::string* _s = _internal_mutable_sentry_id();
  // @@protoc_insertion_point(field_mutable:cura.proto.Slice.sentry_id)
  return _s;
}
inline const std::string& Slice::_internal_sentry_id() const {
  return _impl_.sentry_id_.Get();
}
inline void Slice::_internal_set_sentry_id(const std::string& value) {
  
  _impl_.sentry_id_.Set(value, GetArenaForAllocation());
}
inline std::string* Slice::_internal_mutable_sentry_id() {
  
  return _impl_.sentry_id_.Mutable(GetArenaForAllocation());
}
inline std::string* Slice::release_sentry_id() {
  // @@protoc_insertion_point(field_release:cura.proto.Slice.sentry_id)
  return _impl_.sentry_id_.Release();
}
inline void Slice::set_allocated_sentry_id(std::string* sentry_id) {
  if (sentry_id != nullptr) {
    
  } else {
    
  }
  _impl_.sentry_id_.SetAllocated(sentry_id, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.sentry_id_.IsDefault()) {
    _impl_.sentry_id_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:cura.proto.Slice.sentry_id)
}

// string cura_version = 7;
inline void Slice::clear_cura_version() {
  _impl_.cura_version_.ClearToEmpty();
}
inline const std::string& Slice::cura_version() const {
  // @@protoc_insertion_point(field_get:cura.proto.Slice.cura_version)
  return _internal_cura_version();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void Slice::set_cura_version(ArgT0&& arg0, ArgT... args) {
 
 _impl_.cura_version_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:cura.proto.Slice.cura_version)
}
inline std::string* Slice::mutable_cura_version() {
  std::string* _s = _internal_mutable_cura_version();
  // @@protoc_insertion_point(field_mutable:cura.proto.Slice.cura_version)
  return _s;
}
inline const std::string& Slice::_internal_cura_version() const {
  return _impl_.cura_version_.Get();
}
inline void Slice::_internal_set_cura_version(const std::string& value) {
  
  _impl_.cura_version_.Set(value, GetArenaForAllocation());
}
inline std::string* Slice::_internal_mutable_cura_version() {
  
  return _impl_.cura_version_.Mutable(GetArenaForAllocation());
}
inline std::string* Slice::release_cura_version() {
  // @@protoc_insertion_point(field_release:cura.proto.Slice.cura_version)
  return _impl_.cura_version_.Release();
}
inline void Slice::set_allocated_cura_version(std::string* cura_version) {
  if (cura_version != nullptr) {
    
  } else {
    
  }
  _impl_.cura_version_.SetAllocated(cura_version, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.cura_version_.IsDefault()) {
    _impl_.cura_version_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:cura.proto.Slice.cura_version)
}

// optional string project_name = 8;
inline bool Slice::_internal_has_project_name() const {
  bool value = (_impl_._has_bits_[0] & 0x00000001u) != 0;
  return value;
}
inline bool Slice::has_project_name() const {
  return _internal_has_project_name();
}
inline void Slice::clear_project_name() {
  _impl_.project_name_.ClearToEmpty();
  _impl_._has_bits_[0] &= ~0x00000001u;
}
inline const std::string& Slice::project_name() const {
  // @@protoc_insertion_point(field_get:cura.proto.Slice.project_name)
  return _internal_project_name();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void Slice::set_project_name(ArgT0&& arg0, ArgT... args) {
 _impl_._has_bits_[0] |= 0x00000001u;
 _impl_.project_name_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:cura.proto.Slice.project_name)
}
inline std::string* Slice::mutable_project_name() {
  std::string* _s = _internal_mutable_project_name();
  // @@protoc_insertion_point(field_mutable:cura.proto.Slice.project_name)
  return _s;
}
inline const std::string& Slice::_internal_project_name() const {
  return _impl_.project_name_.Get();
}
inline void Slice::_internal_set_project_name(const std::string& value) {
  _impl_._has_bits_[0] |= 0x00000001u;
  _impl_.project_name_.Set(value, GetArenaForAllocation());
}
inline std::string* Slice::_internal_mutable_project_name() {
  _impl_._has_bits_[0] |= 0x00000001u;
  return _impl_.project_name_.Mutable(GetArenaForAllocation());
}
inline std::string* Slice::release_project_name() {
  // @@protoc_insertion_point(field_release:cura.proto.Slice.project_name)
  if (!_internal_has_project_name()) {
    return nullptr;
  }
  _impl_._has_bits_[0] &= ~0x00000001u;
  auto* p = _impl_.project_name_.Release();
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.project_name_.IsDefault()) {
    _impl_.project_name_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  return p;
}
inline void Slice::set_allocated_project_name(std::string* project_name) {
  if (project_name != nullptr) {
    _impl_._has_bits_[0] |= 0x00000001u;
  } else {
    _impl_._has_bits_[0] &= ~0x00000001u;
  }
  _impl_.project_name_.SetAllocated(project_name, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.project_name_.IsDefault()) {
    _impl_.project_name_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:cura.proto.Slice.project_name)
}

// optional string user_name = 9;
inline bool Slice::_internal_has_user_name() const {
  bool value = (_impl_._has_bits_[0] & 0x00000002u) != 0;
  return value;
}
inline bool Slice::has_user_name() const {
  return _internal_has_user_name();
}
inline void Slice::clear_user_name() {
  _impl_.user_name_.ClearToEmpty();
  _impl_._has_bits_[0] &= ~0x00000002u;
}
inline const std::string& Slice::user_name() const {
  // @@protoc_insertion_point(field_get:cura.proto.Slice.user_name)
  return _internal_user_name();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void Slice::set_user_name(ArgT0&& arg0, ArgT... args) {
 _impl_._has_bits_[0] |= 0x00000002u;
 _impl_.user_name_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:cura.proto.Slice.user_name)
}
inline std::string* Slice::mutable_user_name() {
  std::string* _s = _internal_mutable_user_name();
  // @@protoc_insertion_point(field_mutable:cura.proto.Slice.user_name)
  return _s;
}
inline const std::string& Slice::_internal_user_name() const {
  return _impl_.user_name_.Get();
}
inline void Slice::_internal_set_user_name(const std::string& value) {
  _impl_._has_bits_[0] |= 0x00000002u;
  _impl_.user_name_.Set(value, GetArenaForAllocation());
}
inline std::string* Slice::_internal_mutable_user_name() {
  _impl_._has_bits_[0] |= 0x00000002u;
  return _impl_.user_name_.Mutable(GetArenaForAllocation());
}
inline std::string* Slice::release_user_name() {
  // @@protoc_insertion_point(field_release:cura.proto.Slice.user_name)
  if (!_internal_has_user_name()) {
    return nullptr;
  }
  _impl_._has_bits_[0] &= ~0x00000002u;
  auto* p = _impl_.user_name_.Release();
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.user_name_.IsDefault()) {
    _impl_.user_name_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  return p;
}
inline void Slice::set_allocated_user_name(std::string* user_name) {
  if (user_name != nullptr) {
    _impl_._has_bits_[0] |= 0x00000002u;
  } else {
    _impl_._has_bits_[0] &= ~0x00000002u;
  }
  _impl_.user_name_.SetAllocated(user_name, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.user_name_.IsDefault()) {
    _impl_.user_name_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:cura.proto.Slice.user_name)
}

// -------------------------------------------------------------------

// Extruder

// int32 id = 1;
inline void Extruder::clear_id() {
  _impl_.id_ = 0;
}
inline int32_t Extruder::_internal_id() const {
  return _impl_.id_;
}
inline int32_t Extruder::id() const {
  // @@protoc_insertion_point(field_get:cura.proto.Extruder.id)
  return _internal_id();
}
inline void Extruder::_internal_set_id(int32_t value) {
  
  _impl_.id_ = value;
}
inline void Extruder::set_id(int32_t value) {
  _internal_set_id(value);
  // @@protoc_insertion_point(field_set:cura.proto.Extruder.id)
}

// .cura.proto.SettingList settings = 2;
inline bool Extruder::_internal_has_settings() const {
  return this != internal_default_instance() && _impl_.settings_ != nullptr;
}
inline bool Extruder::has_settings() const {
  return _internal_has_settings();
}
inline void Extruder::clear_settings() {
  if (GetArenaForAllocation() == nullptr && _impl_.settings_ != nullptr) {
    delete _impl_.settings_;
  }
  _impl_.settings_ = nullptr;
}
inline const ::cura::proto::SettingList& Extruder::_internal_settings() const {
  const ::cura::proto::SettingList* p = _impl_.settings_;
  return p != nullptr ? *p : reinterpret_cast<const ::cura::proto::SettingList&>(
      ::cura::proto::_SettingList_default_instance_);
}
inline const ::cura::proto::SettingList& Extruder::settings() const {
  // @@protoc_insertion_point(field_get:cura.proto.Extruder.settings)
  return _internal_settings();
}
inline void Extruder::unsafe_arena_set_allocated_settings(
    ::cura::proto::SettingList* settings) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.settings_);
  }
  _impl_.settings_ = settings;
  if (settings) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:cura.proto.Extruder.settings)
}
inline ::cura::proto::SettingList* Extruder::release_settings() {
  
  ::cura::proto::SettingList* temp = _impl_.settings_;
  _impl_.settings_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::cura::proto::SettingList* Extruder::unsafe_arena_release_settings() {
  // @@protoc_insertion_point(field_release:cura.proto.Extruder.settings)
  
  ::cura::proto::SettingList* temp = _impl_.settings_;
  _impl_.settings_ = nullptr;
  return temp;
}
inline ::cura::proto::SettingList* Extruder::_internal_mutable_settings() {
  
  if (_impl_.settings_ == nullptr) {
    auto* p = CreateMaybeMessage<::cura::proto::SettingList>(GetArenaForAllocation());
    _impl_.settings_ = p;
  }
  return _impl_.settings_;
}
inline ::cura::proto::SettingList* Extruder::mutable_settings() {
  ::cura::proto::SettingList* _msg = _internal_mutable_settings();
  // @@protoc_insertion_point(field_mutable:cura.proto.Extruder.settings)
  return _msg;
}
inline void Extruder::set_allocated_settings(::cura::proto::SettingList* settings) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete _impl_.settings_;
  }
  if (settings) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(settings);
    if (message_arena != submessage_arena) {
      settings = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, settings, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.settings_ = settings;
  // @@protoc_insertion_point(field_set_allocated:cura.proto.Extruder.settings)
}

// -------------------------------------------------------------------

// Object

// int64 id = 1;
inline void Object::clear_id() {
  _impl_.id_ = int64_t{0};
}
inline int64_t Object::_internal_id() const {
  return _impl_.id_;
}
inline int64_t Object::id() const {
  // @@protoc_insertion_point(field_get:cura.proto.Object.id)
  return _internal_id();
}
inline void Object::_internal_set_id(int64_t value) {
  
  _impl_.id_ = value;
}
inline void Object::set_id(int64_t value) {
  _internal_set_id(value);
  // @@protoc_insertion_point(field_set:cura.proto.Object.id)
}

// bytes vertices = 2;
inline void Object::clear_vertices() {
  _impl_.vertices_.ClearToEmpty();
}
inline const std::string& Object::vertices() const {
  // @@protoc_insertion_point(field_get:cura.proto.Object.vertices)
  return _internal_vertices();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void Object::set_vertices(ArgT0&& arg0, ArgT... args) {
 
 _impl_.vertices_.SetBytes(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:cura.proto.Object.vertices)
}
inline std::string* Object::mutable_vertices() {
  std::string* _s = _internal_mutable_vertices();
  // @@protoc_insertion_point(field_mutable:cura.proto.Object.vertices)
  return _s;
}
inline const std::string& Object::_internal_vertices() const {
  return _impl_.vertices_.Get();
}
inline void Object::_internal_set_vertices(const std::string& value) {
  
  _impl_.vertices_.Set(value, GetArenaForAllocation());
}
inline std::string* Object::_internal_mutable_vertices() {
  
  return _impl_.vertices_.Mutable(GetArenaForAllocation());
}
inline std::string* Object::release_vertices() {
  // @@protoc_insertion_point(field_release:cura.proto.Object.vertices)
  return _impl_.vertices_.Release();
}
inline void Object::set_allocated_vertices(std::string* vertices) {
  if (vertices != nullptr) {
    
  } else {
    
  }
  _impl_.vertices_.SetAllocated(vertices, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.vertices_.IsDefault()) {
    _impl_.vertices_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:cura.proto.Object.vertices)
}

// bytes normals = 3;
inline void Object::clear_normals() {
  _impl_.normals_.ClearToEmpty();
}
inline const std::string& Object::normals() const {
  // @@protoc_insertion_point(field_get:cura.proto.Object.normals)
  return _internal_normals();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void Object::set_normals(ArgT0&& arg0, ArgT... args) {
 
 _impl_.normals_.SetBytes(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:cura.proto.Object.normals)
}
inline std::string* Object::mutable_normals() {
  std::string* _s = _internal_mutable_normals();
  // @@protoc_insertion_point(field_mutable:cura.proto.Object.normals)
  return _s;
}
inline const std::string& Object::_internal_normals() const {
  return _impl_.normals_.Get();
}
inline void Object::_internal_set_normals(const std::string& value) {
  
  _impl_.normals_.Set(value, GetArenaForAllocation());
}
inline std::string* Object::_internal_mutable_normals() {
  
  return _impl_.normals_.Mutable(GetArenaForAllocation());
}
inline std::string* Object::release_normals() {
  // @@protoc_insertion_point(field_release:cura.proto.Object.normals)
  return _impl_.normals_.Release();
}
inline void Object::set_allocated_normals(std::string* normals) {
  if (normals != nullptr) {
    
  } else {
    
  }
  _impl_.normals_.SetAllocated(normals, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.normals_.IsDefault()) {
    _impl_.normals_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:cura.proto.Object.normals)
}

// bytes indices = 4;
inline void Object::clear_indices() {
  _impl_.indices_.ClearToEmpty();
}
inline const std::string& Object::indices() const {
  // @@protoc_insertion_point(field_get:cura.proto.Object.indices)
  return _internal_indices();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void Object::set_indices(ArgT0&& arg0, ArgT... args) {
 
 _impl_.indices_.SetBytes(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:cura.proto.Object.indices)
}
inline std::string* Object::mutable_indices() {
  std::string* _s = _internal_mutable_indices();
  // @@protoc_insertion_point(field_mutable:cura.proto.Object.indices)
  return _s;
}
inline const std::string& Object::_internal_indices() const {
  return _impl_.indices_.Get();
}
inline void Object::_internal_set_indices(const std::string& value) {
  
  _impl_.indices_.Set(value, GetArenaForAllocation());
}
inline std::string* Object::_internal_mutable_indices() {
  
  return _impl_.indices_.Mutable(GetArenaForAllocation());
}
inline std::string* Object::release_indices() {
  // @@protoc_insertion_point(field_release:cura.proto.Object.indices)
  return _impl_.indices_.Release();
}
inline void Object::set_allocated_indices(std::string* indices) {
  if (indices != nullptr) {
    
  } else {
    
  }
  _impl_.indices_.SetAllocated(indices, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.indices_.IsDefault()) {
    _impl_.indices_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:cura.proto.Object.indices)
}

// repeated .cura.proto.Setting settings = 5;
inline int Object::_internal_settings_size() const {
  return _impl_.settings_.size();
}
inline int Object::settings_size() const {
  return _internal_settings_size();
}
inline void Object::clear_settings() {
  _impl_.settings_.Clear();
}
inline ::cura::proto::Setting* Object::mutable_settings(int index) {
  // @@protoc_insertion_point(field_mutable:cura.proto.Object.settings)
  return _impl_.settings_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::cura::proto::Setting >*
Object::mutable_settings() {
  // @@protoc_insertion_point(field_mutable_list:cura.proto.Object.settings)
  return &_impl_.settings_;
}
inline const ::cura::proto::Setting& Object::_internal_settings(int index) const {
  return _impl_.settings_.Get(index);
}
inline const ::cura::proto::Setting& Object::settings(int index) const {
  // @@protoc_insertion_point(field_get:cura.proto.Object.settings)
  return _internal_settings(index);
}
inline ::cura::proto::Setting* Object::_internal_add_settings() {
  return _impl_.settings_.Add();
}
inline ::cura::proto::Setting* Object::add_settings() {
  ::cura::proto::Setting* _add = _internal_add_settings();
  // @@protoc_insertion_point(field_add:cura.proto.Object.settings)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::cura::proto::Setting >&
Object::settings() const {
  // @@protoc_insertion_point(field_list:cura.proto.Object.settings)
  return _impl_.settings_;
}

// string name = 6;
inline void Object::clear_name() {
  _impl_.name_.ClearToEmpty();
}
inline const std::string& Object::name() const {
  // @@protoc_insertion_point(field_get:cura.proto.Object.name)
  return _internal_name();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void Object::set_name(ArgT0&& arg0, ArgT... args) {
 
 _impl_.name_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:cura.proto.Object.name)
}
inline std::string* Object::mutable_name() {
  std::string* _s = _internal_mutable_name();
  // @@protoc_insertion_point(field_mutable:cura.proto.Object.name)
  return _s;
}
inline const std::string& Object::_internal_name() const {
  return _impl_.name_.Get();
}
inline void Object::_internal_set_name(const std::string& value) {
  
  _impl_.name_.Set(value, GetArenaForAllocation());
}
inline std::string* Object::_internal_mutable_name() {
  
  return _impl_.name_.Mutable(GetArenaForAllocation());
}
inline std::string* Object::release_name() {
  // @@protoc_insertion_point(field_release:cura.proto.Object.name)
  return _impl_.name_.Release();
}
inline void Object::set_allocated_name(std::string* name) {
  if (name != nullptr) {
    
  } else {
    
  }
  _impl_.name_.SetAllocated(name, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.name_.IsDefault()) {
    _impl_.name_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:cura.proto.Object.name)
}

// -------------------------------------------------------------------

// Progress

// float amount = 1;
inline void Progress::clear_amount() {
  _impl_.amount_ = 0;
}
inline float Progress::_internal_amount() const {
  return _impl_.amount_;
}
inline float Progress::amount() const {
  // @@protoc_insertion_point(field_get:cura.proto.Progress.amount)
  return _internal_amount();
}
inline void Progress::_internal_set_amount(float value) {
  
  _impl_.amount_ = value;
}
inline void Progress::set_amount(float value) {
  _internal_set_amount(value);
  // @@protoc_insertion_point(field_set:cura.proto.Progress.amount)
}

// -------------------------------------------------------------------

// Layer

// int32 id = 1;
inline void Layer::clear_id() {
  _impl_.id_ = 0;
}
inline int32_t Layer::_internal_id() const {
  return _impl_.id_;
}
inline int32_t Layer::id() const {
  // @@protoc_insertion_point(field_get:cura.proto.Layer.id)
  return _internal_id();
}
inline void Layer::_internal_set_id(int32_t value) {
  
  _impl_.id_ = value;
}
inline void Layer::set_id(int32_t value) {
  _internal_set_id(value);
  // @@protoc_insertion_point(field_set:cura.proto.Layer.id)
}

// float height = 2;
inline void Layer::clear_height() {
  _impl_.height_ = 0;
}
inline float Layer::_internal_height() const {
  return _impl_.height_;
}
inline float Layer::height() const {
  // @@protoc_insertion_point(field_get:cura.proto.Layer.height)
  return _internal_height();
}
inline void Layer::_internal_set_height(float value) {
  
  _impl_.height_ = value;
}
inline void Layer::set_height(float value) {
  _internal_set_height(value);
  // @@protoc_insertion_point(field_set:cura.proto.Layer.height)
}

// float thickness = 3;
inline void Layer::clear_thickness() {
  _impl_.thickness_ = 0;
}
inline float Layer::_internal_thickness() const {
  return _impl_.thickness_;
}
inline float Layer::thickness() const {
  // @@protoc_insertion_point(field_get:cura.proto.Layer.thickness)
  return _internal_thickness();
}
inline void Layer::_internal_set_thickness(float value) {
  
  _impl_.thickness_ = value;
}
inline void Layer::set_thickness(float value) {
  _internal_set_thickness(value);
  // @@protoc_insertion_point(field_set:cura.proto.Layer.thickness)
}

// repeated .cura.proto.Polygon polygons = 4;
inline int Layer::_internal_polygons_size() const {
  return _impl_.polygons_.size();
}
inline int Layer::polygons_size() const {
  return _internal_polygons_size();
}
inline void Layer::clear_polygons() {
  _impl_.polygons_.Clear();
}
inline ::cura::proto::Polygon* Layer::mutable_polygons(int index) {
  // @@protoc_insertion_point(field_mutable:cura.proto.Layer.polygons)
  return _impl_.polygons_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::cura::proto::Polygon >*
Layer::mutable_polygons() {
  // @@protoc_insertion_point(field_mutable_list:cura.proto.Layer.polygons)
  return &_impl_.polygons_;
}
inline const ::cura::proto::Polygon& Layer::_internal_polygons(int index) const {
  return _impl_.polygons_.Get(index);
}
inline const ::cura::proto::Polygon& Layer::polygons(int index) const {
  // @@protoc_insertion_point(field_get:cura.proto.Layer.polygons)
  return _internal_polygons(index);
}
inline ::cura::proto::Polygon* Layer::_internal_add_polygons() {
  return _impl_.polygons_.Add();
}
inline ::cura::proto::Polygon* Layer::add_polygons() {
  ::cura::proto::Polygon* _add = _internal_add_polygons();
  // @@protoc_insertion_point(field_add:cura.proto.Layer.polygons)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::cura::proto::Polygon >&
Layer::polygons() const {
  // @@protoc_insertion_point(field_list:cura.proto.Layer.polygons)
  return _impl_.polygons_;
}

// -------------------------------------------------------------------

// Polygon

// .cura.proto.Polygon.Type type = 1;
inline void Polygon::clear_type() {
  _impl_.type_ = 0;
}
inline ::cura::proto::Polygon_Type Polygon::_internal_type() const {
  return static_cast< ::cura::proto::Polygon_Type >(_impl_.type_);
}
inline ::cura::proto::Polygon_Type Polygon::type() const {
  // @@protoc_insertion_point(field_get:cura.proto.Polygon.type)
  return _internal_type();
}
inline void Polygon::_internal_set_type(::cura::proto::Polygon_Type value) {
  
  _impl_.type_ = value;
}
inline void Polygon::set_type(::cura::proto::Polygon_Type value) {
  _internal_set_type(value);
  // @@protoc_insertion_point(field_set:cura.proto.Polygon.type)
}

// bytes points = 2;
inline void Polygon::clear_points() {
  _impl_.points_.ClearToEmpty();
}
inline const std::string& Polygon::points() const {
  // @@protoc_insertion_point(field_get:cura.proto.Polygon.points)
  return _internal_points();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void Polygon::set_points(ArgT0&& arg0, ArgT... args) {
 
 _impl_.points_.SetBytes(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:cura.proto.Polygon.points)
}
inline std::string* Polygon::mutable_points() {
  std::string* _s = _internal_mutable_points();
  // @@protoc_insertion_point(field_mutable:cura.proto.Polygon.points)
  return _s;
}
inline const std::string& Polygon::_internal_points() const {
  return _impl_.points_.Get();
}
inline void Polygon::_internal_set_points(const std::string& value) {
  
  _impl_.points_.Set(value, GetArenaForAllocation());
}
inline std::string* Polygon::_internal_mutable_points() {
  
  return _impl_.points_.Mutable(GetArenaForAllocation());
}
inline std::string* Polygon::release_points() {
  // @@protoc_insertion_point(field_release:cura.proto.Polygon.points)
  return _impl_.points_.Release();
}
inline void Polygon::set_allocated_points(std::string* points) {
  if (points != nullptr) {
    
  } else {
    
  }
  _impl_.points_.SetAllocated(points, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.points_.IsDefault()) {
    _impl_.points_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:cura.proto.Polygon.points)
}

// float line_width = 3;
inline void Polygon::clear_line_width() {
  _impl_.line_width_ = 0;
}
inline float Polygon::_internal_line_width() const {
  return _impl_.line_width_;
}
inline float Polygon::line_width() const {
  // @@protoc_insertion_point(field_get:cura.proto.Polygon.line_width)
  return _internal_line_width();
}
inline void Polygon::_internal_set_line_width(float value) {
  
  _impl_.line_width_ = value;
}
inline void Polygon::set_line_width(float value) {
  _internal_set_line_width(value);
  // @@protoc_insertion_point(field_set:cura.proto.Polygon.line_width)
}

// float line_thickness = 4;
inline void Polygon::clear_line_thickness() {
  _impl_.line_thickness_ = 0;
}
inline float Polygon::_internal_line_thickness() const {
  return _impl_.line_thickness_;
}
inline float Polygon::line_thickness() const {
  // @@protoc_insertion_point(field_get:cura.proto.Polygon.line_thickness)
  return _internal_line_thickness();
}
inline void Polygon::_internal_set_line_thickness(float value) {
  
  _impl_.line_thickness_ = value;
}
inline void Polygon::set_line_thickness(float value) {
  _internal_set_line_thickness(value);
  // @@protoc_insertion_point(field_set:cura.proto.Polygon.line_thickness)
}

// float line_feedrate = 5;
inline void Polygon::clear_line_feedrate() {
  _impl_.line_feedrate_ = 0;
}
inline float Polygon::_internal_line_feedrate() const {
  return _impl_.line_feedrate_;
}
inline float Polygon::line_feedrate() const {
  // @@protoc_insertion_point(field_get:cura.proto.Polygon.line_feedrate)
  return _internal_line_feedrate();
}
inline void Polygon::_internal_set_line_feedrate(float value) {
  
  _impl_.line_feedrate_ = value;
}
inline void Polygon::set_line_feedrate(float value) {
  _internal_set_line_feedrate(value);
  // @@protoc_insertion_point(field_set:cura.proto.Polygon.line_feedrate)
}

// -------------------------------------------------------------------

// LayerOptimized

// int32 id = 1;
inline void LayerOptimized::clear_id() {
  _impl_.id_ = 0;
}
inline int32_t LayerOptimized::_internal_id() const {
  return _impl_.id_;
}
inline int32_t LayerOptimized::id() const {
  // @@protoc_insertion_point(field_get:cura.proto.LayerOptimized.id)
  return _internal_id();
}
inline void LayerOptimized::_internal_set_id(int32_t value) {
  
  _impl_.id_ = value;
}
inline void LayerOptimized::set_id(int32_t value) {
  _internal_set_id(value);
  // @@protoc_insertion_point(field_set:cura.proto.LayerOptimized.id)
}

// float height = 2;
inline void LayerOptimized::clear_height() {
  _impl_.height_ = 0;
}
inline float LayerOptimized::_internal_height() const {
  return _impl_.height_;
}
inline float LayerOptimized::height() const {
  // @@protoc_insertion_point(field_get:cura.proto.LayerOptimized.height)
  return _internal_height();
}
inline void LayerOptimized::_internal_set_height(float value) {
  
  _impl_.height_ = value;
}
inline void LayerOptimized::set_height(float value) {
  _internal_set_height(value);
  // @@protoc_insertion_point(field_set:cura.proto.LayerOptimized.height)
}

// float thickness = 3;
inline void LayerOptimized::clear_thickness() {
  _impl_.thickness_ = 0;
}
inline float LayerOptimized::_internal_thickness() const {
  return _impl_.thickness_;
}
inline float LayerOptimized::thickness() const {
  // @@protoc_insertion_point(field_get:cura.proto.LayerOptimized.thickness)
  return _internal_thickness();
}
inline void LayerOptimized::_internal_set_thickness(float value) {
  
  _impl_.thickness_ = value;
}
inline void LayerOptimized::set_thickness(float value) {
  _internal_set_thickness(value);
  // @@protoc_insertion_point(field_set:cura.proto.LayerOptimized.thickness)
}

// repeated .cura.proto.PathSegment path_segment = 4;
inline int LayerOptimized::_internal_path_segment_size() const {
  return _impl_.path_segment_.size();
}
inline int LayerOptimized::path_segment_size() const {
  return _internal_path_segment_size();
}
inline void LayerOptimized::clear_path_segment() {
  _impl_.path_segment_.Clear();
}
inline ::cura::proto::PathSegment* LayerOptimized::mutable_path_segment(int index) {
  // @@protoc_insertion_point(field_mutable:cura.proto.LayerOptimized.path_segment)
  return _impl_.path_segment_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::cura::proto::PathSegment >*
LayerOptimized::mutable_path_segment() {
  // @@protoc_insertion_point(field_mutable_list:cura.proto.LayerOptimized.path_segment)
  return &_impl_.path_segment_;
}
inline const ::cura::proto::PathSegment& LayerOptimized::_internal_path_segment(int index) const {
  return _impl_.path_segment_.Get(index);
}
inline const ::cura::proto::PathSegment& LayerOptimized::path_segment(int index) const {
  // @@protoc_insertion_point(field_get:cura.proto.LayerOptimized.path_segment)
  return _internal_path_segment(index);
}
inline ::cura::proto::PathSegment* LayerOptimized::_internal_add_path_segment() {
  return _impl_.path_segment_.Add();
}
inline ::cura::proto::PathSegment* LayerOptimized::add_path_segment() {
  ::cura::proto::PathSegment* _add = _internal_add_path_segment();
  // @@protoc_insertion_point(field_add:cura.proto.LayerOptimized.path_segment)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::cura::proto::PathSegment >&
LayerOptimized::path_segment() const {
  // @@protoc_insertion_point(field_list:cura.proto.LayerOptimized.path_segment)
  return _impl_.path_segment_;
}

// -------------------------------------------------------------------

// PathSegment

// int32 extruder = 1;
inline void PathSegment::clear_extruder() {
  _impl_.extruder_ = 0;
}
inline int32_t PathSegment::_internal_extruder() const {
  return _impl_.extruder_;
}
inline int32_t PathSegment::extruder() const {
  // @@protoc_insertion_point(field_get:cura.proto.PathSegment.extruder)
  return _internal_extruder();
}
inline void PathSegment::_internal_set_extruder(int32_t value) {
  
  _impl_.extruder_ = value;
}
inline void PathSegment::set_extruder(int32_t value) {
  _internal_set_extruder(value);
  // @@protoc_insertion_point(field_set:cura.proto.PathSegment.extruder)
}

// .cura.proto.PathSegment.PointType point_type = 2;
inline void PathSegment::clear_point_type() {
  _impl_.point_type_ = 0;
}
inline ::cura::proto::PathSegment_PointType PathSegment::_internal_point_type() const {
  return static_cast< ::cura::proto::PathSegment_PointType >(_impl_.point_type_);
}
inline ::cura::proto::PathSegment_PointType PathSegment::point_type() const {
  // @@protoc_insertion_point(field_get:cura.proto.PathSegment.point_type)
  return _internal_point_type();
}
inline void PathSegment::_internal_set_point_type(::cura::proto::PathSegment_PointType value) {
  
  _impl_.point_type_ = value;
}
inline void PathSegment::set_point_type(::cura::proto::PathSegment_PointType value) {
  _internal_set_point_type(value);
  // @@protoc_insertion_point(field_set:cura.proto.PathSegment.point_type)
}

// bytes points = 3;
inline void PathSegment::clear_points() {
  _impl_.points_.ClearToEmpty();
}
inline const std::string& PathSegment::points() const {
  // @@protoc_insertion_point(field_get:cura.proto.PathSegment.points)
  return _internal_points();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void PathSegment::set_points(ArgT0&& arg0, ArgT... args) {
 
 _impl_.points_.SetBytes(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:cura.proto.PathSegment.points)
}
inline std::string* PathSegment::mutable_points() {
  std::string* _s = _internal_mutable_points();
  // @@protoc_insertion_point(field_mutable:cura.proto.PathSegment.points)
  return _s;
}
inline const std::string& PathSegment::_internal_points() const {
  return _impl_.points_.Get();
}
inline void PathSegment::_internal_set_points(const std::string& value) {
  
  _impl_.points_.Set(value, GetArenaForAllocation());
}
inline std::string* PathSegment::_internal_mutable_points() {
  
  return _impl_.points_.Mutable(GetArenaForAllocation());
}
inline std::string* PathSegment::release_points() {
  // @@protoc_insertion_point(field_release:cura.proto.PathSegment.points)
  return _impl_.points_.Release();
}
inline void PathSegment::set_allocated_points(std::string* points) {
  if (points != nullptr) {
    
  } else {
    
  }
  _impl_.points_.SetAllocated(points, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.points_.IsDefault()) {
    _impl_.points_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:cura.proto.PathSegment.points)
}

// bytes line_type = 4;
inline void PathSegment::clear_line_type() {
  _impl_.line_type_.ClearToEmpty();
}
inline const std::string& PathSegment::line_type() const {
  // @@protoc_insertion_point(field_get:cura.proto.PathSegment.line_type)
  return _internal_line_type();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void PathSegment::set_line_type(ArgT0&& arg0, ArgT... args) {
 
 _impl_.line_type_.SetBytes(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:cura.proto.PathSegment.line_type)
}
inline std::string* PathSegment::mutable_line_type() {
  std::string* _s = _internal_mutable_line_type();
  // @@protoc_insertion_point(field_mutable:cura.proto.PathSegment.line_type)
  return _s;
}
inline const std::string& PathSegment::_internal_line_type() const {
  return _impl_.line_type_.Get();
}
inline void PathSegment::_internal_set_line_type(const std::string& value) {
  
  _impl_.line_type_.Set(value, GetArenaForAllocation());
}
inline std::string* PathSegment::_internal_mutable_line_type() {
  
  return _impl_.line_type_.Mutable(GetArenaForAllocation());
}
inline std::string* PathSegment::release_line_type() {
  // @@protoc_insertion_point(field_release:cura.proto.PathSegment.line_type)
  return _impl_.line_type_.Release();
}
inline void PathSegment::set_allocated_line_type(std::string* line_type) {
  if (line_type != nullptr) {
    
  } else {
    
  }
  _impl_.line_type_.SetAllocated(line_type, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.line_type_.IsDefault()) {
    _impl_.line_type_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:cura.proto.PathSegment.line_type)
}

// bytes line_width = 5;
inline void PathSegment::clear_line_width() {
  _impl_.line_width_.ClearToEmpty();
}
inline const std::string& PathSegment::line_width() const {
  // @@protoc_insertion_point(field_get:cura.proto.PathSegment.line_width)
  return _internal_line_width();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void PathSegment::set_line_width(ArgT0&& arg0, ArgT... args) {
 
 _impl_.line_width_.SetBytes(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:cura.proto.PathSegment.line_width)
}
inline std::string* PathSegment::mutable_line_width() {
  std::string* _s = _internal_mutable_line_width();
  // @@protoc_insertion_point(field_mutable:cura.proto.PathSegment.line_width)
  return _s;
}
inline const std::string& PathSegment::_internal_line_width() const {
  return _impl_.line_width_.Get();
}
inline void PathSegment::_internal_set_line_width(const std::string& value) {
  
  _impl_.line_width_.Set(value, GetArenaForAllocation());
}
inline std::string* PathSegment::_internal_mutable_line_width() {
  
  return _impl_.line_width_.Mutable(GetArenaForAllocation());
}
inline std::string* PathSegment::release_line_width() {
  // @@protoc_insertion_point(field_release:cura.proto.PathSegment.line_width)
  return _impl_.line_width_.Release();
}
inline void PathSegment::set_allocated_line_width(std::string* line_width) {
  if (line_width != nullptr) {
    
  } else {
    
  }
  _impl_.line_width_.SetAllocated(line_width, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.line_width_.IsDefault()) {
    _impl_.line_width_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:cura.proto.PathSegment.line_width)
}

// bytes line_thickness = 6;
inline void PathSegment::clear_line_thickness() {
  _impl_.line_thickness_.ClearToEmpty();
}
inline const std::string& PathSegment::line_thickness() const {
  // @@protoc_insertion_point(field_get:cura.proto.PathSegment.line_thickness)
  return _internal_line_thickness();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void PathSegment::set_line_thickness(ArgT0&& arg0, ArgT... args) {
 
 _impl_.line_thickness_.SetBytes(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:cura.proto.PathSegment.line_thickness)
}
inline std::string* PathSegment::mutable_line_thickness() {
  std::string* _s = _internal_mutable_line_thickness();
  // @@protoc_insertion_point(field_mutable:cura.proto.PathSegment.line_thickness)
  return _s;
}
inline const std::string& PathSegment::_internal_line_thickness() const {
  return _impl_.line_thickness_.Get();
}
inline void PathSegment::_internal_set_line_thickness(const std::string& value) {
  
  _impl_.line_thickness_.Set(value, GetArenaForAllocation());
}
inline std::string* PathSegment::_internal_mutable_line_thickness() {
  
  return _impl_.line_thickness_.Mutable(GetArenaForAllocation());
}
inline std::string* PathSegment::release_line_thickness() {
  // @@protoc_insertion_point(field_release:cura.proto.PathSegment.line_thickness)
  return _impl_.line_thickness_.Release();
}
inline void PathSegment::set_allocated_line_thickness(std::string* line_thickness) {
  if (line_thickness != nullptr) {
    
  } else {
    
  }
  _impl_.line_thickness_.SetAllocated(line_thickness, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.line_thickness_.IsDefault()) {
    _impl_.line_thickness_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:cura.proto.PathSegment.line_thickness)
}

// bytes line_feedrate = 7;
inline void PathSegment::clear_line_feedrate() {
  _impl_.line_feedrate_.ClearToEmpty();
}
inline const std::string& PathSegment::line_feedrate() const {
  // @@protoc_insertion_point(field_get:cura.proto.PathSegment.line_feedrate)
  return _internal_line_feedrate();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void PathSegment::set_line_feedrate(ArgT0&& arg0, ArgT... args) {
 
 _impl_.line_feedrate_.SetBytes(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:cura.proto.PathSegment.line_feedrate)
}
inline std::string* PathSegment::mutable_line_feedrate() {
  std::string* _s = _internal_mutable_line_feedrate();
  // @@protoc_insertion_point(field_mutable:cura.proto.PathSegment.line_feedrate)
  return _s;
}
inline const std::string& PathSegment::_internal_line_feedrate() const {
  return _impl_.line_feedrate_.Get();
}
inline void PathSegment::_internal_set_line_feedrate(const std::string& value) {
  
  _impl_.line_feedrate_.Set(value, GetArenaForAllocation());
}
inline std::string* PathSegment::_internal_mutable_line_feedrate() {
  
  return _impl_.line_feedrate_.Mutable(GetArenaForAllocation());
}
inline std::string* PathSegment::release_line_feedrate() {
  // @@protoc_insertion_point(field_release:cura.proto.PathSegment.line_feedrate)
  return _impl_.line_feedrate_.Release();
}
inline void PathSegment::set_allocated_line_feedrate(std::string* line_feedrate) {
  if (line_feedrate != nullptr) {
    
  } else {
    
  }
  _impl_.line_feedrate_.SetAllocated(line_feedrate, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.line_feedrate_.IsDefault()) {
    _impl_.line_feedrate_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:cura.proto.PathSegment.line_feedrate)
}

// -------------------------------------------------------------------

// GCodeLayer

// bytes data = 2;
inline void GCodeLayer::clear_data() {
  _impl_.data_.ClearToEmpty();
}
inline const std::string& GCodeLayer::data() const {
  // @@protoc_insertion_point(field_get:cura.proto.GCodeLayer.data)
  return _internal_data();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void GCodeLayer::set_data(ArgT0&& arg0, ArgT... args) {
 
 _impl_.data_.SetBytes(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:cura.proto.GCodeLayer.data)
}
inline std::string* GCodeLayer::mutable_data() {
  std::string* _s = _internal_mutable_data();
  // @@protoc_insertion_point(field_mutable:cura.proto.GCodeLayer.data)
  return _s;
}
inline const std::string& GCodeLayer::_internal_data() const {
  return _impl_.data_.Get();
}
inline void GCodeLayer::_internal_set_data(const std::string& value) {
  
  _impl_.data_.Set(value, GetArenaForAllocation());
}
inline std::string* GCodeLayer::_internal_mutable_data() {
  
  return _impl_.data_.Mutable(GetArenaForAllocation());
}
inline std::string* GCodeLayer::release_data() {
  // @@protoc_insertion_point(field_release:cura.proto.GCodeLayer.data)
  return _impl_.data_.Release();
}
inline void GCodeLayer::set_allocated_data(std::string* data) {
  if (data != nullptr) {
    
  } else {
    
  }
  _impl_.data_.SetAllocated(data, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.data_.IsDefault()) {
    _impl_.data_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:cura.proto.GCodeLayer.data)
}

// -------------------------------------------------------------------

// PrintTimeMaterialEstimates

// float time_none = 1;
inline void PrintTimeMaterialEstimates::clear_time_none() {
  _impl_.time_none_ = 0;
}
inline float PrintTimeMaterialEstimates::_internal_time_none() const {
  return _impl_.time_none_;
}
inline float PrintTimeMaterialEstimates::time_none() const {
  // @@protoc_insertion_point(field_get:cura.proto.PrintTimeMaterialEstimates.time_none)
  return _internal_time_none();
}
inline void PrintTimeMaterialEstimates::_internal_set_time_none(float value) {
  
  _impl_.time_none_ = value;
}
inline void PrintTimeMaterialEstimates::set_time_none(float value) {
  _internal_set_time_none(value);
  // @@protoc_insertion_point(field_set:cura.proto.PrintTimeMaterialEstimates.time_none)
}

// float time_inset_0 = 2;
inline void PrintTimeMaterialEstimates::clear_time_inset_0() {
  _impl_.time_inset_0_ = 0;
}
inline float PrintTimeMaterialEstimates::_internal_time_inset_0() const {
  return _impl_.time_inset_0_;
}
inline float PrintTimeMaterialEstimates::time_inset_0() const {
  // @@protoc_insertion_point(field_get:cura.proto.PrintTimeMaterialEstimates.time_inset_0)
  return _internal_time_inset_0();
}
inline void PrintTimeMaterialEstimates::_internal_set_time_inset_0(float value) {
  
  _impl_.time_inset_0_ = value;
}
inline void PrintTimeMaterialEstimates::set_time_inset_0(float value) {
  _internal_set_time_inset_0(value);
  // @@protoc_insertion_point(field_set:cura.proto.PrintTimeMaterialEstimates.time_inset_0)
}

// float time_inset_x = 3;
inline void PrintTimeMaterialEstimates::clear_time_inset_x() {
  _impl_.time_inset_x_ = 0;
}
inline float PrintTimeMaterialEstimates::_internal_time_inset_x() const {
  return _impl_.time_inset_x_;
}
inline float PrintTimeMaterialEstimates::time_inset_x() const {
  // @@protoc_insertion_point(field_get:cura.proto.PrintTimeMaterialEstimates.time_inset_x)
  return _internal_time_inset_x();
}
inline void PrintTimeMaterialEstimates::_internal_set_time_inset_x(float value) {
  
  _impl_.time_inset_x_ = value;
}
inline void PrintTimeMaterialEstimates::set_time_inset_x(float value) {
  _internal_set_time_inset_x(value);
  // @@protoc_insertion_point(field_set:cura.proto.PrintTimeMaterialEstimates.time_inset_x)
}

// float time_skin = 4;
inline void PrintTimeMaterialEstimates::clear_time_skin() {
  _impl_.time_skin_ = 0;
}
inline float PrintTimeMaterialEstimates::_internal_time_skin() const {
  return _impl_.time_skin_;
}
inline float PrintTimeMaterialEstimates::time_skin() const {
  // @@protoc_insertion_point(field_get:cura.proto.PrintTimeMaterialEstimates.time_skin)
  return _internal_time_skin();
}
inline void PrintTimeMaterialEstimates::_internal_set_time_skin(float value) {
  
  _impl_.time_skin_ = value;
}
inline void PrintTimeMaterialEstimates::set_time_skin(float value) {
  _internal_set_time_skin(value);
  // @@protoc_insertion_point(field_set:cura.proto.PrintTimeMaterialEstimates.time_skin)
}

// float time_support = 5;
inline void PrintTimeMaterialEstimates::clear_time_support() {
  _impl_.time_support_ = 0;
}
inline float PrintTimeMaterialEstimates::_internal_time_support() const {
  return _impl_.time_support_;
}
inline float PrintTimeMaterialEstimates::time_support() const {
  // @@protoc_insertion_point(field_get:cura.proto.PrintTimeMaterialEstimates.time_support)
  return _internal_time_support();
}
inline void PrintTimeMaterialEstimates::_internal_set_time_support(float value) {
  
  _impl_.time_support_ = value;
}
inline void PrintTimeMaterialEstimates::set_time_support(float value) {
  _internal_set_time_support(value);
  // @@protoc_insertion_point(field_set:cura.proto.PrintTimeMaterialEstimates.time_support)
}

// float time_skirt = 6;
inline void PrintTimeMaterialEstimates::clear_time_skirt() {
  _impl_.time_skirt_ = 0;
}
inline float PrintTimeMaterialEstimates::_internal_time_skirt() const {
  return _impl_.time_skirt_;
}
inline float PrintTimeMaterialEstimates::time_skirt() const {
  // @@protoc_insertion_point(field_get:cura.proto.PrintTimeMaterialEstimates.time_skirt)
  return _internal_time_skirt();
}
inline void PrintTimeMaterialEstimates::_internal_set_time_skirt(float value) {
  
  _impl_.time_skirt_ = value;
}
inline void PrintTimeMaterialEstimates::set_time_skirt(float value) {
  _internal_set_time_skirt(value);
  // @@protoc_insertion_point(field_set:cura.proto.PrintTimeMaterialEstimates.time_skirt)
}

// float time_infill = 7;
inline void PrintTimeMaterialEstimates::clear_time_infill() {
  _impl_.time_infill_ = 0;
}
inline float PrintTimeMaterialEstimates::_internal_time_infill() const {
  return _impl_.time_infill_;
}
inline float PrintTimeMaterialEstimates::time_infill() const {
  // @@protoc_insertion_point(field_get:cura.proto.PrintTimeMaterialEstimates.time_infill)
  return _internal_time_infill();
}
inline void PrintTimeMaterialEstimates::_internal_set_time_infill(float value) {
  
  _impl_.time_infill_ = value;
}
inline void PrintTimeMaterialEstimates::set_time_infill(float value) {
  _internal_set_time_infill(value);
  // @@protoc_insertion_point(field_set:cura.proto.PrintTimeMaterialEstimates.time_infill)
}

// float time_support_infill = 8;
inline void PrintTimeMaterialEstimates::clear_time_support_infill() {
  _impl_.time_support_infill_ = 0;
}
inline float PrintTimeMaterialEstimates::_internal_time_support_infill() const {
  return _impl_.time_support_infill_;
}
inline float PrintTimeMaterialEstimates::time_support_infill() const {
  // @@protoc_insertion_point(field_get:cura.proto.PrintTimeMaterialEstimates.time_support_infill)
  return _internal_time_support_infill();
}
inline void PrintTimeMaterialEstimates::_internal_set_time_support_infill(float value) {
  
  _impl_.time_support_infill_ = value;
}
inline void PrintTimeMaterialEstimates::set_time_support_infill(float value) {
  _internal_set_time_support_infill(value);
  // @@protoc_insertion_point(field_set:cura.proto.PrintTimeMaterialEstimates.time_support_infill)
}

// float time_travel = 9;
inline void PrintTimeMaterialEstimates::clear_time_travel() {
  _impl_.time_travel_ = 0;
}
inline float PrintTimeMaterialEstimates::_internal_time_travel() const {
  return _impl_.time_travel_;
}
inline float PrintTimeMaterialEstimates::time_travel() const {
  // @@protoc_insertion_point(field_get:cura.proto.PrintTimeMaterialEstimates.time_travel)
  return _internal_time_travel();
}
inline void PrintTimeMaterialEstimates::_internal_set_time_travel(float value) {
  
  _impl_.time_travel_ = value;
}
inline void PrintTimeMaterialEstimates::set_time_travel(float value) {
  _internal_set_time_travel(value);
  // @@protoc_insertion_point(field_set:cura.proto.PrintTimeMaterialEstimates.time_travel)
}

// float time_retract = 10;
inline void PrintTimeMaterialEstimates::clear_time_retract() {
  _impl_.time_retract_ = 0;
}
inline float PrintTimeMaterialEstimates::_internal_time_retract() const {
  return _impl_.time_retract_;
}
inline float PrintTimeMaterialEstimates::time_retract() const {
  // @@protoc_insertion_point(field_get:cura.proto.PrintTimeMaterialEstimates.time_retract)
  return _internal_time_retract();
}
inline void PrintTimeMaterialEstimates::_internal_set_time_retract(float value) {
  
  _impl_.time_retract_ = value;
}
inline void PrintTimeMaterialEstimates::set_time_retract(float value) {
  _internal_set_time_retract(value);
  // @@protoc_insertion_point(field_set:cura.proto.PrintTimeMaterialEstimates.time_retract)
}

// float time_support_interface = 11;
inline void PrintTimeMaterialEstimates::clear_time_support_interface() {
  _impl_.time_support_interface_ = 0;
}
inline float PrintTimeMaterialEstimates::_internal_time_support_interface() const {
  return _impl_.time_support_interface_;
}
inline float PrintTimeMaterialEstimates::time_support_interface() const {
  // @@protoc_insertion_point(field_get:cura.proto.PrintTimeMaterialEstimates.time_support_interface)
  return _internal_time_support_interface();
}
inline void PrintTimeMaterialEstimates::_internal_set_time_support_interface(float value) {
  
  _impl_.time_support_interface_ = value;
}
inline void PrintTimeMaterialEstimates::set_time_support_interface(float value) {
  _internal_set_time_support_interface(value);
  // @@protoc_insertion_point(field_set:cura.proto.PrintTimeMaterialEstimates.time_support_interface)
}

// float time_prime_tower = 12;
inline void PrintTimeMaterialEstimates::clear_time_prime_tower() {
  _impl_.time_prime_tower_ = 0;
}
inline float PrintTimeMaterialEstimates::_internal_time_prime_tower() const {
  return _impl_.time_prime_tower_;
}
inline float PrintTimeMaterialEstimates::time_prime_tower() const {
  // @@protoc_insertion_point(field_get:cura.proto.PrintTimeMaterialEstimates.time_prime_tower)
  return _internal_time_prime_tower();
}
inline void PrintTimeMaterialEstimates::_internal_set_time_prime_tower(float value) {
  
  _impl_.time_prime_tower_ = value;
}
inline void PrintTimeMaterialEstimates::set_time_prime_tower(float value) {
  _internal_set_time_prime_tower(value);
  // @@protoc_insertion_point(field_set:cura.proto.PrintTimeMaterialEstimates.time_prime_tower)
}

// repeated .cura.proto.MaterialEstimates materialEstimates = 13;
inline int PrintTimeMaterialEstimates::_internal_materialestimates_size() const {
  return _impl_.materialestimates_.size();
}
inline int PrintTimeMaterialEstimates::materialestimates_size() const {
  return _internal_materialestimates_size();
}
inline void PrintTimeMaterialEstimates::clear_materialestimates() {
  _impl_.materialestimates_.Clear();
}
inline ::cura::proto::MaterialEstimates* PrintTimeMaterialEstimates::mutable_materialestimates(int index) {
  // @@protoc_insertion_point(field_mutable:cura.proto.PrintTimeMaterialEstimates.materialEstimates)
  return _impl_.materialestimates_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::cura::proto::MaterialEstimates >*
PrintTimeMaterialEstimates::mutable_materialestimates() {
  // @@protoc_insertion_point(field_mutable_list:cura.proto.PrintTimeMaterialEstimates.materialEstimates)
  return &_impl_.materialestimates_;
}
inline const ::cura::proto::MaterialEstimates& PrintTimeMaterialEstimates::_internal_materialestimates(int index) const {
  return _impl_.materialestimates_.Get(index);
}
inline const ::cura::proto::MaterialEstimates& PrintTimeMaterialEstimates::materialestimates(int index) const {
  // @@protoc_insertion_point(field_get:cura.proto.PrintTimeMaterialEstimates.materialEstimates)
  return _internal_materialestimates(index);
}
inline ::cura::proto::MaterialEstimates* PrintTimeMaterialEstimates::_internal_add_materialestimates() {
  return _impl_.materialestimates_.Add();
}
inline ::cura::proto::MaterialEstimates* PrintTimeMaterialEstimates::add_materialestimates() {
  ::cura::proto::MaterialEstimates* _add = _internal_add_materialestimates();
  // @@protoc_insertion_point(field_add:cura.proto.PrintTimeMaterialEstimates.materialEstimates)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::cura::proto::MaterialEstimates >&
PrintTimeMaterialEstimates::materialestimates() const {
  // @@protoc_insertion_point(field_list:cura.proto.PrintTimeMaterialEstimates.materialEstimates)
  return _impl_.materialestimates_;
}

// -------------------------------------------------------------------

// MaterialEstimates

// int64 id = 1;
inline void MaterialEstimates::clear_id() {
  _impl_.id_ = int64_t{0};
}
inline int64_t MaterialEstimates::_internal_id() const {
  return _impl_.id_;
}
inline int64_t MaterialEstimates::id() const {
  // @@protoc_insertion_point(field_get:cura.proto.MaterialEstimates.id)
  return _internal_id();
}
inline void MaterialEstimates::_internal_set_id(int64_t value) {
  
  _impl_.id_ = value;
}
inline void MaterialEstimates::set_id(int64_t value) {
  _internal_set_id(value);
  // @@protoc_insertion_point(field_set:cura.proto.MaterialEstimates.id)
}

// float material_amount = 2;
inline void MaterialEstimates::clear_material_amount() {
  _impl_.material_amount_ = 0;
}
inline float MaterialEstimates::_internal_material_amount() const {
  return _impl_.material_amount_;
}
inline float MaterialEstimates::material_amount() const {
  // @@protoc_insertion_point(field_get:cura.proto.MaterialEstimates.material_amount)
  return _internal_material_amount();
}
inline void MaterialEstimates::_internal_set_material_amount(float value) {
  
  _impl_.material_amount_ = value;
}
inline void MaterialEstimates::set_material_amount(float value) {
  _internal_set_material_amount(value);
  // @@protoc_insertion_point(field_set:cura.proto.MaterialEstimates.material_amount)
}

// -------------------------------------------------------------------

// SettingList

// repeated .cura.proto.Setting settings = 1;
inline int SettingList::_internal_settings_size() const {
  return _impl_.settings_.size();
}
inline int SettingList::settings_size() const {
  return _internal_settings_size();
}
inline void SettingList::clear_settings() {
  _impl_.settings_.Clear();
}
inline ::cura::proto::Setting* SettingList::mutable_settings(int index) {
  // @@protoc_insertion_point(field_mutable:cura.proto.SettingList.settings)
  return _impl_.settings_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::cura::proto::Setting >*
SettingList::mutable_settings() {
  // @@protoc_insertion_point(field_mutable_list:cura.proto.SettingList.settings)
  return &_impl_.settings_;
}
inline const ::cura::proto::Setting& SettingList::_internal_settings(int index) const {
  return _impl_.settings_.Get(index);
}
inline const ::cura::proto::Setting& SettingList::settings(int index) const {
  // @@protoc_insertion_point(field_get:cura.proto.SettingList.settings)
  return _internal_settings(index);
}
inline ::cura::proto::Setting* SettingList::_internal_add_settings() {
  return _impl_.settings_.Add();
}
inline ::cura::proto::Setting* SettingList::add_settings() {
  ::cura::proto::Setting* _add = _internal_add_settings();
  // @@protoc_insertion_point(field_add:cura.proto.SettingList.settings)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::cura::proto::Setting >&
SettingList::settings() const {
  // @@protoc_insertion_point(field_list:cura.proto.SettingList.settings)
  return _impl_.settings_;
}

// -------------------------------------------------------------------

// Setting

// string name = 1;
inline void Setting::clear_name() {
  _impl_.name_.ClearToEmpty();
}
inline const std::string& Setting::name() const {
  // @@protoc_insertion_point(field_get:cura.proto.Setting.name)
  return _internal_name();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void Setting::set_name(ArgT0&& arg0, ArgT... args) {
 
 _impl_.name_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:cura.proto.Setting.name)
}
inline std::string* Setting::mutable_name() {
  std::string* _s = _internal_mutable_name();
  // @@protoc_insertion_point(field_mutable:cura.proto.Setting.name)
  return _s;
}
inline const std::string& Setting::_internal_name() const {
  return _impl_.name_.Get();
}
inline void Setting::_internal_set_name(const std::string& value) {
  
  _impl_.name_.Set(value, GetArenaForAllocation());
}
inline std::string* Setting::_internal_mutable_name() {
  
  return _impl_.name_.Mutable(GetArenaForAllocation());
}
inline std::string* Setting::release_name() {
  // @@protoc_insertion_point(field_release:cura.proto.Setting.name)
  return _impl_.name_.Release();
}
inline void Setting::set_allocated_name(std::string* name) {
  if (name != nullptr) {
    
  } else {
    
  }
  _impl_.name_.SetAllocated(name, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.name_.IsDefault()) {
    _impl_.name_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:cura.proto.Setting.name)
}

// bytes value = 2;
inline void Setting::clear_value() {
  _impl_.value_.ClearToEmpty();
}
inline const std::string& Setting::value() const {
  // @@protoc_insertion_point(field_get:cura.proto.Setting.value)
  return _internal_value();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void Setting::set_value(ArgT0&& arg0, ArgT... args) {
 
 _impl_.value_.SetBytes(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:cura.proto.Setting.value)
}
inline std::string* Setting::mutable_value() {
  std::string* _s = _internal_mutable_value();
  // @@protoc_insertion_point(field_mutable:cura.proto.Setting.value)
  return _s;
}
inline const std::string& Setting::_internal_value() const {
  return _impl_.value_.Get();
}
inline void Setting::_internal_set_value(const std::string& value) {
  
  _impl_.value_.Set(value, GetArenaForAllocation());
}
inline std::string* Setting::_internal_mutable_value() {
  
  return _impl_.value_.Mutable(GetArenaForAllocation());
}
inline std::string* Setting::release_value() {
  // @@protoc_insertion_point(field_release:cura.proto.Setting.value)
  return _impl_.value_.Release();
}
inline void Setting::set_allocated_value(std::string* value) {
  if (value != nullptr) {
    
  } else {
    
  }
  _impl_.value_.SetAllocated(value, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.value_.IsDefault()) {
    _impl_.value_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:cura.proto.Setting.value)
}

// -------------------------------------------------------------------

// SettingExtruder

// string name = 1;
inline void SettingExtruder::clear_name() {
  _impl_.name_.ClearToEmpty();
}
inline const std::string& SettingExtruder::name() const {
  // @@protoc_insertion_point(field_get:cura.proto.SettingExtruder.name)
  return _internal_name();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void SettingExtruder::set_name(ArgT0&& arg0, ArgT... args) {
 
 _impl_.name_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:cura.proto.SettingExtruder.name)
}
inline std::string* SettingExtruder::mutable_name() {
  std::string* _s = _internal_mutable_name();
  // @@protoc_insertion_point(field_mutable:cura.proto.SettingExtruder.name)
  return _s;
}
inline const std::string& SettingExtruder::_internal_name() const {
  return _impl_.name_.Get();
}
inline void SettingExtruder::_internal_set_name(const std::string& value) {
  
  _impl_.name_.Set(value, GetArenaForAllocation());
}
inline std::string* SettingExtruder::_internal_mutable_name() {
  
  return _impl_.name_.Mutable(GetArenaForAllocation());
}
inline std::string* SettingExtruder::release_name() {
  // @@protoc_insertion_point(field_release:cura.proto.SettingExtruder.name)
  return _impl_.name_.Release();
}
inline void SettingExtruder::set_allocated_name(std::string* name) {
  if (name != nullptr) {
    
  } else {
    
  }
  _impl_.name_.SetAllocated(name, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.name_.IsDefault()) {
    _impl_.name_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:cura.proto.SettingExtruder.name)
}

// int32 extruder = 2;
inline void SettingExtruder::clear_extruder() {
  _impl_.extruder_ = 0;
}
inline int32_t SettingExtruder::_internal_extruder() const {
  return _impl_.extruder_;
}
inline int32_t SettingExtruder::extruder() const {
  // @@protoc_insertion_point(field_get:cura.proto.SettingExtruder.extruder)
  return _internal_extruder();
}
inline void SettingExtruder::_internal_set_extruder(int32_t value) {
  
  _impl_.extruder_ = value;
}
inline void SettingExtruder::set_extruder(int32_t value) {
  _internal_set_extruder(value);
  // @@protoc_insertion_point(field_set:cura.proto.SettingExtruder.extruder)
}

// -------------------------------------------------------------------

// GCodePrefix

// bytes data = 2;
inline void GCodePrefix::clear_data() {
  _impl_.data_.ClearToEmpty();
}
inline const std::string& GCodePrefix::data() const {
  // @@protoc_insertion_point(field_get:cura.proto.GCodePrefix.data)
  return _internal_data();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void GCodePrefix::set_data(ArgT0&& arg0, ArgT... args) {
 
 _impl_.data_.SetBytes(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:cura.proto.GCodePrefix.data)
}
inline std::string* GCodePrefix::mutable_data() {
  std::string* _s = _internal_mutable_data();
  // @@protoc_insertion_point(field_mutable:cura.proto.GCodePrefix.data)
  return _s;
}
inline const std::string& GCodePrefix::_internal_data() const {
  return _impl_.data_.Get();
}
inline void GCodePrefix::_internal_set_data(const std::string& value) {
  
  _impl_.data_.Set(value, GetArenaForAllocation());
}
inline std::string* GCodePrefix::_internal_mutable_data() {
  
  return _impl_.data_.Mutable(GetArenaForAllocation());
}
inline std::string* GCodePrefix::release_data() {
  // @@protoc_insertion_point(field_release:cura.proto.GCodePrefix.data)
  return _impl_.data_.Release();
}
inline void GCodePrefix::set_allocated_data(std::string* data) {
  if (data != nullptr) {
    
  } else {
    
  }
  _impl_.data_.SetAllocated(data, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.data_.IsDefault()) {
    _impl_.data_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:cura.proto.GCodePrefix.data)
}

// -------------------------------------------------------------------

// SliceUUID

// string slice_uuid = 1;
inline void SliceUUID::clear_slice_uuid() {
  _impl_.slice_uuid_.ClearToEmpty();
}
inline const std::string& SliceUUID::slice_uuid() const {
  // @@protoc_insertion_point(field_get:cura.proto.SliceUUID.slice_uuid)
  return _internal_slice_uuid();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void SliceUUID::set_slice_uuid(ArgT0&& arg0, ArgT... args) {
 
 _impl_.slice_uuid_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:cura.proto.SliceUUID.slice_uuid)
}
inline std::string* SliceUUID::mutable_slice_uuid() {
  std::string* _s = _internal_mutable_slice_uuid();
  // @@protoc_insertion_point(field_mutable:cura.proto.SliceUUID.slice_uuid)
  return _s;
}
inline const std::string& SliceUUID::_internal_slice_uuid() const {
  return _impl_.slice_uuid_.Get();
}
inline void SliceUUID::_internal_set_slice_uuid(const std::string& value) {
  
  _impl_.slice_uuid_.Set(value, GetArenaForAllocation());
}
inline std::string* SliceUUID::_internal_mutable_slice_uuid() {
  
  return _impl_.slice_uuid_.Mutable(GetArenaForAllocation());
}
inline std::string* SliceUUID::release_slice_uuid() {
  // @@protoc_insertion_point(field_release:cura.proto.SliceUUID.slice_uuid)
  return _impl_.slice_uuid_.Release();
}
inline void SliceUUID::set_allocated_slice_uuid(std::string* slice_uuid) {
  if (slice_uuid != nullptr) {
    
  } else {
    
  }
  _impl_.slice_uuid_.SetAllocated(slice_uuid, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.slice_uuid_.IsDefault()) {
    _impl_.slice_uuid_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:cura.proto.SliceUUID.slice_uuid)
}

// -------------------------------------------------------------------

// SlicingFinished

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace proto
}  // namespace cura

PROTOBUF_NAMESPACE_OPEN

template <> struct is_proto_enum< ::cura::proto::Polygon_Type> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::cura::proto::Polygon_Type>() {
  return ::cura::proto::Polygon_Type_descriptor();
}
template <> struct is_proto_enum< ::cura::proto::PathSegment_PointType> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::cura::proto::PathSegment_PointType>() {
  return ::cura::proto::PathSegment_PointType_descriptor();
}
template <> struct is_proto_enum< ::cura::proto::SlotID> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::cura::proto::SlotID>() {
  return ::cura::proto::SlotID_descriptor();
}

PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_Cura_2eproto

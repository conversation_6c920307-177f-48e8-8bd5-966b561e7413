# CuraEngine 自动化构建脚本 (PowerShell版本)
param(
    [switch]$Clean = $false,
    [switch]$SkipConan = $false
)

Write-Host "========================================" -ForegroundColor Green
Write-Host "CuraEngine 自动化构建脚本 (PowerShell)" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green

# 检查是否在正确的目录
if (-not (Test-Path "conanfile.py")) {
    Write-Host "错误: 请在CuraEngine根目录运行此脚本" -ForegroundColor Red
    Read-Host "按任意键退出"
    exit 1
}

# 错误处理函数
function Exit-OnError {
    param($Message)
    Write-Host "错误: $Message" -ForegroundColor Red
    Read-Host "按任意键退出"
    exit 1
}

try {
    if ($Clean) {
        Write-Host "步骤 1: 清理之前的构建..." -ForegroundColor Yellow
        if (Test-Path "build") {
            Remove-Item -Recurse -Force "build"
        }
    }

    if (-not $SkipConan) {
        Write-Host "步骤 2: 重新安装Conan依赖项（修复Boost配置）..." -ForegroundColor Yellow
        $conanResult = & conan install . --build=missing --update -o "boost/*:header_only=False" -o "boost/*:without_exception=False"
        if ($LASTEXITCODE -ne 0) {
            Exit-OnError "Conan依赖项安装失败"
        }
    }

    Write-Host "步骤 3: 配置CMake项目..." -ForegroundColor Yellow
    
    # 尝试使用Visual Studio环境
    $vsPath = "C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\Build\vcvars64.bat"
    if (Test-Path $vsPath) {
        Write-Host "使用Visual Studio 2022环境..." -ForegroundColor Cyan
        cmd /c "`"$vsPath`" && cmake --preset conan-release"
    } else {
        # 直接尝试cmake
        cmake --preset conan-release
    }
    
    if ($LASTEXITCODE -ne 0) {
        Exit-OnError "CMake配置失败"
    }

    Write-Host "步骤 4: 构建CuraEngine..." -ForegroundColor Yellow
    cmake --build build\Release --config Release
    
    if ($LASTEXITCODE -ne 0) {
        Exit-OnError "构建失败"
    }

    Write-Host "========================================" -ForegroundColor Green
    Write-Host "构建成功完成！" -ForegroundColor Green
    Write-Host "CuraEngine.exe 位置: build\Release\CuraEngine.exe" -ForegroundColor Green
    Write-Host "========================================" -ForegroundColor Green

    # 检查生成的文件
    if (Test-Path "build\Release\CuraEngine.exe") {
        Write-Host "验证: CuraEngine.exe 已成功生成" -ForegroundColor Green
        Get-ChildItem "build\Release\CuraEngine.exe" | Format-Table Name, Length, LastWriteTime
    } else {
        Write-Host "警告: CuraEngine.exe 未找到" -ForegroundColor Yellow
    }

} catch {
    Write-Host "发生未预期的错误: $_" -ForegroundColor Red
    Read-Host "按任意键退出"
    exit 1
}

Read-Host "按任意键退出"

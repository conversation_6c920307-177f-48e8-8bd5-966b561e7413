########### AGGREGATED COMPONENTS AND DEPENDENCIES FOR THE MULTI CONFIG #####################
#############################################################################################

set(mapbox-variant_COMPONENT_NAMES "")
if(DEFINED mapbox-variant_FIND_DEPENDENCY_NAMES)
  list(APPEND mapbox-variant_FIND_DEPENDENCY_NAMES )
  list(REMOVE_DUPLICATES mapbox-variant_FIND_DEPENDENCY_NAMES)
else()
  set(mapbox-variant_FIND_DEPENDENCY_NAMES )
endif()

########### VARIABLES #######################################################################
#############################################################################################
set(mapbox-variant_PACKAGE_FOLDER_RELEASE "C:/Users/<USER>/.conan2/p/mapbo1b62b48748b82/p")
set(mapbox-variant_BUILD_MODULES_PATHS_RELEASE )


set(mapbox-variant_INCLUDE_DIRS_RELEASE "${mapbox-variant_PACKAGE_FOLDER_RELEASE}/include")
set(mapbox-variant_RES_DIRS_RELEASE )
set(mapbox-variant_DEFINITIONS_RELEASE )
set(mapbox-variant_SHARED_LINK_FLAGS_RELEASE )
set(mapbox-variant_EXE_LINK_FLAGS_RELEASE )
set(mapbox-variant_OBJECTS_RELEASE )
set(mapbox-variant_COMPILE_DEFINITIONS_RELEASE )
set(mapbox-variant_COMPILE_OPTIONS_C_RELEASE )
set(mapbox-variant_COMPILE_OPTIONS_CXX_RELEASE )
set(mapbox-variant_LIB_DIRS_RELEASE )
set(mapbox-variant_BIN_DIRS_RELEASE )
set(mapbox-variant_LIBRARY_TYPE_RELEASE UNKNOWN)
set(mapbox-variant_IS_HOST_WINDOWS_RELEASE 1)
set(mapbox-variant_LIBS_RELEASE )
set(mapbox-variant_SYSTEM_LIBS_RELEASE )
set(mapbox-variant_FRAMEWORK_DIRS_RELEASE )
set(mapbox-variant_FRAMEWORKS_RELEASE )
set(mapbox-variant_BUILD_DIRS_RELEASE )
set(mapbox-variant_NO_SONAME_MODE_RELEASE FALSE)


# COMPOUND VARIABLES
set(mapbox-variant_COMPILE_OPTIONS_RELEASE
    "$<$<COMPILE_LANGUAGE:CXX>:${mapbox-variant_COMPILE_OPTIONS_CXX_RELEASE}>"
    "$<$<COMPILE_LANGUAGE:C>:${mapbox-variant_COMPILE_OPTIONS_C_RELEASE}>")
set(mapbox-variant_LINKER_FLAGS_RELEASE
    "$<$<STREQUAL:$<TARGET_PROPERTY:TYPE>,SHARED_LIBRARY>:${mapbox-variant_SHARED_LINK_FLAGS_RELEASE}>"
    "$<$<STREQUAL:$<TARGET_PROPERTY:TYPE>,MODULE_LIBRARY>:${mapbox-variant_SHARED_LINK_FLAGS_RELEASE}>"
    "$<$<STREQUAL:$<TARGET_PROPERTY:TYPE>,EXECUTABLE>:${mapbox-variant_EXE_LINK_FLAGS_RELEASE}>")


set(mapbox-variant_COMPONENTS_RELEASE )
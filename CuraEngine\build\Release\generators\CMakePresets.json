{"version": 3, "vendor": {"conan": {}}, "cmakeMinimumRequired": {"major": 3, "minor": 15, "patch": 0}, "configurePresets": [{"name": "conan-release", "displayName": "'conan-release' config", "description": "'conan-release' configure using 'Ninja' generator", "generator": "Ninja", "cacheVariables": {"CMAKE_POLICY_DEFAULT_CMP0091": "NEW", "BUILD_TESTING": "OFF", "CMAKE_BUILD_TYPE": "Release"}, "toolset": {"value": "v143", "strategy": "external"}, "architecture": {"value": "x64", "strategy": "external"}, "toolchainFile": "generators\\conan_toolchain.cmake", "binaryDir": "C:\\Users\\<USER>\\vscode\\Cura-Dev\\CuraEngine\\build\\Release"}], "buildPresets": [{"name": "conan-release", "configurePreset": "conan-release", "jobs": 6}], "testPresets": [{"name": "conan-release", "configurePreset": "conan-release", "environment": {"PATH": "C:\\Users\\<USER>\\.conan2\\p\\b\\arcusee7666f716199\\p\\bin;C:\\Users\\<USER>\\.conan2\\p\\b\\clippf8ddcd4a5d961\\p\\bin;$penv{PATH}", "GRPC_DEFAULT_SSL_ROOTS_FILE_PATH": "C:\\Users\\<USER>\\.conan2\\p\\grpc8b6c73b4e5756\\p\\res\\grpc\\roots.pem", "OPENSSL_MODULES": "C:\\Users\\<USER>\\.conan2\\p\\opens13d3a0b103336\\p\\lib\\ossl-modules"}}]}
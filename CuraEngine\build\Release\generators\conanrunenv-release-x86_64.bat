@echo off
chcp 65001 > nul
setlocal
echo @echo off > "%~dp0/deactivate_conanrunenv-release-x86_64.bat"
echo echo Restoring environment >> "%~dp0/deactivate_conanrunenv-release-x86_64.bat"
for %%v in (PATH GRPC_DEFAULT_SSL_ROOTS_FILE_PATH OPENSSL_MODULES) do (
    set foundenvvar=
    for /f "delims== tokens=1,2" %%a in ('set') do (
        if /I "%%a" == "%%v" (
            echo set "%%a=%%b">> "%~dp0/deactivate_conanrunenv-release-x86_64.bat"
            set foundenvvar=1
        )
    )
    if not defined foundenvvar (
        echo set %%v=>> "%~dp0/deactivate_conanrunenv-release-x86_64.bat"
    )
)
endlocal


set "PATH=C:\Users\<USER>\.conan2\p\b\arcusee7666f716199\p\bin;C:\Users\<USER>\.conan2\p\b\clippf8ddcd4a5d961\p\bin;%PATH%"
set "GRPC_DEFAULT_SSL_ROOTS_FILE_PATH=C:\Users\<USER>\.conan2\p\grpc8b6c73b4e5756\p\res\grpc\roots.pem"
set "OPENSSL_MODULES=C:\Users\<USER>\.conan2\p\opens13d3a0b103336\p\lib\ossl-modules"
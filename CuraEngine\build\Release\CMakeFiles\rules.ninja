# CMAKE generated file: DO NOT EDIT!
# Generated by "Ninja" Generator, CMake Version 4.1

# This file contains all the rules used to get the outputs files
# built from the input files.
# It is included in the main 'build.ninja'.

# =============================================================================
# Project: CuraEngine
# Configurations: Release
# =============================================================================
# =============================================================================

#############################################
# localized /showIncludes string

msvc_deps_prefix = 注意: 包含文件:  


#############################################
# Rule for generating CXX dependencies.

rule CXX_SCAN___CuraEngine_Release
  deps = msvc
  command = C:\PROGRA~1\MICROS~1\2022\COMMUN~1\VC\Tools\MSVC\1444~1.352\bin\Hostx64\x64\cl.exe $DEFINES $INCLUDES $FLAGS $in -nologo -TP -showIncludes -scanDependencies $DYNDEP_INTERMEDIATE_FILE -Fo$OBJ_FILE
  description = Scanning $in for CXX dependencies


#############################################
# Rule to generate ninja dyndep files for CXX.

rule CXX_DYNDEP___CuraEngine_Release
  command = "C:\Program Files\CMake\bin\cmake.exe" -E cmake_ninja_dyndep --tdi=CMakeFiles\_CuraEngine.dir\CXXDependInfo.json --lang=CXX --modmapfmt=msvc --dd=$out @$out.rsp
  description = Generating CXX dyndep file $out
  rspfile = $out.rsp
  rspfile_content = $in
  restat = 1


#############################################
# Rule for compiling CXX files.

rule CXX_COMPILER___CuraEngine_scanned_Release
  deps = msvc
  command = ${LAUNCHER}${CODE_CHECK}C:\PROGRA~1\MICROS~1\2022\COMMUN~1\VC\Tools\MSVC\1444~1.352\bin\Hostx64\x64\cl.exe  /nologo /TP $DEFINES $INCLUDES $FLAGS /showIncludes @$DYNDEP_MODULE_MAP_FILE /Fo$out /Fd$TARGET_COMPILE_PDB /FS -c $in
  description = Building CXX object $out


#############################################
# Rule for compiling CXX files.

rule CXX_COMPILER___CuraEngine_unscanned_Release
  deps = msvc
  command = ${LAUNCHER}${CODE_CHECK}C:\PROGRA~1\MICROS~1\2022\COMMUN~1\VC\Tools\MSVC\1444~1.352\bin\Hostx64\x64\cl.exe  /nologo /TP $DEFINES $INCLUDES $FLAGS /showIncludes /Fo$out /Fd$TARGET_COMPILE_PDB /FS -c $in
  description = Building CXX object $out


#############################################
# Rule for linking CXX static library.

rule CXX_STATIC_LIBRARY_LINKER___CuraEngine_Release
  command = C:\WINDOWS\system32\cmd.exe /C "$PRE_LINK && C:\PROGRA~1\MICROS~1\2022\COMMUN~1\VC\Tools\MSVC\1444~1.352\bin\Hostx64\x64\lib.exe /nologo $LINK_FLAGS /out:$TARGET_FILE @$RSP_FILE && $POST_BUILD"
  description = Linking CXX static library $TARGET_FILE
  rspfile = $RSP_FILE
  rspfile_content = $in_newline $LINK_PATH $LINK_LIBRARIES
  restat = $RESTAT


#############################################
# Rule for generating CXX dependencies.

rule CXX_SCAN__CuraEngine_Release
  deps = msvc
  command = C:\PROGRA~1\MICROS~1\2022\COMMUN~1\VC\Tools\MSVC\1444~1.352\bin\Hostx64\x64\cl.exe $DEFINES $INCLUDES $FLAGS $in -nologo -TP -showIncludes -scanDependencies $DYNDEP_INTERMEDIATE_FILE -Fo$OBJ_FILE
  description = Scanning $in for CXX dependencies


#############################################
# Rule to generate ninja dyndep files for CXX.

rule CXX_DYNDEP__CuraEngine_Release
  command = "C:\Program Files\CMake\bin\cmake.exe" -E cmake_ninja_dyndep --tdi=CMakeFiles\CuraEngine.dir\CXXDependInfo.json --lang=CXX --modmapfmt=msvc --dd=$out @$out.rsp
  description = Generating CXX dyndep file $out
  rspfile = $out.rsp
  rspfile_content = $in
  restat = 1


#############################################
# Rule for compiling CXX files.

rule CXX_COMPILER__CuraEngine_scanned_Release
  deps = msvc
  command = ${LAUNCHER}${CODE_CHECK}C:\PROGRA~1\MICROS~1\2022\COMMUN~1\VC\Tools\MSVC\1444~1.352\bin\Hostx64\x64\cl.exe  /nologo /TP $DEFINES $INCLUDES $FLAGS /showIncludes @$DYNDEP_MODULE_MAP_FILE /Fo$out /Fd$TARGET_COMPILE_PDB /FS -c $in
  description = Building CXX object $out


#############################################
# Rule for compiling CXX files.

rule CXX_COMPILER__CuraEngine_unscanned_Release
  deps = msvc
  command = ${LAUNCHER}${CODE_CHECK}C:\PROGRA~1\MICROS~1\2022\COMMUN~1\VC\Tools\MSVC\1444~1.352\bin\Hostx64\x64\cl.exe  /nologo /TP $DEFINES $INCLUDES $FLAGS /showIncludes /Fo$out /Fd$TARGET_COMPILE_PDB /FS -c $in
  description = Building CXX object $out


#############################################
# Rule for compiling RC files.

rule RC_COMPILER__CuraEngine_unscanned_Release
  depfile = $DEP_FILE
  deps = gcc
  command = C:/PROGRA~1/CMake/bin/cmcldeps.exe RC $in $DEP_FILE $out "注意: 包含文件:  " "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/bin/Hostx64/x64/cl.exe" ${LAUNCHER}${CODE_CHECK}C:\PROGRA~2\WI3CF2~1\10\bin\100261~1.0\x64\rc.exe $DEFINES $INCLUDES $FLAGS /fo $out $in
  description = Building RC object $out


#############################################
# Rule for linking CXX executable.

rule CXX_EXECUTABLE_LINKER__CuraEngine_Release
  command = C:\WINDOWS\system32\cmd.exe /C "$PRE_LINK && "C:\Program Files\CMake\bin\cmake.exe" -E vs_link_exe --msvc-ver=1944 --intdir=$OBJECT_DIR --rc=C:\PROGRA~2\WI3CF2~1\10\bin\100261~1.0\x64\rc.exe --mt=C:\PROGRA~2\WI3CF2~1\10\bin\100261~1.0\x64\mt.exe --manifests $MANIFESTS -- C:\PROGRA~1\MICROS~1\2022\COMMUN~1\VC\Tools\MSVC\1444~1.352\bin\Hostx64\x64\link.exe /nologo @$RSP_FILE  /out:$TARGET_FILE /implib:$TARGET_IMPLIB /pdb:$TARGET_PDB /version:0.0 $LINK_FLAGS && $POST_BUILD"
  description = Linking CXX executable $TARGET_FILE
  rspfile = $RSP_FILE
  rspfile_content = $in_newline $LINK_PATH $LINK_LIBRARIES
  restat = $RESTAT


#############################################
# Rule for running custom commands.

rule CUSTOM_COMMAND
  command = $COMMAND
  description = $DESC


#############################################
# Rule for re-running cmake.

rule RERUN_CMAKE
  command = "C:\Program Files\CMake\bin\cmake.exe" --regenerate-during-build -SC:\Users\<USER>\vscode\Cura-Dev\CuraEngine -BC:\Users\<USER>\vscode\Cura-Dev\CuraEngine\build\Release
  description = Re-running CMake...
  generator = 1


#############################################
# Rule for cleaning all built files.

rule CLEAN
  command = C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Scripts\ninja.exe $FILE_ARG -t clean $TARGETS
  description = Cleaning all built files...


#############################################
# Rule for printing all primary targets available.

rule HELP
  command = C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Scripts\ninja.exe -t targets
  description = All primary targets available:


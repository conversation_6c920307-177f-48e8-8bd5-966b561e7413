# <PERSON> automatically generated toolchain file
# DO NOT EDIT MANUALLY, it will be overwritten

# Avoid including toolchain file several times (bad if appending to variables like
#   CMAKE_CXX_FLAGS. See https://github.com/android/ndk/issues/323
include_guard()
message(STATUS "Using Conan toolchain: ${CMAKE_CURRENT_LIST_FILE}")
if(${CMAKE_VERSION} VERSION_LESS "3.15")
    message(FATAL_ERROR "The 'CMakeToolchain' generator only works with CMake >= 3.15")
endif()

########## 'user_toolchain' block #############
# Include one or more CMake user toolchain from tools.cmake.cmaketoolchain:user_toolchain



########## 'generic_system' block #############
# Definition of system, platform and toolset





########## 'compilers' block #############



########## 'libcxx' block #############
# Definition of libcxx from 'compiler.libcxx' setting, defining the
# right CXX_FLAGS for that libcxx



########## 'vs_runtime' block #############
# Definition of VS runtime CMAKE_MSVC_RUNTIME_LIBRARY, from settings build_type,
# compiler.runtime, compiler.runtime_type

cmake_policy(GET CMP0091 POLICY_CMP0091)
if(NOT "${POLICY_CMP0091}" STREQUAL NEW)
    message(FATAL_ERROR "The CMake policy CMP0091 must be NEW, but is '${POLICY_CMP0091}'")
endif()
message(STATUS "Conan toolchain: Setting CMAKE_MSVC_RUNTIME_LIBRARY=$<$<CONFIG:Release>:MultiThreadedDLL>")
set(CMAKE_MSVC_RUNTIME_LIBRARY "$<$<CONFIG:Release>:MultiThreadedDLL>")


########## 'cppstd' block #############
# Define the C++ and C standards from 'compiler.cppstd' and 'compiler.cstd'

function(conan_modify_std_watch variable access value current_list_file stack)
    set(conan_watched_std_variable 20)
    if (${variable} STREQUAL "CMAKE_C_STANDARD")
        set(conan_watched_std_variable )
    endif()
    if (${access} STREQUAL "MODIFIED_ACCESS" AND NOT ${value} STREQUAL ${conan_watched_std_variable})
        message(STATUS "Warning: Standard ${variable} value defined in conan_toolchain.cmake to ${conan_watched_std_variable} has been modified to ${value} by ${current_list_file}")
    endif()
    unset(conan_watched_std_variable)
endfunction()

message(STATUS "Conan toolchain: C++ Standard 20 with extensions OFF")
set(CMAKE_CXX_STANDARD 20)
set(CMAKE_CXX_EXTENSIONS OFF)
set(CMAKE_CXX_STANDARD_REQUIRED ON)
variable_watch(CMAKE_CXX_STANDARD conan_modify_std_watch)


########## 'extra_flags' block #############
# Include extra C++, C and linker flags from configuration tools.build:<type>flags
# and from CMakeToolchain.extra_<type>_flags

# Conan conf flags start: 
# Conan conf flags end


########## 'cmake_flags_init' block #############
# Define CMAKE_<XXX>_FLAGS from CONAN_<XXX>_FLAGS

foreach(config IN LISTS CMAKE_CONFIGURATION_TYPES)
    string(TOUPPER ${config} config)
    if(DEFINED CONAN_CXX_FLAGS_${config})
      string(APPEND CMAKE_CXX_FLAGS_${config}_INIT " ${CONAN_CXX_FLAGS_${config}}")
    endif()
    if(DEFINED CONAN_C_FLAGS_${config})
      string(APPEND CMAKE_C_FLAGS_${config}_INIT " ${CONAN_C_FLAGS_${config}}")
    endif()
    if(DEFINED CONAN_SHARED_LINKER_FLAGS_${config})
      string(APPEND CMAKE_SHARED_LINKER_FLAGS_${config}_INIT " ${CONAN_SHARED_LINKER_FLAGS_${config}}")
    endif()
    if(DEFINED CONAN_EXE_LINKER_FLAGS_${config})
      string(APPEND CMAKE_EXE_LINKER_FLAGS_${config}_INIT " ${CONAN_EXE_LINKER_FLAGS_${config}}")
    endif()
endforeach()

if(DEFINED CONAN_CXX_FLAGS)
  string(APPEND CMAKE_CXX_FLAGS_INIT " ${CONAN_CXX_FLAGS}")
endif()
if(DEFINED CONAN_C_FLAGS)
  string(APPEND CMAKE_C_FLAGS_INIT " ${CONAN_C_FLAGS}")
endif()
if(DEFINED CONAN_SHARED_LINKER_FLAGS)
  string(APPEND CMAKE_SHARED_LINKER_FLAGS_INIT " ${CONAN_SHARED_LINKER_FLAGS}")
endif()
if(DEFINED CONAN_EXE_LINKER_FLAGS)
  string(APPEND CMAKE_EXE_LINKER_FLAGS_INIT " ${CONAN_EXE_LINKER_FLAGS}")
endif()


########## 'extra_variables' block #############
# Definition of extra CMake variables from tools.cmake.cmaketoolchain:extra_variables



########## 'try_compile' block #############
# Blocks after this one will not be added when running CMake try/checks

get_property( _CMAKE_IN_TRY_COMPILE GLOBAL PROPERTY IN_TRY_COMPILE )
if(_CMAKE_IN_TRY_COMPILE)
    message(STATUS "Running toolchain IN_TRY_COMPILE")
    return()
endif()


########## 'find_paths' block #############
# Define paths to find packages, programs, libraries, etc.

set(CMAKE_FIND_PACKAGE_PREFER_CONFIG ON)

# Definition of CMAKE_MODULE_PATH
list(PREPEND CMAKE_MODULE_PATH "C:/Users/<USER>/.conan2/p/proto47a676cb9257b/p/lib/cmake/protobuf" "C:/Users/<USER>/.conan2/p/opens13d3a0b103336/p/lib/cmake")
# the generators folder (where conan generates files, like this toolchain)
list(PREPEND CMAKE_MODULE_PATH ${CMAKE_CURRENT_LIST_DIR})

# Definition of CMAKE_PREFIX_PATH, CMAKE_XXXXX_PATH
# The explicitly defined "builddirs" of "host" context dependencies must be in PREFIX_PATH
list(PREPEND CMAKE_PREFIX_PATH "C:/Users/<USER>/.conan2/p/proto47a676cb9257b/p/lib/cmake/protobuf" "C:/Users/<USER>/.conan2/p/opens13d3a0b103336/p/lib/cmake")
# The Conan local "generators" folder, where this toolchain is saved.
list(PREPEND CMAKE_PREFIX_PATH ${CMAKE_CURRENT_LIST_DIR} )
list(PREPEND CMAKE_LIBRARY_PATH "C:/Users/<USER>/.conan2/p/b/arcusee7666f716199/p/lib" "C:/Users/<USER>/.conan2/p/b/curae1b419103f28e5/p/lib" "C:/Users/<USER>/.conan2/p/grpc8b6c73b4e5756/p/lib" "C:/Users/<USER>/.conan2/p/absei6a15444d60d47/p/lib" "C:/Users/<USER>/.conan2/p/proto47a676cb9257b/p/lib" "C:/Users/<USER>/.conan2/p/c-are581008031a482/p/lib" "C:/Users/<USER>/.conan2/p/opens13d3a0b103336/p/lib" "C:/Users/<USER>/.conan2/p/re24eb2cefbcf664/p/lib" "C:/Users/<USER>/.conan2/p/b/clippf8ddcd4a5d961/p/lib" "C:/Users/<USER>/.conan2/p/spdlo2d4b540ab0f22/p/lib" "C:/Users/<USER>/.conan2/p/fmtdb20aad6e1bd4/p/lib" "C:/Users/<USER>/.conan2/p/range0301bf3d76d5d/p/lib" "C:/Users/<USER>/.conan2/p/zlibcfb9789dc2a53/p/lib")
list(PREPEND CMAKE_INCLUDE_PATH "C:/Users/<USER>/.conan2/p/scrip4cfd014ad4660/p/include" "C:/Users/<USER>/.conan2/p/b/arcusee7666f716199/p/include" "C:/Users/<USER>/.conan2/p/nearg388d58da7a54c/p/include" "C:/Users/<USER>/.conan2/p/b/curae1b419103f28e5/p/include" "C:/Users/<USER>/.conan2/p/asio-c31dc2a380bf0/p/include" "C:/Users/<USER>/.conan2/p/grpc8b6c73b4e5756/p/include" "C:/Users/<USER>/.conan2/p/absei6a15444d60d47/p/include" "C:/Users/<USER>/.conan2/p/proto47a676cb9257b/p/include" "C:/Users/<USER>/.conan2/p/c-are581008031a482/p/include" "C:/Users/<USER>/.conan2/p/opens13d3a0b103336/p/include" "C:/Users/<USER>/.conan2/p/re24eb2cefbcf664/p/include" "C:/Users/<USER>/.conan2/p/b/clippf8ddcd4a5d961/p/include" "C:/Users/<USER>/.conan2/p/boost49354e0e38e86/p/include" "C:/Users/<USER>/.conan2/p/rapidf7a3355ba53c4/p/include" "C:/Users/<USER>/.conan2/p/stb6342cecb318f5/p/include" "C:/Users/<USER>/.conan2/p/spdlo2d4b540ab0f22/p/include" "C:/Users/<USER>/.conan2/p/fmtdb20aad6e1bd4/p/include" "C:/Users/<USER>/.conan2/p/range0301bf3d76d5d/p/include" "C:/Users/<USER>/.conan2/p/zlibcfb9789dc2a53/p/include" "C:/Users/<USER>/.conan2/p/mapbo34ee9aeb574f9/p/include" "C:/Users/<USER>/.conan2/p/mapbofe72fb50cf7c4/p/include" "C:/Users/<USER>/.conan2/p/mapbo1b62b48748b82/p/include")
set(CONAN_RUNTIME_LIB_DIRS "C:/Users/<USER>/.conan2/p/b/arcusee7666f716199/p/bin" "C:/Users/<USER>/.conan2/p/b/curae1b419103f28e5/p/bin" "C:/Users/<USER>/.conan2/p/grpc8b6c73b4e5756/p/bin" "C:/Users/<USER>/.conan2/p/absei6a15444d60d47/p/bin" "C:/Users/<USER>/.conan2/p/proto47a676cb9257b/p/bin" "C:/Users/<USER>/.conan2/p/c-are581008031a482/p/bin" "C:/Users/<USER>/.conan2/p/opens13d3a0b103336/p/bin" "C:/Users/<USER>/.conan2/p/re24eb2cefbcf664/p/bin" "C:/Users/<USER>/.conan2/p/b/clippf8ddcd4a5d961/p/bin" "C:/Users/<USER>/.conan2/p/boost49354e0e38e86/p/bin" "C:/Users/<USER>/.conan2/p/spdlo2d4b540ab0f22/p/bin" "C:/Users/<USER>/.conan2/p/fmtdb20aad6e1bd4/p/bin" "C:/Users/<USER>/.conan2/p/range0301bf3d76d5d/p/bin" "C:/Users/<USER>/.conan2/p/zlibcfb9789dc2a53/p/bin" )



########## 'pkg_config' block #############
# Define pkg-config from 'tools.gnu:pkg_config' executable and paths

if (DEFINED ENV{PKG_CONFIG_PATH})
set(ENV{PKG_CONFIG_PATH} "${CMAKE_CURRENT_LIST_DIR};$ENV{PKG_CONFIG_PATH}")
else()
set(ENV{PKG_CONFIG_PATH} "${CMAKE_CURRENT_LIST_DIR};")
endif()


########## 'rpath' block #############
# Defining CMAKE_SKIP_RPATH



########## 'output_dirs' block #############
# Definition of CMAKE_INSTALL_XXX folders

set(CMAKE_INSTALL_BINDIR "bin")
set(CMAKE_INSTALL_SBINDIR "bin")
set(CMAKE_INSTALL_LIBEXECDIR "bin")
set(CMAKE_INSTALL_LIBDIR "lib")
set(CMAKE_INSTALL_INCLUDEDIR "include")
set(CMAKE_INSTALL_OLDINCLUDEDIR "include")


########## 'variables' block #############
# Definition of CMake variables from CMakeToolchain.variables values

# Variables
set(CURA_ENGINE_VERSION "5.11.0-alpha.0" CACHE STRING "Variable CURA_ENGINE_VERSION conan-toolchain defined")
set(CURA_ENGINE_HASH "unknown" CACHE STRING "Variable CURA_ENGINE_HASH conan-toolchain defined")
set(ENABLE_ARCUS "True" CACHE STRING "Variable ENABLE_ARCUS conan-toolchain defined")
set(ENABLE_TESTING OFF CACHE BOOL "Variable ENABLE_TESTING conan-toolchain defined")
set(ENABLE_BENCHMARKS "False" CACHE STRING "Variable ENABLE_BENCHMARKS conan-toolchain defined")
set(EXTENSIVE_WARNINGS "False" CACHE STRING "Variable EXTENSIVE_WARNINGS conan-toolchain defined")
set(OLDER_APPLE_CLANG OFF CACHE BOOL "Variable OLDER_APPLE_CLANG conan-toolchain defined")
set(ENABLE_THREADING ON CACHE BOOL "Variable ENABLE_THREADING conan-toolchain defined")
set(ENABLE_PLUGINS ON CACHE BOOL "Variable ENABLE_PLUGINS conan-toolchain defined")
set(ENABLE_REMOTE_PLUGINS "False" CACHE STRING "Variable ENABLE_REMOTE_PLUGINS conan-toolchain defined")
set(ENABLE_SENTRY "False" CACHE STRING "Variable ENABLE_SENTRY conan-toolchain defined")
set(SENTRY_URL "" CACHE STRING "Variable SENTRY_URL conan-toolchain defined")
set(SENTRY_ENVIRONMENT "development" CACHE STRING "Variable SENTRY_ENVIRONMENT conan-toolchain defined")
# Variables  per configuration



########## 'preprocessor' block #############
# Preprocessor definitions from CMakeToolchain.preprocessor_definitions values

# Preprocessor definitions per configuration



if(CMAKE_POLICY_DEFAULT_CMP0091)  # Avoid unused and not-initialized warnings
endif()

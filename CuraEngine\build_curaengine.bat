@echo off
chcp 65001 >nul
echo ========================================
echo CuraEngine 完整构建和部署脚本
echo ========================================

REM 设置错误处理
setlocal enabledelayedexpansion

REM 解析命令行参数
set "QUICK_BUILD=false"
set "CLEAN_ONLY=false"
set "NO_DEPLOY=false"
set "NO_GIT=false"
set "HELP=false"

:parse_args
if "%~1"=="" goto :args_done
if /i "%~1"=="--quick" set "QUICK_BUILD=true"
if /i "%~1"=="-q" set "QUICK_BUILD=true"
if /i "%~1"=="--clean" set "CLEAN_ONLY=true"
if /i "%~1"=="-c" set "CLEAN_ONLY=true"
if /i "%~1"=="--no-deploy" set "NO_DEPLOY=true"
if /i "%~1"=="-nd" set "NO_DEPLOY=true"
if /i "%~1"=="--no-git" set "NO_GIT=true"
if /i "%~1"=="-ng" set "NO_GIT=true"
if /i "%~1"=="--help" set "HELP=true"
if /i "%~1"=="-h" set "HELP=true"
shift
goto :parse_args

:args_done

REM 显示帮助信息
if "%HELP%"=="true" (
    echo.
    echo 用法: build_curaengine.bat [选项]
    echo.
    echo 选项:
    echo   --quick, -q      快速构建（跳过依赖项安装，仅编译源码）
    echo   --clean, -c      仅清理构建目录，不进行构建
    echo   --no-deploy, -nd 不部署到Cura目录
    echo   --no-git, -ng    不提交到Git仓库
    echo   --help, -h       显示此帮助信息
    echo.
    echo 示例:
    echo   build_curaengine.bat           # 完整构建和部署
    echo   build_curaengine.bat --quick   # 快速构建（推荐用于代码修改后）
    echo   build_curaengine.bat --clean   # 仅清理构建目录
    echo   build_curaengine.bat -q -nd    # 快速构建但不部署
    echo.
    pause
    exit /b 0
)

REM 检查是否在正确的目录
if not exist "conanfile.py" (
    echo 错误: 请在CuraEngine根目录运行此脚本
    pause
    exit /b 1
)

REM 仅清理模式
if "%CLEAN_ONLY%"=="true" (
    echo 步骤: 清理构建目录...
    if exist "build" (
        rmdir /s /q "build"
        echo 成功: 构建目录已清理
    ) else (
        echo 信息: 构建目录不存在，无需清理
    )
    pause
    exit /b 0
)

echo 步骤 1: 清理之前的构建...
if exist "build" rmdir /s /q "build"

REM 快速构建模式跳过依赖项安装
if "%QUICK_BUILD%"=="true" (
    echo 步骤 2: 跳过依赖项安装（快速构建模式）...
    echo 信息: 使用现有的Conan依赖项
    if not exist "build" (
        echo 警告: 构建目录不存在，需要先运行完整构建
        echo 提示: 运行 build_curaengine.bat （不带 --quick 参数）
        pause
        exit /b 1
    )
) else (
    echo 步骤 2: 安装Conan依赖项（正确的Boost配置）...
    conan install . --build=missing -o "boost/*:header_only=False" -o "boost/*:without_exception=False"

    if !errorlevel! neq 0 (
        echo 错误: Conan依赖项安装失败
        pause
        exit /b 1
    )
)

echo 步骤 3: 设置Visual Studio环境并配置CMake...
call "C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\Build\vcvars64.bat"

REM 快速构建模式可能不需要重新配置CMake
if "%QUICK_BUILD%"=="true" (
    if exist "build\Release\build.ninja" (
        echo 信息: 跳过CMake配置（使用现有配置）
        goto :build_step
    ) else (
        echo 信息: 需要配置CMake（构建文件不存在）
    )
)

cmake --preset conan-release

if !errorlevel! neq 0 (
    echo 错误: CMake配置失败
    pause
    exit /b 1
)

:build_step
echo 步骤 4: 构建CuraEngine...
cmake --build build\Release --config Release

if !errorlevel! neq 0 (
    echo 错误: 构建失败
    pause
    exit /b 1
)

echo 步骤 5: 验证构建结果...
if not exist "build\Release\CuraEngine.exe" (
    echo 错误: CuraEngine.exe未生成
    pause
    exit /b 1
)

echo 步骤 6: 测试CuraEngine...
build\Release\CuraEngine.exe --help >nul 2>&1
if !errorlevel! neq 0 (
    echo 警告: CuraEngine.exe可能无法正常工作
) else (
    echo 成功: CuraEngine.exe工作正常
)

REM 部署步骤（可选）
if "%NO_DEPLOY%"=="true" (
    echo 信息: 跳过部署到Cura目录（--no-deploy选项）
    goto :git_step
)

echo 步骤 7: 复制CuraEngine.exe到Cura目录...
set CURA_DIR=C:\Users\<USER>\vscode\Cura-Dev\Cura
if not exist "%CURA_DIR%" (
    echo 错误: 找不到Cura目录: %CURA_DIR%
    pause
    exit /b 1
)

cmd /c copy "build\Release\CuraEngine.exe" "%CURA_DIR%\CuraEngine.exe" /Y
if !errorlevel! neq 0 (
    echo 错误: 复制CuraEngine.exe到Cura目录失败
    pause
    exit /b 1
)
echo 成功: CuraEngine.exe已复制到 %CURA_DIR%

:git_step
REM Git提交步骤（可选）
if "%NO_GIT%"=="true" (
    echo 信息: 跳过Git提交（--no-git选项）
    goto :completion
)

if "%NO_DEPLOY%"=="true" (
    echo 信息: 跳过Git提交（未部署文件）
    goto :completion
)

echo 步骤 8: 推送到GitHub仓库...
cd /d "%CURA_DIR%"
git add CuraEngine.exe
git commit -m "更新CuraEngine.exe - 构建于 %date% %time%"
git push origin main

if !errorlevel! neq 0 (
    echo 警告: Git推送失败。请检查GitHub凭据和网络连接。
    echo 您可能需要手动推送: cd %CURA_DIR% ^&^& git push origin main
) else (
    echo 成功: 更改已推送到GitHub仓库
)

:completion
echo ========================================
echo 所有操作已成功完成！
echo ========================================
echo 构建位置: %~dp0build\Release\CuraEngine.exe
if "%NO_DEPLOY%"=="false" (
    echo 部署位置: %CURA_DIR%\CuraEngine.exe
)
echo GitHub仓库: https://github.com/wsd07/Cura
echo ========================================

REM 显示构建统计信息
echo.
echo 构建统计:
if "%QUICK_BUILD%"=="true" (
    echo - 构建模式: 快速构建（仅编译源码）
) else (
    echo - 构建模式: 完整构建（包含依赖项）
)
if "%NO_DEPLOY%"=="true" (
    echo - 部署状态: 跳过
) else (
    echo - 部署状态: 已完成
)
if "%NO_GIT%"=="true" (
    echo - Git提交: 跳过
) else (
    echo - Git提交: 已完成
)

pause

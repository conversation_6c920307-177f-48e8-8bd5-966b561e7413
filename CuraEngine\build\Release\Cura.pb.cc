// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: Cura.proto

#include "Cura.pb.h"

#include <algorithm>

#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/wire_format_lite.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>

PROTOBUF_PRAGMA_INIT_SEG

namespace _pb = ::PROTOBUF_NAMESPACE_ID;
namespace _pbi = _pb::internal;

namespace cura {
namespace proto {
PROTOBUF_CONSTEXPR ObjectList::ObjectList(
    ::_pbi::ConstantInitialized): _impl_{
    /*decltype(_impl_.objects_)*/{}
  , /*decltype(_impl_.settings_)*/{}
  , /*decltype(_impl_._cached_size_)*/{}} {}
struct ObjectListDefaultTypeInternal {
  PROTOBUF_CONSTEXPR ObjectListDefaultTypeInternal()
      : _instance(::_pbi::ConstantInitialized{}) {}
  ~ObjectListDefaultTypeInternal() {}
  union {
    ObjectList _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT PROTOBUF_ATTRIBUTE_INIT_PRIORITY1 ObjectListDefaultTypeInternal _ObjectList_default_instance_;
PROTOBUF_CONSTEXPR EnginePlugin::EnginePlugin(
    ::_pbi::ConstantInitialized): _impl_{
    /*decltype(_impl_.address_)*/{&::_pbi::fixed_address_empty_string, ::_pbi::ConstantInitialized{}}
  , /*decltype(_impl_.plugin_name_)*/{&::_pbi::fixed_address_empty_string, ::_pbi::ConstantInitialized{}}
  , /*decltype(_impl_.plugin_version_)*/{&::_pbi::fixed_address_empty_string, ::_pbi::ConstantInitialized{}}
  , /*decltype(_impl_.id_)*/0
  , /*decltype(_impl_.port_)*/0u
  , /*decltype(_impl_._cached_size_)*/{}} {}
struct EnginePluginDefaultTypeInternal {
  PROTOBUF_CONSTEXPR EnginePluginDefaultTypeInternal()
      : _instance(::_pbi::ConstantInitialized{}) {}
  ~EnginePluginDefaultTypeInternal() {}
  union {
    EnginePlugin _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT PROTOBUF_ATTRIBUTE_INIT_PRIORITY1 EnginePluginDefaultTypeInternal _EnginePlugin_default_instance_;
PROTOBUF_CONSTEXPR Slice::Slice(
    ::_pbi::ConstantInitialized): _impl_{
    /*decltype(_impl_._has_bits_)*/{}
  , /*decltype(_impl_._cached_size_)*/{}
  , /*decltype(_impl_.object_lists_)*/{}
  , /*decltype(_impl_.extruders_)*/{}
  , /*decltype(_impl_.limit_to_extruder_)*/{}
  , /*decltype(_impl_.engine_plugins_)*/{}
  , /*decltype(_impl_.sentry_id_)*/{&::_pbi::fixed_address_empty_string, ::_pbi::ConstantInitialized{}}
  , /*decltype(_impl_.cura_version_)*/{&::_pbi::fixed_address_empty_string, ::_pbi::ConstantInitialized{}}
  , /*decltype(_impl_.project_name_)*/{&::_pbi::fixed_address_empty_string, ::_pbi::ConstantInitialized{}}
  , /*decltype(_impl_.user_name_)*/{&::_pbi::fixed_address_empty_string, ::_pbi::ConstantInitialized{}}
  , /*decltype(_impl_.global_settings_)*/nullptr} {}
struct SliceDefaultTypeInternal {
  PROTOBUF_CONSTEXPR SliceDefaultTypeInternal()
      : _instance(::_pbi::ConstantInitialized{}) {}
  ~SliceDefaultTypeInternal() {}
  union {
    Slice _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT PROTOBUF_ATTRIBUTE_INIT_PRIORITY1 SliceDefaultTypeInternal _Slice_default_instance_;
PROTOBUF_CONSTEXPR Extruder::Extruder(
    ::_pbi::ConstantInitialized): _impl_{
    /*decltype(_impl_.settings_)*/nullptr
  , /*decltype(_impl_.id_)*/0
  , /*decltype(_impl_._cached_size_)*/{}} {}
struct ExtruderDefaultTypeInternal {
  PROTOBUF_CONSTEXPR ExtruderDefaultTypeInternal()
      : _instance(::_pbi::ConstantInitialized{}) {}
  ~ExtruderDefaultTypeInternal() {}
  union {
    Extruder _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT PROTOBUF_ATTRIBUTE_INIT_PRIORITY1 ExtruderDefaultTypeInternal _Extruder_default_instance_;
PROTOBUF_CONSTEXPR Object::Object(
    ::_pbi::ConstantInitialized): _impl_{
    /*decltype(_impl_.settings_)*/{}
  , /*decltype(_impl_.vertices_)*/{&::_pbi::fixed_address_empty_string, ::_pbi::ConstantInitialized{}}
  , /*decltype(_impl_.normals_)*/{&::_pbi::fixed_address_empty_string, ::_pbi::ConstantInitialized{}}
  , /*decltype(_impl_.indices_)*/{&::_pbi::fixed_address_empty_string, ::_pbi::ConstantInitialized{}}
  , /*decltype(_impl_.name_)*/{&::_pbi::fixed_address_empty_string, ::_pbi::ConstantInitialized{}}
  , /*decltype(_impl_.id_)*/int64_t{0}
  , /*decltype(_impl_._cached_size_)*/{}} {}
struct ObjectDefaultTypeInternal {
  PROTOBUF_CONSTEXPR ObjectDefaultTypeInternal()
      : _instance(::_pbi::ConstantInitialized{}) {}
  ~ObjectDefaultTypeInternal() {}
  union {
    Object _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT PROTOBUF_ATTRIBUTE_INIT_PRIORITY1 ObjectDefaultTypeInternal _Object_default_instance_;
PROTOBUF_CONSTEXPR Progress::Progress(
    ::_pbi::ConstantInitialized): _impl_{
    /*decltype(_impl_.amount_)*/0
  , /*decltype(_impl_._cached_size_)*/{}} {}
struct ProgressDefaultTypeInternal {
  PROTOBUF_CONSTEXPR ProgressDefaultTypeInternal()
      : _instance(::_pbi::ConstantInitialized{}) {}
  ~ProgressDefaultTypeInternal() {}
  union {
    Progress _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT PROTOBUF_ATTRIBUTE_INIT_PRIORITY1 ProgressDefaultTypeInternal _Progress_default_instance_;
PROTOBUF_CONSTEXPR Layer::Layer(
    ::_pbi::ConstantInitialized): _impl_{
    /*decltype(_impl_.polygons_)*/{}
  , /*decltype(_impl_.id_)*/0
  , /*decltype(_impl_.height_)*/0
  , /*decltype(_impl_.thickness_)*/0
  , /*decltype(_impl_._cached_size_)*/{}} {}
struct LayerDefaultTypeInternal {
  PROTOBUF_CONSTEXPR LayerDefaultTypeInternal()
      : _instance(::_pbi::ConstantInitialized{}) {}
  ~LayerDefaultTypeInternal() {}
  union {
    Layer _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT PROTOBUF_ATTRIBUTE_INIT_PRIORITY1 LayerDefaultTypeInternal _Layer_default_instance_;
PROTOBUF_CONSTEXPR Polygon::Polygon(
    ::_pbi::ConstantInitialized): _impl_{
    /*decltype(_impl_.points_)*/{&::_pbi::fixed_address_empty_string, ::_pbi::ConstantInitialized{}}
  , /*decltype(_impl_.type_)*/0
  , /*decltype(_impl_.line_width_)*/0
  , /*decltype(_impl_.line_thickness_)*/0
  , /*decltype(_impl_.line_feedrate_)*/0
  , /*decltype(_impl_._cached_size_)*/{}} {}
struct PolygonDefaultTypeInternal {
  PROTOBUF_CONSTEXPR PolygonDefaultTypeInternal()
      : _instance(::_pbi::ConstantInitialized{}) {}
  ~PolygonDefaultTypeInternal() {}
  union {
    Polygon _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT PROTOBUF_ATTRIBUTE_INIT_PRIORITY1 PolygonDefaultTypeInternal _Polygon_default_instance_;
PROTOBUF_CONSTEXPR LayerOptimized::LayerOptimized(
    ::_pbi::ConstantInitialized): _impl_{
    /*decltype(_impl_.path_segment_)*/{}
  , /*decltype(_impl_.id_)*/0
  , /*decltype(_impl_.height_)*/0
  , /*decltype(_impl_.thickness_)*/0
  , /*decltype(_impl_._cached_size_)*/{}} {}
struct LayerOptimizedDefaultTypeInternal {
  PROTOBUF_CONSTEXPR LayerOptimizedDefaultTypeInternal()
      : _instance(::_pbi::ConstantInitialized{}) {}
  ~LayerOptimizedDefaultTypeInternal() {}
  union {
    LayerOptimized _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT PROTOBUF_ATTRIBUTE_INIT_PRIORITY1 LayerOptimizedDefaultTypeInternal _LayerOptimized_default_instance_;
PROTOBUF_CONSTEXPR PathSegment::PathSegment(
    ::_pbi::ConstantInitialized): _impl_{
    /*decltype(_impl_.points_)*/{&::_pbi::fixed_address_empty_string, ::_pbi::ConstantInitialized{}}
  , /*decltype(_impl_.line_type_)*/{&::_pbi::fixed_address_empty_string, ::_pbi::ConstantInitialized{}}
  , /*decltype(_impl_.line_width_)*/{&::_pbi::fixed_address_empty_string, ::_pbi::ConstantInitialized{}}
  , /*decltype(_impl_.line_thickness_)*/{&::_pbi::fixed_address_empty_string, ::_pbi::ConstantInitialized{}}
  , /*decltype(_impl_.line_feedrate_)*/{&::_pbi::fixed_address_empty_string, ::_pbi::ConstantInitialized{}}
  , /*decltype(_impl_.extruder_)*/0
  , /*decltype(_impl_.point_type_)*/0
  , /*decltype(_impl_._cached_size_)*/{}} {}
struct PathSegmentDefaultTypeInternal {
  PROTOBUF_CONSTEXPR PathSegmentDefaultTypeInternal()
      : _instance(::_pbi::ConstantInitialized{}) {}
  ~PathSegmentDefaultTypeInternal() {}
  union {
    PathSegment _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT PROTOBUF_ATTRIBUTE_INIT_PRIORITY1 PathSegmentDefaultTypeInternal _PathSegment_default_instance_;
PROTOBUF_CONSTEXPR GCodeLayer::GCodeLayer(
    ::_pbi::ConstantInitialized): _impl_{
    /*decltype(_impl_.data_)*/{&::_pbi::fixed_address_empty_string, ::_pbi::ConstantInitialized{}}
  , /*decltype(_impl_._cached_size_)*/{}} {}
struct GCodeLayerDefaultTypeInternal {
  PROTOBUF_CONSTEXPR GCodeLayerDefaultTypeInternal()
      : _instance(::_pbi::ConstantInitialized{}) {}
  ~GCodeLayerDefaultTypeInternal() {}
  union {
    GCodeLayer _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT PROTOBUF_ATTRIBUTE_INIT_PRIORITY1 GCodeLayerDefaultTypeInternal _GCodeLayer_default_instance_;
PROTOBUF_CONSTEXPR PrintTimeMaterialEstimates::PrintTimeMaterialEstimates(
    ::_pbi::ConstantInitialized): _impl_{
    /*decltype(_impl_.materialestimates_)*/{}
  , /*decltype(_impl_.time_none_)*/0
  , /*decltype(_impl_.time_inset_0_)*/0
  , /*decltype(_impl_.time_inset_x_)*/0
  , /*decltype(_impl_.time_skin_)*/0
  , /*decltype(_impl_.time_support_)*/0
  , /*decltype(_impl_.time_skirt_)*/0
  , /*decltype(_impl_.time_infill_)*/0
  , /*decltype(_impl_.time_support_infill_)*/0
  , /*decltype(_impl_.time_travel_)*/0
  , /*decltype(_impl_.time_retract_)*/0
  , /*decltype(_impl_.time_support_interface_)*/0
  , /*decltype(_impl_.time_prime_tower_)*/0
  , /*decltype(_impl_._cached_size_)*/{}} {}
struct PrintTimeMaterialEstimatesDefaultTypeInternal {
  PROTOBUF_CONSTEXPR PrintTimeMaterialEstimatesDefaultTypeInternal()
      : _instance(::_pbi::ConstantInitialized{}) {}
  ~PrintTimeMaterialEstimatesDefaultTypeInternal() {}
  union {
    PrintTimeMaterialEstimates _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT PROTOBUF_ATTRIBUTE_INIT_PRIORITY1 PrintTimeMaterialEstimatesDefaultTypeInternal _PrintTimeMaterialEstimates_default_instance_;
PROTOBUF_CONSTEXPR MaterialEstimates::MaterialEstimates(
    ::_pbi::ConstantInitialized): _impl_{
    /*decltype(_impl_.id_)*/int64_t{0}
  , /*decltype(_impl_.material_amount_)*/0
  , /*decltype(_impl_._cached_size_)*/{}} {}
struct MaterialEstimatesDefaultTypeInternal {
  PROTOBUF_CONSTEXPR MaterialEstimatesDefaultTypeInternal()
      : _instance(::_pbi::ConstantInitialized{}) {}
  ~MaterialEstimatesDefaultTypeInternal() {}
  union {
    MaterialEstimates _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT PROTOBUF_ATTRIBUTE_INIT_PRIORITY1 MaterialEstimatesDefaultTypeInternal _MaterialEstimates_default_instance_;
PROTOBUF_CONSTEXPR SettingList::SettingList(
    ::_pbi::ConstantInitialized): _impl_{
    /*decltype(_impl_.settings_)*/{}
  , /*decltype(_impl_._cached_size_)*/{}} {}
struct SettingListDefaultTypeInternal {
  PROTOBUF_CONSTEXPR SettingListDefaultTypeInternal()
      : _instance(::_pbi::ConstantInitialized{}) {}
  ~SettingListDefaultTypeInternal() {}
  union {
    SettingList _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT PROTOBUF_ATTRIBUTE_INIT_PRIORITY1 SettingListDefaultTypeInternal _SettingList_default_instance_;
PROTOBUF_CONSTEXPR Setting::Setting(
    ::_pbi::ConstantInitialized): _impl_{
    /*decltype(_impl_.name_)*/{&::_pbi::fixed_address_empty_string, ::_pbi::ConstantInitialized{}}
  , /*decltype(_impl_.value_)*/{&::_pbi::fixed_address_empty_string, ::_pbi::ConstantInitialized{}}
  , /*decltype(_impl_._cached_size_)*/{}} {}
struct SettingDefaultTypeInternal {
  PROTOBUF_CONSTEXPR SettingDefaultTypeInternal()
      : _instance(::_pbi::ConstantInitialized{}) {}
  ~SettingDefaultTypeInternal() {}
  union {
    Setting _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT PROTOBUF_ATTRIBUTE_INIT_PRIORITY1 SettingDefaultTypeInternal _Setting_default_instance_;
PROTOBUF_CONSTEXPR SettingExtruder::SettingExtruder(
    ::_pbi::ConstantInitialized): _impl_{
    /*decltype(_impl_.name_)*/{&::_pbi::fixed_address_empty_string, ::_pbi::ConstantInitialized{}}
  , /*decltype(_impl_.extruder_)*/0
  , /*decltype(_impl_._cached_size_)*/{}} {}
struct SettingExtruderDefaultTypeInternal {
  PROTOBUF_CONSTEXPR SettingExtruderDefaultTypeInternal()
      : _instance(::_pbi::ConstantInitialized{}) {}
  ~SettingExtruderDefaultTypeInternal() {}
  union {
    SettingExtruder _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT PROTOBUF_ATTRIBUTE_INIT_PRIORITY1 SettingExtruderDefaultTypeInternal _SettingExtruder_default_instance_;
PROTOBUF_CONSTEXPR GCodePrefix::GCodePrefix(
    ::_pbi::ConstantInitialized): _impl_{
    /*decltype(_impl_.data_)*/{&::_pbi::fixed_address_empty_string, ::_pbi::ConstantInitialized{}}
  , /*decltype(_impl_._cached_size_)*/{}} {}
struct GCodePrefixDefaultTypeInternal {
  PROTOBUF_CONSTEXPR GCodePrefixDefaultTypeInternal()
      : _instance(::_pbi::ConstantInitialized{}) {}
  ~GCodePrefixDefaultTypeInternal() {}
  union {
    GCodePrefix _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT PROTOBUF_ATTRIBUTE_INIT_PRIORITY1 GCodePrefixDefaultTypeInternal _GCodePrefix_default_instance_;
PROTOBUF_CONSTEXPR SliceUUID::SliceUUID(
    ::_pbi::ConstantInitialized): _impl_{
    /*decltype(_impl_.slice_uuid_)*/{&::_pbi::fixed_address_empty_string, ::_pbi::ConstantInitialized{}}
  , /*decltype(_impl_._cached_size_)*/{}} {}
struct SliceUUIDDefaultTypeInternal {
  PROTOBUF_CONSTEXPR SliceUUIDDefaultTypeInternal()
      : _instance(::_pbi::ConstantInitialized{}) {}
  ~SliceUUIDDefaultTypeInternal() {}
  union {
    SliceUUID _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT PROTOBUF_ATTRIBUTE_INIT_PRIORITY1 SliceUUIDDefaultTypeInternal _SliceUUID_default_instance_;
PROTOBUF_CONSTEXPR SlicingFinished::SlicingFinished(
    ::_pbi::ConstantInitialized) {}
struct SlicingFinishedDefaultTypeInternal {
  PROTOBUF_CONSTEXPR SlicingFinishedDefaultTypeInternal()
      : _instance(::_pbi::ConstantInitialized{}) {}
  ~SlicingFinishedDefaultTypeInternal() {}
  union {
    SlicingFinished _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT PROTOBUF_ATTRIBUTE_INIT_PRIORITY1 SlicingFinishedDefaultTypeInternal _SlicingFinished_default_instance_;
}  // namespace proto
}  // namespace cura
static ::_pb::Metadata file_level_metadata_Cura_2eproto[19];
static const ::_pb::EnumDescriptor* file_level_enum_descriptors_Cura_2eproto[3];
static constexpr ::_pb::ServiceDescriptor const** file_level_service_descriptors_Cura_2eproto = nullptr;

const uint32_t TableStruct_Cura_2eproto::offsets[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::cura::proto::ObjectList, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::cura::proto::ObjectList, _impl_.objects_),
  PROTOBUF_FIELD_OFFSET(::cura::proto::ObjectList, _impl_.settings_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::cura::proto::EnginePlugin, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::cura::proto::EnginePlugin, _impl_.id_),
  PROTOBUF_FIELD_OFFSET(::cura::proto::EnginePlugin, _impl_.address_),
  PROTOBUF_FIELD_OFFSET(::cura::proto::EnginePlugin, _impl_.port_),
  PROTOBUF_FIELD_OFFSET(::cura::proto::EnginePlugin, _impl_.plugin_name_),
  PROTOBUF_FIELD_OFFSET(::cura::proto::EnginePlugin, _impl_.plugin_version_),
  PROTOBUF_FIELD_OFFSET(::cura::proto::Slice, _impl_._has_bits_),
  PROTOBUF_FIELD_OFFSET(::cura::proto::Slice, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::cura::proto::Slice, _impl_.object_lists_),
  PROTOBUF_FIELD_OFFSET(::cura::proto::Slice, _impl_.global_settings_),
  PROTOBUF_FIELD_OFFSET(::cura::proto::Slice, _impl_.extruders_),
  PROTOBUF_FIELD_OFFSET(::cura::proto::Slice, _impl_.limit_to_extruder_),
  PROTOBUF_FIELD_OFFSET(::cura::proto::Slice, _impl_.engine_plugins_),
  PROTOBUF_FIELD_OFFSET(::cura::proto::Slice, _impl_.sentry_id_),
  PROTOBUF_FIELD_OFFSET(::cura::proto::Slice, _impl_.cura_version_),
  PROTOBUF_FIELD_OFFSET(::cura::proto::Slice, _impl_.project_name_),
  PROTOBUF_FIELD_OFFSET(::cura::proto::Slice, _impl_.user_name_),
  ~0u,
  ~0u,
  ~0u,
  ~0u,
  ~0u,
  ~0u,
  ~0u,
  0,
  1,
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::cura::proto::Extruder, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::cura::proto::Extruder, _impl_.id_),
  PROTOBUF_FIELD_OFFSET(::cura::proto::Extruder, _impl_.settings_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::cura::proto::Object, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::cura::proto::Object, _impl_.id_),
  PROTOBUF_FIELD_OFFSET(::cura::proto::Object, _impl_.vertices_),
  PROTOBUF_FIELD_OFFSET(::cura::proto::Object, _impl_.normals_),
  PROTOBUF_FIELD_OFFSET(::cura::proto::Object, _impl_.indices_),
  PROTOBUF_FIELD_OFFSET(::cura::proto::Object, _impl_.settings_),
  PROTOBUF_FIELD_OFFSET(::cura::proto::Object, _impl_.name_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::cura::proto::Progress, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::cura::proto::Progress, _impl_.amount_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::cura::proto::Layer, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::cura::proto::Layer, _impl_.id_),
  PROTOBUF_FIELD_OFFSET(::cura::proto::Layer, _impl_.height_),
  PROTOBUF_FIELD_OFFSET(::cura::proto::Layer, _impl_.thickness_),
  PROTOBUF_FIELD_OFFSET(::cura::proto::Layer, _impl_.polygons_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::cura::proto::Polygon, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::cura::proto::Polygon, _impl_.type_),
  PROTOBUF_FIELD_OFFSET(::cura::proto::Polygon, _impl_.points_),
  PROTOBUF_FIELD_OFFSET(::cura::proto::Polygon, _impl_.line_width_),
  PROTOBUF_FIELD_OFFSET(::cura::proto::Polygon, _impl_.line_thickness_),
  PROTOBUF_FIELD_OFFSET(::cura::proto::Polygon, _impl_.line_feedrate_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::cura::proto::LayerOptimized, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::cura::proto::LayerOptimized, _impl_.id_),
  PROTOBUF_FIELD_OFFSET(::cura::proto::LayerOptimized, _impl_.height_),
  PROTOBUF_FIELD_OFFSET(::cura::proto::LayerOptimized, _impl_.thickness_),
  PROTOBUF_FIELD_OFFSET(::cura::proto::LayerOptimized, _impl_.path_segment_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::cura::proto::PathSegment, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::cura::proto::PathSegment, _impl_.extruder_),
  PROTOBUF_FIELD_OFFSET(::cura::proto::PathSegment, _impl_.point_type_),
  PROTOBUF_FIELD_OFFSET(::cura::proto::PathSegment, _impl_.points_),
  PROTOBUF_FIELD_OFFSET(::cura::proto::PathSegment, _impl_.line_type_),
  PROTOBUF_FIELD_OFFSET(::cura::proto::PathSegment, _impl_.line_width_),
  PROTOBUF_FIELD_OFFSET(::cura::proto::PathSegment, _impl_.line_thickness_),
  PROTOBUF_FIELD_OFFSET(::cura::proto::PathSegment, _impl_.line_feedrate_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::cura::proto::GCodeLayer, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::cura::proto::GCodeLayer, _impl_.data_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::cura::proto::PrintTimeMaterialEstimates, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::cura::proto::PrintTimeMaterialEstimates, _impl_.time_none_),
  PROTOBUF_FIELD_OFFSET(::cura::proto::PrintTimeMaterialEstimates, _impl_.time_inset_0_),
  PROTOBUF_FIELD_OFFSET(::cura::proto::PrintTimeMaterialEstimates, _impl_.time_inset_x_),
  PROTOBUF_FIELD_OFFSET(::cura::proto::PrintTimeMaterialEstimates, _impl_.time_skin_),
  PROTOBUF_FIELD_OFFSET(::cura::proto::PrintTimeMaterialEstimates, _impl_.time_support_),
  PROTOBUF_FIELD_OFFSET(::cura::proto::PrintTimeMaterialEstimates, _impl_.time_skirt_),
  PROTOBUF_FIELD_OFFSET(::cura::proto::PrintTimeMaterialEstimates, _impl_.time_infill_),
  PROTOBUF_FIELD_OFFSET(::cura::proto::PrintTimeMaterialEstimates, _impl_.time_support_infill_),
  PROTOBUF_FIELD_OFFSET(::cura::proto::PrintTimeMaterialEstimates, _impl_.time_travel_),
  PROTOBUF_FIELD_OFFSET(::cura::proto::PrintTimeMaterialEstimates, _impl_.time_retract_),
  PROTOBUF_FIELD_OFFSET(::cura::proto::PrintTimeMaterialEstimates, _impl_.time_support_interface_),
  PROTOBUF_FIELD_OFFSET(::cura::proto::PrintTimeMaterialEstimates, _impl_.time_prime_tower_),
  PROTOBUF_FIELD_OFFSET(::cura::proto::PrintTimeMaterialEstimates, _impl_.materialestimates_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::cura::proto::MaterialEstimates, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::cura::proto::MaterialEstimates, _impl_.id_),
  PROTOBUF_FIELD_OFFSET(::cura::proto::MaterialEstimates, _impl_.material_amount_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::cura::proto::SettingList, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::cura::proto::SettingList, _impl_.settings_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::cura::proto::Setting, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::cura::proto::Setting, _impl_.name_),
  PROTOBUF_FIELD_OFFSET(::cura::proto::Setting, _impl_.value_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::cura::proto::SettingExtruder, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::cura::proto::SettingExtruder, _impl_.name_),
  PROTOBUF_FIELD_OFFSET(::cura::proto::SettingExtruder, _impl_.extruder_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::cura::proto::GCodePrefix, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::cura::proto::GCodePrefix, _impl_.data_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::cura::proto::SliceUUID, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::cura::proto::SliceUUID, _impl_.slice_uuid_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::cura::proto::SlicingFinished, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
};
static const ::_pbi::MigrationSchema schemas[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  { 0, -1, -1, sizeof(::cura::proto::ObjectList)},
  { 8, -1, -1, sizeof(::cura::proto::EnginePlugin)},
  { 19, 34, -1, sizeof(::cura::proto::Slice)},
  { 43, -1, -1, sizeof(::cura::proto::Extruder)},
  { 51, -1, -1, sizeof(::cura::proto::Object)},
  { 63, -1, -1, sizeof(::cura::proto::Progress)},
  { 70, -1, -1, sizeof(::cura::proto::Layer)},
  { 80, -1, -1, sizeof(::cura::proto::Polygon)},
  { 91, -1, -1, sizeof(::cura::proto::LayerOptimized)},
  { 101, -1, -1, sizeof(::cura::proto::PathSegment)},
  { 114, -1, -1, sizeof(::cura::proto::GCodeLayer)},
  { 121, -1, -1, sizeof(::cura::proto::PrintTimeMaterialEstimates)},
  { 140, -1, -1, sizeof(::cura::proto::MaterialEstimates)},
  { 148, -1, -1, sizeof(::cura::proto::SettingList)},
  { 155, -1, -1, sizeof(::cura::proto::Setting)},
  { 163, -1, -1, sizeof(::cura::proto::SettingExtruder)},
  { 171, -1, -1, sizeof(::cura::proto::GCodePrefix)},
  { 178, -1, -1, sizeof(::cura::proto::SliceUUID)},
  { 185, -1, -1, sizeof(::cura::proto::SlicingFinished)},
};

static const ::_pb::Message* const file_default_instances[] = {
  &::cura::proto::_ObjectList_default_instance_._instance,
  &::cura::proto::_EnginePlugin_default_instance_._instance,
  &::cura::proto::_Slice_default_instance_._instance,
  &::cura::proto::_Extruder_default_instance_._instance,
  &::cura::proto::_Object_default_instance_._instance,
  &::cura::proto::_Progress_default_instance_._instance,
  &::cura::proto::_Layer_default_instance_._instance,
  &::cura::proto::_Polygon_default_instance_._instance,
  &::cura::proto::_LayerOptimized_default_instance_._instance,
  &::cura::proto::_PathSegment_default_instance_._instance,
  &::cura::proto::_GCodeLayer_default_instance_._instance,
  &::cura::proto::_PrintTimeMaterialEstimates_default_instance_._instance,
  &::cura::proto::_MaterialEstimates_default_instance_._instance,
  &::cura::proto::_SettingList_default_instance_._instance,
  &::cura::proto::_Setting_default_instance_._instance,
  &::cura::proto::_SettingExtruder_default_instance_._instance,
  &::cura::proto::_GCodePrefix_default_instance_._instance,
  &::cura::proto::_SliceUUID_default_instance_._instance,
  &::cura::proto::_SlicingFinished_default_instance_._instance,
};

const char descriptor_table_protodef_Cura_2eproto[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) =
  "\n\nCura.proto\022\ncura.proto\"X\n\nObjectList\022#"
  "\n\007objects\030\001 \003(\0132\022.cura.proto.Object\022%\n\010s"
  "ettings\030\002 \003(\0132\023.cura.proto.Setting\"z\n\014En"
  "ginePlugin\022\036\n\002id\030\001 \001(\0162\022.cura.proto.Slot"
  "ID\022\017\n\007address\030\002 \001(\t\022\014\n\004port\030\003 \001(\r\022\023\n\013plu"
  "gin_name\030\004 \001(\t\022\026\n\016plugin_version\030\005 \001(\t\"\365"
  "\002\n\005Slice\022,\n\014object_lists\030\001 \003(\0132\026.cura.pr"
  "oto.ObjectList\0220\n\017global_settings\030\002 \001(\0132"
  "\027.cura.proto.SettingList\022\'\n\textruders\030\003 "
  "\003(\0132\024.cura.proto.Extruder\0226\n\021limit_to_ex"
  "truder\030\004 \003(\0132\033.cura.proto.SettingExtrude"
  "r\0220\n\016engine_plugins\030\005 \003(\0132\030.cura.proto.E"
  "nginePlugin\022\021\n\tsentry_id\030\006 \001(\t\022\024\n\014cura_v"
  "ersion\030\007 \001(\t\022\031\n\014project_name\030\010 \001(\tH\000\210\001\001\022"
  "\026\n\tuser_name\030\t \001(\tH\001\210\001\001B\017\n\r_project_name"
  "B\014\n\n_user_name\"A\n\010Extruder\022\n\n\002id\030\001 \001(\005\022)"
  "\n\010settings\030\002 \001(\0132\027.cura.proto.SettingLis"
  "t\"}\n\006Object\022\n\n\002id\030\001 \001(\003\022\020\n\010vertices\030\002 \001("
  "\014\022\017\n\007normals\030\003 \001(\014\022\017\n\007indices\030\004 \001(\014\022%\n\010s"
  "ettings\030\005 \003(\0132\023.cura.proto.Setting\022\014\n\004na"
  "me\030\006 \001(\t\"\032\n\010Progress\022\016\n\006amount\030\001 \001(\002\"]\n\005"
  "Layer\022\n\n\002id\030\001 \001(\005\022\016\n\006height\030\002 \001(\002\022\021\n\tthi"
  "ckness\030\003 \001(\002\022%\n\010polygons\030\004 \003(\0132\023.cura.pr"
  "oto.Polygon\"\324\003\n\007Polygon\022&\n\004type\030\001 \001(\0162\030."
  "cura.proto.Polygon.Type\022\016\n\006points\030\002 \001(\014\022"
  "\022\n\nline_width\030\003 \001(\002\022\026\n\016line_thickness\030\004 "
  "\001(\002\022\025\n\rline_feedrate\030\005 \001(\002\"\315\002\n\004Type\022\014\n\010N"
  "oneType\020\000\022\016\n\nInset0Type\020\001\022\016\n\nInsetXType\020"
  "\002\022\014\n\010SkinType\020\003\022\017\n\013SupportType\020\004\022\r\n\tSkir"
  "tType\020\005\022\016\n\nInfillType\020\006\022\025\n\021SupportInfill"
  "Type\020\007\022\023\n\017MoveUnretracted\020\010\022\021\n\rMoveRetra"
  "cted\020\t\022\030\n\024SupportInterfaceType\020\n\022\022\n\016Prim"
  "eTowerType\020\013\022\027\n\023MoveWhileRetracting\020\014\022\031\n"
  "\025MoveWhileUnretracting\020\r\022\036\n\032StationaryRe"
  "tractUnretract\020\016\022\030\n\024NumPrintFeatureTypes"
  "\020\017\"n\n\016LayerOptimized\022\n\n\002id\030\001 \001(\005\022\016\n\006heig"
  "ht\030\002 \001(\002\022\021\n\tthickness\030\003 \001(\002\022-\n\014path_segm"
  "ent\030\004 \003(\0132\027.cura.proto.PathSegment\"\343\001\n\013P"
  "athSegment\022\020\n\010extruder\030\001 \001(\005\0225\n\npoint_ty"
  "pe\030\002 \001(\0162!.cura.proto.PathSegment.PointT"
  "ype\022\016\n\006points\030\003 \001(\014\022\021\n\tline_type\030\004 \001(\014\022\022"
  "\n\nline_width\030\005 \001(\014\022\026\n\016line_thickness\030\006 \001"
  "(\014\022\025\n\rline_feedrate\030\007 \001(\014\"%\n\tPointType\022\013"
  "\n\007Point2D\020\000\022\013\n\007Point3D\020\001\"\032\n\nGCodeLayer\022\014"
  "\n\004data\030\002 \001(\014\"\351\002\n\032PrintTimeMaterialEstima"
  "tes\022\021\n\ttime_none\030\001 \001(\002\022\024\n\014time_inset_0\030\002"
  " \001(\002\022\024\n\014time_inset_x\030\003 \001(\002\022\021\n\ttime_skin\030"
  "\004 \001(\002\022\024\n\014time_support\030\005 \001(\002\022\022\n\ntime_skir"
  "t\030\006 \001(\002\022\023\n\013time_infill\030\007 \001(\002\022\033\n\023time_sup"
  "port_infill\030\010 \001(\002\022\023\n\013time_travel\030\t \001(\002\022\024"
  "\n\014time_retract\030\n \001(\002\022\036\n\026time_support_int"
  "erface\030\013 \001(\002\022\030\n\020time_prime_tower\030\014 \001(\002\0228"
  "\n\021materialEstimates\030\r \003(\0132\035.cura.proto.M"
  "aterialEstimates\"8\n\021MaterialEstimates\022\n\n"
  "\002id\030\001 \001(\003\022\027\n\017material_amount\030\002 \001(\002\"4\n\013Se"
  "ttingList\022%\n\010settings\030\001 \003(\0132\023.cura.proto"
  ".Setting\"&\n\007Setting\022\014\n\004name\030\001 \001(\t\022\r\n\005val"
  "ue\030\002 \001(\014\"1\n\017SettingExtruder\022\014\n\004name\030\001 \001("
  "\t\022\020\n\010extruder\030\002 \001(\005\"\033\n\013GCodePrefix\022\014\n\004da"
  "ta\030\002 \001(\014\"\037\n\tSliceUUID\022\022\n\nslice_uuid\030\001 \001("
  "\t\"\021\n\017SlicingFinished*\216\001\n\006SlotID\022\026\n\022SETTI"
  "NGS_BROADCAST\020\000\022\023\n\017SIMPLIFY_MODIFY\020d\022\026\n\022"
  "POSTPROCESS_MODIFY\020e\022\021\n\rINFILL_MODIFY\020f\022"
  "\026\n\022GCODE_PATHS_MODIFY\020g\022\024\n\017INFILL_GENERA"
  "TE\020\310\001b\006proto3"
  ;
static ::_pbi::once_flag descriptor_table_Cura_2eproto_once;
const ::_pbi::DescriptorTable descriptor_table_Cura_2eproto = {
    false, false, 2573, descriptor_table_protodef_Cura_2eproto,
    "Cura.proto",
    &descriptor_table_Cura_2eproto_once, nullptr, 0, 19,
    schemas, file_default_instances, TableStruct_Cura_2eproto::offsets,
    file_level_metadata_Cura_2eproto, file_level_enum_descriptors_Cura_2eproto,
    file_level_service_descriptors_Cura_2eproto,
};
PROTOBUF_ATTRIBUTE_WEAK const ::_pbi::DescriptorTable* descriptor_table_Cura_2eproto_getter() {
  return &descriptor_table_Cura_2eproto;
}

// Force running AddDescriptors() at dynamic initialization time.
PROTOBUF_ATTRIBUTE_INIT_PRIORITY2 static ::_pbi::AddDescriptorsRunner dynamic_init_dummy_Cura_2eproto(&descriptor_table_Cura_2eproto);
namespace cura {
namespace proto {
const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* Polygon_Type_descriptor() {
  ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&descriptor_table_Cura_2eproto);
  return file_level_enum_descriptors_Cura_2eproto[0];
}
bool Polygon_Type_IsValid(int value) {
  switch (value) {
    case 0:
    case 1:
    case 2:
    case 3:
    case 4:
    case 5:
    case 6:
    case 7:
    case 8:
    case 9:
    case 10:
    case 11:
    case 12:
    case 13:
    case 14:
    case 15:
      return true;
    default:
      return false;
  }
}

#if (__cplusplus < 201703) && (!defined(_MSC_VER) || (_MSC_VER >= 1900 && _MSC_VER < 1912))
constexpr Polygon_Type Polygon::NoneType;
constexpr Polygon_Type Polygon::Inset0Type;
constexpr Polygon_Type Polygon::InsetXType;
constexpr Polygon_Type Polygon::SkinType;
constexpr Polygon_Type Polygon::SupportType;
constexpr Polygon_Type Polygon::SkirtType;
constexpr Polygon_Type Polygon::InfillType;
constexpr Polygon_Type Polygon::SupportInfillType;
constexpr Polygon_Type Polygon::MoveUnretracted;
constexpr Polygon_Type Polygon::MoveRetracted;
constexpr Polygon_Type Polygon::SupportInterfaceType;
constexpr Polygon_Type Polygon::PrimeTowerType;
constexpr Polygon_Type Polygon::MoveWhileRetracting;
constexpr Polygon_Type Polygon::MoveWhileUnretracting;
constexpr Polygon_Type Polygon::StationaryRetractUnretract;
constexpr Polygon_Type Polygon::NumPrintFeatureTypes;
constexpr Polygon_Type Polygon::Type_MIN;
constexpr Polygon_Type Polygon::Type_MAX;
constexpr int Polygon::Type_ARRAYSIZE;
#endif  // (__cplusplus < 201703) && (!defined(_MSC_VER) || (_MSC_VER >= 1900 && _MSC_VER < 1912))
const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* PathSegment_PointType_descriptor() {
  ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&descriptor_table_Cura_2eproto);
  return file_level_enum_descriptors_Cura_2eproto[1];
}
bool PathSegment_PointType_IsValid(int value) {
  switch (value) {
    case 0:
    case 1:
      return true;
    default:
      return false;
  }
}

#if (__cplusplus < 201703) && (!defined(_MSC_VER) || (_MSC_VER >= 1900 && _MSC_VER < 1912))
constexpr PathSegment_PointType PathSegment::Point2D;
constexpr PathSegment_PointType PathSegment::Point3D;
constexpr PathSegment_PointType PathSegment::PointType_MIN;
constexpr PathSegment_PointType PathSegment::PointType_MAX;
constexpr int PathSegment::PointType_ARRAYSIZE;
#endif  // (__cplusplus < 201703) && (!defined(_MSC_VER) || (_MSC_VER >= 1900 && _MSC_VER < 1912))
const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* SlotID_descriptor() {
  ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&descriptor_table_Cura_2eproto);
  return file_level_enum_descriptors_Cura_2eproto[2];
}
bool SlotID_IsValid(int value) {
  switch (value) {
    case 0:
    case 100:
    case 101:
    case 102:
    case 103:
    case 200:
      return true;
    default:
      return false;
  }
}


// ===================================================================

class ObjectList::_Internal {
 public:
};

ObjectList::ObjectList(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor(arena, is_message_owned);
  // @@protoc_insertion_point(arena_constructor:cura.proto.ObjectList)
}
ObjectList::ObjectList(const ObjectList& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  ObjectList* const _this = this; (void)_this;
  new (&_impl_) Impl_{
      decltype(_impl_.objects_){from._impl_.objects_}
    , decltype(_impl_.settings_){from._impl_.settings_}
    , /*decltype(_impl_._cached_size_)*/{}};

  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  // @@protoc_insertion_point(copy_constructor:cura.proto.ObjectList)
}

inline void ObjectList::SharedCtor(
    ::_pb::Arena* arena, bool is_message_owned) {
  (void)arena;
  (void)is_message_owned;
  new (&_impl_) Impl_{
      decltype(_impl_.objects_){arena}
    , decltype(_impl_.settings_){arena}
    , /*decltype(_impl_._cached_size_)*/{}
  };
}

ObjectList::~ObjectList() {
  // @@protoc_insertion_point(destructor:cura.proto.ObjectList)
  if (auto *arena = _internal_metadata_.DeleteReturnArena<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>()) {
  (void)arena;
    return;
  }
  SharedDtor();
}

inline void ObjectList::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  _impl_.objects_.~RepeatedPtrField();
  _impl_.settings_.~RepeatedPtrField();
}

void ObjectList::SetCachedSize(int size) const {
  _impl_._cached_size_.Set(size);
}

void ObjectList::Clear() {
// @@protoc_insertion_point(message_clear_start:cura.proto.ObjectList)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  _impl_.objects_.Clear();
  _impl_.settings_.Clear();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* ObjectList::_InternalParse(const char* ptr, ::_pbi::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::_pbi::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // repeated .cura.proto.Object objects = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_objects(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<10>(ptr));
        } else
          goto handle_unusual;
        continue;
      // repeated .cura.proto.Setting settings = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_settings(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<18>(ptr));
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* ObjectList::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:cura.proto.ObjectList)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated .cura.proto.Object objects = 1;
  for (unsigned i = 0,
      n = static_cast<unsigned>(this->_internal_objects_size()); i < n; i++) {
    const auto& repfield = this->_internal_objects(i);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
        InternalWriteMessage(1, repfield, repfield.GetCachedSize(), target, stream);
  }

  // repeated .cura.proto.Setting settings = 2;
  for (unsigned i = 0,
      n = static_cast<unsigned>(this->_internal_settings_size()); i < n; i++) {
    const auto& repfield = this->_internal_settings(i);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
        InternalWriteMessage(2, repfield, repfield.GetCachedSize(), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::_pbi::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:cura.proto.ObjectList)
  return target;
}

size_t ObjectList::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:cura.proto.ObjectList)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated .cura.proto.Object objects = 1;
  total_size += 1UL * this->_internal_objects_size();
  for (const auto& msg : this->_impl_.objects_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  // repeated .cura.proto.Setting settings = 2;
  total_size += 1UL * this->_internal_settings_size();
  for (const auto& msg : this->_impl_.settings_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_impl_._cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData ObjectList::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSourceCheck,
    ObjectList::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*ObjectList::GetClassData() const { return &_class_data_; }


void ObjectList::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg) {
  auto* const _this = static_cast<ObjectList*>(&to_msg);
  auto& from = static_cast<const ObjectList&>(from_msg);
  // @@protoc_insertion_point(class_specific_merge_from_start:cura.proto.ObjectList)
  GOOGLE_DCHECK_NE(&from, _this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  _this->_impl_.objects_.MergeFrom(from._impl_.objects_);
  _this->_impl_.settings_.MergeFrom(from._impl_.settings_);
  _this->_internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void ObjectList::CopyFrom(const ObjectList& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:cura.proto.ObjectList)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool ObjectList::IsInitialized() const {
  return true;
}

void ObjectList::InternalSwap(ObjectList* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  _impl_.objects_.InternalSwap(&other->_impl_.objects_);
  _impl_.settings_.InternalSwap(&other->_impl_.settings_);
}

::PROTOBUF_NAMESPACE_ID::Metadata ObjectList::GetMetadata() const {
  return ::_pbi::AssignDescriptors(
      &descriptor_table_Cura_2eproto_getter, &descriptor_table_Cura_2eproto_once,
      file_level_metadata_Cura_2eproto[0]);
}

// ===================================================================

class EnginePlugin::_Internal {
 public:
};

EnginePlugin::EnginePlugin(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor(arena, is_message_owned);
  // @@protoc_insertion_point(arena_constructor:cura.proto.EnginePlugin)
}
EnginePlugin::EnginePlugin(const EnginePlugin& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  EnginePlugin* const _this = this; (void)_this;
  new (&_impl_) Impl_{
      decltype(_impl_.address_){}
    , decltype(_impl_.plugin_name_){}
    , decltype(_impl_.plugin_version_){}
    , decltype(_impl_.id_){}
    , decltype(_impl_.port_){}
    , /*decltype(_impl_._cached_size_)*/{}};

  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  _impl_.address_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.address_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_address().empty()) {
    _this->_impl_.address_.Set(from._internal_address(), 
      _this->GetArenaForAllocation());
  }
  _impl_.plugin_name_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.plugin_name_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_plugin_name().empty()) {
    _this->_impl_.plugin_name_.Set(from._internal_plugin_name(), 
      _this->GetArenaForAllocation());
  }
  _impl_.plugin_version_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.plugin_version_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_plugin_version().empty()) {
    _this->_impl_.plugin_version_.Set(from._internal_plugin_version(), 
      _this->GetArenaForAllocation());
  }
  ::memcpy(&_impl_.id_, &from._impl_.id_,
    static_cast<size_t>(reinterpret_cast<char*>(&_impl_.port_) -
    reinterpret_cast<char*>(&_impl_.id_)) + sizeof(_impl_.port_));
  // @@protoc_insertion_point(copy_constructor:cura.proto.EnginePlugin)
}

inline void EnginePlugin::SharedCtor(
    ::_pb::Arena* arena, bool is_message_owned) {
  (void)arena;
  (void)is_message_owned;
  new (&_impl_) Impl_{
      decltype(_impl_.address_){}
    , decltype(_impl_.plugin_name_){}
    , decltype(_impl_.plugin_version_){}
    , decltype(_impl_.id_){0}
    , decltype(_impl_.port_){0u}
    , /*decltype(_impl_._cached_size_)*/{}
  };
  _impl_.address_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.address_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  _impl_.plugin_name_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.plugin_name_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  _impl_.plugin_version_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.plugin_version_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
}

EnginePlugin::~EnginePlugin() {
  // @@protoc_insertion_point(destructor:cura.proto.EnginePlugin)
  if (auto *arena = _internal_metadata_.DeleteReturnArena<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>()) {
  (void)arena;
    return;
  }
  SharedDtor();
}

inline void EnginePlugin::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  _impl_.address_.Destroy();
  _impl_.plugin_name_.Destroy();
  _impl_.plugin_version_.Destroy();
}

void EnginePlugin::SetCachedSize(int size) const {
  _impl_._cached_size_.Set(size);
}

void EnginePlugin::Clear() {
// @@protoc_insertion_point(message_clear_start:cura.proto.EnginePlugin)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  _impl_.address_.ClearToEmpty();
  _impl_.plugin_name_.ClearToEmpty();
  _impl_.plugin_version_.ClearToEmpty();
  ::memset(&_impl_.id_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&_impl_.port_) -
      reinterpret_cast<char*>(&_impl_.id_)) + sizeof(_impl_.port_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* EnginePlugin::_InternalParse(const char* ptr, ::_pbi::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::_pbi::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // .cura.proto.SlotID id = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 8)) {
          uint64_t val = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
          _internal_set_id(static_cast<::cura::proto::SlotID>(val));
        } else
          goto handle_unusual;
        continue;
      // string address = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          auto str = _internal_mutable_address();
          ptr = ::_pbi::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(ptr);
          CHK_(::_pbi::VerifyUTF8(str, "cura.proto.EnginePlugin.address"));
        } else
          goto handle_unusual;
        continue;
      // uint32 port = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 24)) {
          _impl_.port_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string plugin_name = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 34)) {
          auto str = _internal_mutable_plugin_name();
          ptr = ::_pbi::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(ptr);
          CHK_(::_pbi::VerifyUTF8(str, "cura.proto.EnginePlugin.plugin_name"));
        } else
          goto handle_unusual;
        continue;
      // string plugin_version = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 42)) {
          auto str = _internal_mutable_plugin_version();
          ptr = ::_pbi::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(ptr);
          CHK_(::_pbi::VerifyUTF8(str, "cura.proto.EnginePlugin.plugin_version"));
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* EnginePlugin::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:cura.proto.EnginePlugin)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // .cura.proto.SlotID id = 1;
  if (this->_internal_id() != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteEnumToArray(
      1, this->_internal_id(), target);
  }

  // string address = 2;
  if (!this->_internal_address().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_address().data(), static_cast<int>(this->_internal_address().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "cura.proto.EnginePlugin.address");
    target = stream->WriteStringMaybeAliased(
        2, this->_internal_address(), target);
  }

  // uint32 port = 3;
  if (this->_internal_port() != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteUInt32ToArray(3, this->_internal_port(), target);
  }

  // string plugin_name = 4;
  if (!this->_internal_plugin_name().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_plugin_name().data(), static_cast<int>(this->_internal_plugin_name().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "cura.proto.EnginePlugin.plugin_name");
    target = stream->WriteStringMaybeAliased(
        4, this->_internal_plugin_name(), target);
  }

  // string plugin_version = 5;
  if (!this->_internal_plugin_version().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_plugin_version().data(), static_cast<int>(this->_internal_plugin_version().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "cura.proto.EnginePlugin.plugin_version");
    target = stream->WriteStringMaybeAliased(
        5, this->_internal_plugin_version(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::_pbi::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:cura.proto.EnginePlugin)
  return target;
}

size_t EnginePlugin::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:cura.proto.EnginePlugin)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // string address = 2;
  if (!this->_internal_address().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_address());
  }

  // string plugin_name = 4;
  if (!this->_internal_plugin_name().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_plugin_name());
  }

  // string plugin_version = 5;
  if (!this->_internal_plugin_version().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_plugin_version());
  }

  // .cura.proto.SlotID id = 1;
  if (this->_internal_id() != 0) {
    total_size += 1 +
      ::_pbi::WireFormatLite::EnumSize(this->_internal_id());
  }

  // uint32 port = 3;
  if (this->_internal_port() != 0) {
    total_size += ::_pbi::WireFormatLite::UInt32SizePlusOne(this->_internal_port());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_impl_._cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData EnginePlugin::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSourceCheck,
    EnginePlugin::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*EnginePlugin::GetClassData() const { return &_class_data_; }


void EnginePlugin::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg) {
  auto* const _this = static_cast<EnginePlugin*>(&to_msg);
  auto& from = static_cast<const EnginePlugin&>(from_msg);
  // @@protoc_insertion_point(class_specific_merge_from_start:cura.proto.EnginePlugin)
  GOOGLE_DCHECK_NE(&from, _this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (!from._internal_address().empty()) {
    _this->_internal_set_address(from._internal_address());
  }
  if (!from._internal_plugin_name().empty()) {
    _this->_internal_set_plugin_name(from._internal_plugin_name());
  }
  if (!from._internal_plugin_version().empty()) {
    _this->_internal_set_plugin_version(from._internal_plugin_version());
  }
  if (from._internal_id() != 0) {
    _this->_internal_set_id(from._internal_id());
  }
  if (from._internal_port() != 0) {
    _this->_internal_set_port(from._internal_port());
  }
  _this->_internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void EnginePlugin::CopyFrom(const EnginePlugin& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:cura.proto.EnginePlugin)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool EnginePlugin::IsInitialized() const {
  return true;
}

void EnginePlugin::InternalSwap(EnginePlugin* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &_impl_.address_, lhs_arena,
      &other->_impl_.address_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &_impl_.plugin_name_, lhs_arena,
      &other->_impl_.plugin_name_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &_impl_.plugin_version_, lhs_arena,
      &other->_impl_.plugin_version_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(EnginePlugin, _impl_.port_)
      + sizeof(EnginePlugin::_impl_.port_)
      - PROTOBUF_FIELD_OFFSET(EnginePlugin, _impl_.id_)>(
          reinterpret_cast<char*>(&_impl_.id_),
          reinterpret_cast<char*>(&other->_impl_.id_));
}

::PROTOBUF_NAMESPACE_ID::Metadata EnginePlugin::GetMetadata() const {
  return ::_pbi::AssignDescriptors(
      &descriptor_table_Cura_2eproto_getter, &descriptor_table_Cura_2eproto_once,
      file_level_metadata_Cura_2eproto[1]);
}

// ===================================================================

class Slice::_Internal {
 public:
  using HasBits = decltype(std::declval<Slice>()._impl_._has_bits_);
  static const ::cura::proto::SettingList& global_settings(const Slice* msg);
  static void set_has_project_name(HasBits* has_bits) {
    (*has_bits)[0] |= 1u;
  }
  static void set_has_user_name(HasBits* has_bits) {
    (*has_bits)[0] |= 2u;
  }
};

const ::cura::proto::SettingList&
Slice::_Internal::global_settings(const Slice* msg) {
  return *msg->_impl_.global_settings_;
}
Slice::Slice(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor(arena, is_message_owned);
  // @@protoc_insertion_point(arena_constructor:cura.proto.Slice)
}
Slice::Slice(const Slice& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  Slice* const _this = this; (void)_this;
  new (&_impl_) Impl_{
      decltype(_impl_._has_bits_){from._impl_._has_bits_}
    , /*decltype(_impl_._cached_size_)*/{}
    , decltype(_impl_.object_lists_){from._impl_.object_lists_}
    , decltype(_impl_.extruders_){from._impl_.extruders_}
    , decltype(_impl_.limit_to_extruder_){from._impl_.limit_to_extruder_}
    , decltype(_impl_.engine_plugins_){from._impl_.engine_plugins_}
    , decltype(_impl_.sentry_id_){}
    , decltype(_impl_.cura_version_){}
    , decltype(_impl_.project_name_){}
    , decltype(_impl_.user_name_){}
    , decltype(_impl_.global_settings_){nullptr}};

  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  _impl_.sentry_id_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.sentry_id_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_sentry_id().empty()) {
    _this->_impl_.sentry_id_.Set(from._internal_sentry_id(), 
      _this->GetArenaForAllocation());
  }
  _impl_.cura_version_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.cura_version_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_cura_version().empty()) {
    _this->_impl_.cura_version_.Set(from._internal_cura_version(), 
      _this->GetArenaForAllocation());
  }
  _impl_.project_name_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.project_name_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (from._internal_has_project_name()) {
    _this->_impl_.project_name_.Set(from._internal_project_name(), 
      _this->GetArenaForAllocation());
  }
  _impl_.user_name_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.user_name_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (from._internal_has_user_name()) {
    _this->_impl_.user_name_.Set(from._internal_user_name(), 
      _this->GetArenaForAllocation());
  }
  if (from._internal_has_global_settings()) {
    _this->_impl_.global_settings_ = new ::cura::proto::SettingList(*from._impl_.global_settings_);
  }
  // @@protoc_insertion_point(copy_constructor:cura.proto.Slice)
}

inline void Slice::SharedCtor(
    ::_pb::Arena* arena, bool is_message_owned) {
  (void)arena;
  (void)is_message_owned;
  new (&_impl_) Impl_{
      decltype(_impl_._has_bits_){}
    , /*decltype(_impl_._cached_size_)*/{}
    , decltype(_impl_.object_lists_){arena}
    , decltype(_impl_.extruders_){arena}
    , decltype(_impl_.limit_to_extruder_){arena}
    , decltype(_impl_.engine_plugins_){arena}
    , decltype(_impl_.sentry_id_){}
    , decltype(_impl_.cura_version_){}
    , decltype(_impl_.project_name_){}
    , decltype(_impl_.user_name_){}
    , decltype(_impl_.global_settings_){nullptr}
  };
  _impl_.sentry_id_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.sentry_id_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  _impl_.cura_version_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.cura_version_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  _impl_.project_name_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.project_name_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  _impl_.user_name_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.user_name_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
}

Slice::~Slice() {
  // @@protoc_insertion_point(destructor:cura.proto.Slice)
  if (auto *arena = _internal_metadata_.DeleteReturnArena<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>()) {
  (void)arena;
    return;
  }
  SharedDtor();
}

inline void Slice::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  _impl_.object_lists_.~RepeatedPtrField();
  _impl_.extruders_.~RepeatedPtrField();
  _impl_.limit_to_extruder_.~RepeatedPtrField();
  _impl_.engine_plugins_.~RepeatedPtrField();
  _impl_.sentry_id_.Destroy();
  _impl_.cura_version_.Destroy();
  _impl_.project_name_.Destroy();
  _impl_.user_name_.Destroy();
  if (this != internal_default_instance()) delete _impl_.global_settings_;
}

void Slice::SetCachedSize(int size) const {
  _impl_._cached_size_.Set(size);
}

void Slice::Clear() {
// @@protoc_insertion_point(message_clear_start:cura.proto.Slice)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  _impl_.object_lists_.Clear();
  _impl_.extruders_.Clear();
  _impl_.limit_to_extruder_.Clear();
  _impl_.engine_plugins_.Clear();
  _impl_.sentry_id_.ClearToEmpty();
  _impl_.cura_version_.ClearToEmpty();
  cached_has_bits = _impl_._has_bits_[0];
  if (cached_has_bits & 0x00000003u) {
    if (cached_has_bits & 0x00000001u) {
      _impl_.project_name_.ClearNonDefaultToEmpty();
    }
    if (cached_has_bits & 0x00000002u) {
      _impl_.user_name_.ClearNonDefaultToEmpty();
    }
  }
  if (GetArenaForAllocation() == nullptr && _impl_.global_settings_ != nullptr) {
    delete _impl_.global_settings_;
  }
  _impl_.global_settings_ = nullptr;
  _impl_._has_bits_.Clear();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* Slice::_InternalParse(const char* ptr, ::_pbi::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  _Internal::HasBits has_bits{};
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::_pbi::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // repeated .cura.proto.ObjectList object_lists = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_object_lists(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<10>(ptr));
        } else
          goto handle_unusual;
        continue;
      // .cura.proto.SettingList global_settings = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          ptr = ctx->ParseMessage(_internal_mutable_global_settings(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // repeated .cura.proto.Extruder extruders = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 26)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_extruders(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<26>(ptr));
        } else
          goto handle_unusual;
        continue;
      // repeated .cura.proto.SettingExtruder limit_to_extruder = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 34)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_limit_to_extruder(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<34>(ptr));
        } else
          goto handle_unusual;
        continue;
      // repeated .cura.proto.EnginePlugin engine_plugins = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 42)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_engine_plugins(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<42>(ptr));
        } else
          goto handle_unusual;
        continue;
      // string sentry_id = 6;
      case 6:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 50)) {
          auto str = _internal_mutable_sentry_id();
          ptr = ::_pbi::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(ptr);
          CHK_(::_pbi::VerifyUTF8(str, "cura.proto.Slice.sentry_id"));
        } else
          goto handle_unusual;
        continue;
      // string cura_version = 7;
      case 7:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 58)) {
          auto str = _internal_mutable_cura_version();
          ptr = ::_pbi::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(ptr);
          CHK_(::_pbi::VerifyUTF8(str, "cura.proto.Slice.cura_version"));
        } else
          goto handle_unusual;
        continue;
      // optional string project_name = 8;
      case 8:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 66)) {
          auto str = _internal_mutable_project_name();
          ptr = ::_pbi::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(ptr);
          CHK_(::_pbi::VerifyUTF8(str, "cura.proto.Slice.project_name"));
        } else
          goto handle_unusual;
        continue;
      // optional string user_name = 9;
      case 9:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 74)) {
          auto str = _internal_mutable_user_name();
          ptr = ::_pbi::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(ptr);
          CHK_(::_pbi::VerifyUTF8(str, "cura.proto.Slice.user_name"));
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  _impl_._has_bits_.Or(has_bits);
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* Slice::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:cura.proto.Slice)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated .cura.proto.ObjectList object_lists = 1;
  for (unsigned i = 0,
      n = static_cast<unsigned>(this->_internal_object_lists_size()); i < n; i++) {
    const auto& repfield = this->_internal_object_lists(i);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
        InternalWriteMessage(1, repfield, repfield.GetCachedSize(), target, stream);
  }

  // .cura.proto.SettingList global_settings = 2;
  if (this->_internal_has_global_settings()) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(2, _Internal::global_settings(this),
        _Internal::global_settings(this).GetCachedSize(), target, stream);
  }

  // repeated .cura.proto.Extruder extruders = 3;
  for (unsigned i = 0,
      n = static_cast<unsigned>(this->_internal_extruders_size()); i < n; i++) {
    const auto& repfield = this->_internal_extruders(i);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
        InternalWriteMessage(3, repfield, repfield.GetCachedSize(), target, stream);
  }

  // repeated .cura.proto.SettingExtruder limit_to_extruder = 4;
  for (unsigned i = 0,
      n = static_cast<unsigned>(this->_internal_limit_to_extruder_size()); i < n; i++) {
    const auto& repfield = this->_internal_limit_to_extruder(i);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
        InternalWriteMessage(4, repfield, repfield.GetCachedSize(), target, stream);
  }

  // repeated .cura.proto.EnginePlugin engine_plugins = 5;
  for (unsigned i = 0,
      n = static_cast<unsigned>(this->_internal_engine_plugins_size()); i < n; i++) {
    const auto& repfield = this->_internal_engine_plugins(i);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
        InternalWriteMessage(5, repfield, repfield.GetCachedSize(), target, stream);
  }

  // string sentry_id = 6;
  if (!this->_internal_sentry_id().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_sentry_id().data(), static_cast<int>(this->_internal_sentry_id().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "cura.proto.Slice.sentry_id");
    target = stream->WriteStringMaybeAliased(
        6, this->_internal_sentry_id(), target);
  }

  // string cura_version = 7;
  if (!this->_internal_cura_version().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_cura_version().data(), static_cast<int>(this->_internal_cura_version().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "cura.proto.Slice.cura_version");
    target = stream->WriteStringMaybeAliased(
        7, this->_internal_cura_version(), target);
  }

  // optional string project_name = 8;
  if (_internal_has_project_name()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_project_name().data(), static_cast<int>(this->_internal_project_name().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "cura.proto.Slice.project_name");
    target = stream->WriteStringMaybeAliased(
        8, this->_internal_project_name(), target);
  }

  // optional string user_name = 9;
  if (_internal_has_user_name()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_user_name().data(), static_cast<int>(this->_internal_user_name().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "cura.proto.Slice.user_name");
    target = stream->WriteStringMaybeAliased(
        9, this->_internal_user_name(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::_pbi::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:cura.proto.Slice)
  return target;
}

size_t Slice::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:cura.proto.Slice)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated .cura.proto.ObjectList object_lists = 1;
  total_size += 1UL * this->_internal_object_lists_size();
  for (const auto& msg : this->_impl_.object_lists_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  // repeated .cura.proto.Extruder extruders = 3;
  total_size += 1UL * this->_internal_extruders_size();
  for (const auto& msg : this->_impl_.extruders_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  // repeated .cura.proto.SettingExtruder limit_to_extruder = 4;
  total_size += 1UL * this->_internal_limit_to_extruder_size();
  for (const auto& msg : this->_impl_.limit_to_extruder_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  // repeated .cura.proto.EnginePlugin engine_plugins = 5;
  total_size += 1UL * this->_internal_engine_plugins_size();
  for (const auto& msg : this->_impl_.engine_plugins_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  // string sentry_id = 6;
  if (!this->_internal_sentry_id().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_sentry_id());
  }

  // string cura_version = 7;
  if (!this->_internal_cura_version().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_cura_version());
  }

  cached_has_bits = _impl_._has_bits_[0];
  if (cached_has_bits & 0x00000003u) {
    // optional string project_name = 8;
    if (cached_has_bits & 0x00000001u) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
          this->_internal_project_name());
    }

    // optional string user_name = 9;
    if (cached_has_bits & 0x00000002u) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
          this->_internal_user_name());
    }

  }
  // .cura.proto.SettingList global_settings = 2;
  if (this->_internal_has_global_settings()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *_impl_.global_settings_);
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_impl_._cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData Slice::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSourceCheck,
    Slice::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*Slice::GetClassData() const { return &_class_data_; }


void Slice::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg) {
  auto* const _this = static_cast<Slice*>(&to_msg);
  auto& from = static_cast<const Slice&>(from_msg);
  // @@protoc_insertion_point(class_specific_merge_from_start:cura.proto.Slice)
  GOOGLE_DCHECK_NE(&from, _this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  _this->_impl_.object_lists_.MergeFrom(from._impl_.object_lists_);
  _this->_impl_.extruders_.MergeFrom(from._impl_.extruders_);
  _this->_impl_.limit_to_extruder_.MergeFrom(from._impl_.limit_to_extruder_);
  _this->_impl_.engine_plugins_.MergeFrom(from._impl_.engine_plugins_);
  if (!from._internal_sentry_id().empty()) {
    _this->_internal_set_sentry_id(from._internal_sentry_id());
  }
  if (!from._internal_cura_version().empty()) {
    _this->_internal_set_cura_version(from._internal_cura_version());
  }
  cached_has_bits = from._impl_._has_bits_[0];
  if (cached_has_bits & 0x00000003u) {
    if (cached_has_bits & 0x00000001u) {
      _this->_internal_set_project_name(from._internal_project_name());
    }
    if (cached_has_bits & 0x00000002u) {
      _this->_internal_set_user_name(from._internal_user_name());
    }
  }
  if (from._internal_has_global_settings()) {
    _this->_internal_mutable_global_settings()->::cura::proto::SettingList::MergeFrom(
        from._internal_global_settings());
  }
  _this->_internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void Slice::CopyFrom(const Slice& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:cura.proto.Slice)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool Slice::IsInitialized() const {
  return true;
}

void Slice::InternalSwap(Slice* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  swap(_impl_._has_bits_[0], other->_impl_._has_bits_[0]);
  _impl_.object_lists_.InternalSwap(&other->_impl_.object_lists_);
  _impl_.extruders_.InternalSwap(&other->_impl_.extruders_);
  _impl_.limit_to_extruder_.InternalSwap(&other->_impl_.limit_to_extruder_);
  _impl_.engine_plugins_.InternalSwap(&other->_impl_.engine_plugins_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &_impl_.sentry_id_, lhs_arena,
      &other->_impl_.sentry_id_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &_impl_.cura_version_, lhs_arena,
      &other->_impl_.cura_version_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &_impl_.project_name_, lhs_arena,
      &other->_impl_.project_name_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &_impl_.user_name_, lhs_arena,
      &other->_impl_.user_name_, rhs_arena
  );
  swap(_impl_.global_settings_, other->_impl_.global_settings_);
}

::PROTOBUF_NAMESPACE_ID::Metadata Slice::GetMetadata() const {
  return ::_pbi::AssignDescriptors(
      &descriptor_table_Cura_2eproto_getter, &descriptor_table_Cura_2eproto_once,
      file_level_metadata_Cura_2eproto[2]);
}

// ===================================================================

class Extruder::_Internal {
 public:
  static const ::cura::proto::SettingList& settings(const Extruder* msg);
};

const ::cura::proto::SettingList&
Extruder::_Internal::settings(const Extruder* msg) {
  return *msg->_impl_.settings_;
}
Extruder::Extruder(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor(arena, is_message_owned);
  // @@protoc_insertion_point(arena_constructor:cura.proto.Extruder)
}
Extruder::Extruder(const Extruder& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  Extruder* const _this = this; (void)_this;
  new (&_impl_) Impl_{
      decltype(_impl_.settings_){nullptr}
    , decltype(_impl_.id_){}
    , /*decltype(_impl_._cached_size_)*/{}};

  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  if (from._internal_has_settings()) {
    _this->_impl_.settings_ = new ::cura::proto::SettingList(*from._impl_.settings_);
  }
  _this->_impl_.id_ = from._impl_.id_;
  // @@protoc_insertion_point(copy_constructor:cura.proto.Extruder)
}

inline void Extruder::SharedCtor(
    ::_pb::Arena* arena, bool is_message_owned) {
  (void)arena;
  (void)is_message_owned;
  new (&_impl_) Impl_{
      decltype(_impl_.settings_){nullptr}
    , decltype(_impl_.id_){0}
    , /*decltype(_impl_._cached_size_)*/{}
  };
}

Extruder::~Extruder() {
  // @@protoc_insertion_point(destructor:cura.proto.Extruder)
  if (auto *arena = _internal_metadata_.DeleteReturnArena<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>()) {
  (void)arena;
    return;
  }
  SharedDtor();
}

inline void Extruder::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  if (this != internal_default_instance()) delete _impl_.settings_;
}

void Extruder::SetCachedSize(int size) const {
  _impl_._cached_size_.Set(size);
}

void Extruder::Clear() {
// @@protoc_insertion_point(message_clear_start:cura.proto.Extruder)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  if (GetArenaForAllocation() == nullptr && _impl_.settings_ != nullptr) {
    delete _impl_.settings_;
  }
  _impl_.settings_ = nullptr;
  _impl_.id_ = 0;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* Extruder::_InternalParse(const char* ptr, ::_pbi::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::_pbi::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // int32 id = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 8)) {
          _impl_.id_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .cura.proto.SettingList settings = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          ptr = ctx->ParseMessage(_internal_mutable_settings(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* Extruder::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:cura.proto.Extruder)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // int32 id = 1;
  if (this->_internal_id() != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteInt32ToArray(1, this->_internal_id(), target);
  }

  // .cura.proto.SettingList settings = 2;
  if (this->_internal_has_settings()) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(2, _Internal::settings(this),
        _Internal::settings(this).GetCachedSize(), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::_pbi::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:cura.proto.Extruder)
  return target;
}

size_t Extruder::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:cura.proto.Extruder)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // .cura.proto.SettingList settings = 2;
  if (this->_internal_has_settings()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *_impl_.settings_);
  }

  // int32 id = 1;
  if (this->_internal_id() != 0) {
    total_size += ::_pbi::WireFormatLite::Int32SizePlusOne(this->_internal_id());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_impl_._cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData Extruder::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSourceCheck,
    Extruder::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*Extruder::GetClassData() const { return &_class_data_; }


void Extruder::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg) {
  auto* const _this = static_cast<Extruder*>(&to_msg);
  auto& from = static_cast<const Extruder&>(from_msg);
  // @@protoc_insertion_point(class_specific_merge_from_start:cura.proto.Extruder)
  GOOGLE_DCHECK_NE(&from, _this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_has_settings()) {
    _this->_internal_mutable_settings()->::cura::proto::SettingList::MergeFrom(
        from._internal_settings());
  }
  if (from._internal_id() != 0) {
    _this->_internal_set_id(from._internal_id());
  }
  _this->_internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void Extruder::CopyFrom(const Extruder& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:cura.proto.Extruder)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool Extruder::IsInitialized() const {
  return true;
}

void Extruder::InternalSwap(Extruder* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(Extruder, _impl_.id_)
      + sizeof(Extruder::_impl_.id_)
      - PROTOBUF_FIELD_OFFSET(Extruder, _impl_.settings_)>(
          reinterpret_cast<char*>(&_impl_.settings_),
          reinterpret_cast<char*>(&other->_impl_.settings_));
}

::PROTOBUF_NAMESPACE_ID::Metadata Extruder::GetMetadata() const {
  return ::_pbi::AssignDescriptors(
      &descriptor_table_Cura_2eproto_getter, &descriptor_table_Cura_2eproto_once,
      file_level_metadata_Cura_2eproto[3]);
}

// ===================================================================

class Object::_Internal {
 public:
};

Object::Object(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor(arena, is_message_owned);
  // @@protoc_insertion_point(arena_constructor:cura.proto.Object)
}
Object::Object(const Object& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  Object* const _this = this; (void)_this;
  new (&_impl_) Impl_{
      decltype(_impl_.settings_){from._impl_.settings_}
    , decltype(_impl_.vertices_){}
    , decltype(_impl_.normals_){}
    , decltype(_impl_.indices_){}
    , decltype(_impl_.name_){}
    , decltype(_impl_.id_){}
    , /*decltype(_impl_._cached_size_)*/{}};

  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  _impl_.vertices_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.vertices_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_vertices().empty()) {
    _this->_impl_.vertices_.Set(from._internal_vertices(), 
      _this->GetArenaForAllocation());
  }
  _impl_.normals_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.normals_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_normals().empty()) {
    _this->_impl_.normals_.Set(from._internal_normals(), 
      _this->GetArenaForAllocation());
  }
  _impl_.indices_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.indices_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_indices().empty()) {
    _this->_impl_.indices_.Set(from._internal_indices(), 
      _this->GetArenaForAllocation());
  }
  _impl_.name_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.name_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_name().empty()) {
    _this->_impl_.name_.Set(from._internal_name(), 
      _this->GetArenaForAllocation());
  }
  _this->_impl_.id_ = from._impl_.id_;
  // @@protoc_insertion_point(copy_constructor:cura.proto.Object)
}

inline void Object::SharedCtor(
    ::_pb::Arena* arena, bool is_message_owned) {
  (void)arena;
  (void)is_message_owned;
  new (&_impl_) Impl_{
      decltype(_impl_.settings_){arena}
    , decltype(_impl_.vertices_){}
    , decltype(_impl_.normals_){}
    , decltype(_impl_.indices_){}
    , decltype(_impl_.name_){}
    , decltype(_impl_.id_){int64_t{0}}
    , /*decltype(_impl_._cached_size_)*/{}
  };
  _impl_.vertices_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.vertices_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  _impl_.normals_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.normals_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  _impl_.indices_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.indices_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  _impl_.name_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.name_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
}

Object::~Object() {
  // @@protoc_insertion_point(destructor:cura.proto.Object)
  if (auto *arena = _internal_metadata_.DeleteReturnArena<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>()) {
  (void)arena;
    return;
  }
  SharedDtor();
}

inline void Object::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  _impl_.settings_.~RepeatedPtrField();
  _impl_.vertices_.Destroy();
  _impl_.normals_.Destroy();
  _impl_.indices_.Destroy();
  _impl_.name_.Destroy();
}

void Object::SetCachedSize(int size) const {
  _impl_._cached_size_.Set(size);
}

void Object::Clear() {
// @@protoc_insertion_point(message_clear_start:cura.proto.Object)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  _impl_.settings_.Clear();
  _impl_.vertices_.ClearToEmpty();
  _impl_.normals_.ClearToEmpty();
  _impl_.indices_.ClearToEmpty();
  _impl_.name_.ClearToEmpty();
  _impl_.id_ = int64_t{0};
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* Object::_InternalParse(const char* ptr, ::_pbi::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::_pbi::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // int64 id = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 8)) {
          _impl_.id_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // bytes vertices = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          auto str = _internal_mutable_vertices();
          ptr = ::_pbi::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // bytes normals = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 26)) {
          auto str = _internal_mutable_normals();
          ptr = ::_pbi::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // bytes indices = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 34)) {
          auto str = _internal_mutable_indices();
          ptr = ::_pbi::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // repeated .cura.proto.Setting settings = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 42)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_settings(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<42>(ptr));
        } else
          goto handle_unusual;
        continue;
      // string name = 6;
      case 6:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 50)) {
          auto str = _internal_mutable_name();
          ptr = ::_pbi::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(ptr);
          CHK_(::_pbi::VerifyUTF8(str, "cura.proto.Object.name"));
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* Object::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:cura.proto.Object)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // int64 id = 1;
  if (this->_internal_id() != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteInt64ToArray(1, this->_internal_id(), target);
  }

  // bytes vertices = 2;
  if (!this->_internal_vertices().empty()) {
    target = stream->WriteBytesMaybeAliased(
        2, this->_internal_vertices(), target);
  }

  // bytes normals = 3;
  if (!this->_internal_normals().empty()) {
    target = stream->WriteBytesMaybeAliased(
        3, this->_internal_normals(), target);
  }

  // bytes indices = 4;
  if (!this->_internal_indices().empty()) {
    target = stream->WriteBytesMaybeAliased(
        4, this->_internal_indices(), target);
  }

  // repeated .cura.proto.Setting settings = 5;
  for (unsigned i = 0,
      n = static_cast<unsigned>(this->_internal_settings_size()); i < n; i++) {
    const auto& repfield = this->_internal_settings(i);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
        InternalWriteMessage(5, repfield, repfield.GetCachedSize(), target, stream);
  }

  // string name = 6;
  if (!this->_internal_name().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_name().data(), static_cast<int>(this->_internal_name().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "cura.proto.Object.name");
    target = stream->WriteStringMaybeAliased(
        6, this->_internal_name(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::_pbi::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:cura.proto.Object)
  return target;
}

size_t Object::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:cura.proto.Object)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated .cura.proto.Setting settings = 5;
  total_size += 1UL * this->_internal_settings_size();
  for (const auto& msg : this->_impl_.settings_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  // bytes vertices = 2;
  if (!this->_internal_vertices().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::BytesSize(
        this->_internal_vertices());
  }

  // bytes normals = 3;
  if (!this->_internal_normals().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::BytesSize(
        this->_internal_normals());
  }

  // bytes indices = 4;
  if (!this->_internal_indices().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::BytesSize(
        this->_internal_indices());
  }

  // string name = 6;
  if (!this->_internal_name().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_name());
  }

  // int64 id = 1;
  if (this->_internal_id() != 0) {
    total_size += ::_pbi::WireFormatLite::Int64SizePlusOne(this->_internal_id());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_impl_._cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData Object::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSourceCheck,
    Object::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*Object::GetClassData() const { return &_class_data_; }


void Object::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg) {
  auto* const _this = static_cast<Object*>(&to_msg);
  auto& from = static_cast<const Object&>(from_msg);
  // @@protoc_insertion_point(class_specific_merge_from_start:cura.proto.Object)
  GOOGLE_DCHECK_NE(&from, _this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  _this->_impl_.settings_.MergeFrom(from._impl_.settings_);
  if (!from._internal_vertices().empty()) {
    _this->_internal_set_vertices(from._internal_vertices());
  }
  if (!from._internal_normals().empty()) {
    _this->_internal_set_normals(from._internal_normals());
  }
  if (!from._internal_indices().empty()) {
    _this->_internal_set_indices(from._internal_indices());
  }
  if (!from._internal_name().empty()) {
    _this->_internal_set_name(from._internal_name());
  }
  if (from._internal_id() != 0) {
    _this->_internal_set_id(from._internal_id());
  }
  _this->_internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void Object::CopyFrom(const Object& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:cura.proto.Object)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool Object::IsInitialized() const {
  return true;
}

void Object::InternalSwap(Object* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  _impl_.settings_.InternalSwap(&other->_impl_.settings_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &_impl_.vertices_, lhs_arena,
      &other->_impl_.vertices_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &_impl_.normals_, lhs_arena,
      &other->_impl_.normals_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &_impl_.indices_, lhs_arena,
      &other->_impl_.indices_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &_impl_.name_, lhs_arena,
      &other->_impl_.name_, rhs_arena
  );
  swap(_impl_.id_, other->_impl_.id_);
}

::PROTOBUF_NAMESPACE_ID::Metadata Object::GetMetadata() const {
  return ::_pbi::AssignDescriptors(
      &descriptor_table_Cura_2eproto_getter, &descriptor_table_Cura_2eproto_once,
      file_level_metadata_Cura_2eproto[4]);
}

// ===================================================================

class Progress::_Internal {
 public:
};

Progress::Progress(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor(arena, is_message_owned);
  // @@protoc_insertion_point(arena_constructor:cura.proto.Progress)
}
Progress::Progress(const Progress& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  Progress* const _this = this; (void)_this;
  new (&_impl_) Impl_{
      decltype(_impl_.amount_){}
    , /*decltype(_impl_._cached_size_)*/{}};

  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  _this->_impl_.amount_ = from._impl_.amount_;
  // @@protoc_insertion_point(copy_constructor:cura.proto.Progress)
}

inline void Progress::SharedCtor(
    ::_pb::Arena* arena, bool is_message_owned) {
  (void)arena;
  (void)is_message_owned;
  new (&_impl_) Impl_{
      decltype(_impl_.amount_){0}
    , /*decltype(_impl_._cached_size_)*/{}
  };
}

Progress::~Progress() {
  // @@protoc_insertion_point(destructor:cura.proto.Progress)
  if (auto *arena = _internal_metadata_.DeleteReturnArena<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>()) {
  (void)arena;
    return;
  }
  SharedDtor();
}

inline void Progress::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void Progress::SetCachedSize(int size) const {
  _impl_._cached_size_.Set(size);
}

void Progress::Clear() {
// @@protoc_insertion_point(message_clear_start:cura.proto.Progress)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  _impl_.amount_ = 0;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* Progress::_InternalParse(const char* ptr, ::_pbi::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::_pbi::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // float amount = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 13)) {
          _impl_.amount_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* Progress::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:cura.proto.Progress)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // float amount = 1;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_amount = this->_internal_amount();
  uint32_t raw_amount;
  memcpy(&raw_amount, &tmp_amount, sizeof(tmp_amount));
  if (raw_amount != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteFloatToArray(1, this->_internal_amount(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::_pbi::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:cura.proto.Progress)
  return target;
}

size_t Progress::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:cura.proto.Progress)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // float amount = 1;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_amount = this->_internal_amount();
  uint32_t raw_amount;
  memcpy(&raw_amount, &tmp_amount, sizeof(tmp_amount));
  if (raw_amount != 0) {
    total_size += 1 + 4;
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_impl_._cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData Progress::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSourceCheck,
    Progress::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*Progress::GetClassData() const { return &_class_data_; }


void Progress::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg) {
  auto* const _this = static_cast<Progress*>(&to_msg);
  auto& from = static_cast<const Progress&>(from_msg);
  // @@protoc_insertion_point(class_specific_merge_from_start:cura.proto.Progress)
  GOOGLE_DCHECK_NE(&from, _this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_amount = from._internal_amount();
  uint32_t raw_amount;
  memcpy(&raw_amount, &tmp_amount, sizeof(tmp_amount));
  if (raw_amount != 0) {
    _this->_internal_set_amount(from._internal_amount());
  }
  _this->_internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void Progress::CopyFrom(const Progress& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:cura.proto.Progress)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool Progress::IsInitialized() const {
  return true;
}

void Progress::InternalSwap(Progress* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  swap(_impl_.amount_, other->_impl_.amount_);
}

::PROTOBUF_NAMESPACE_ID::Metadata Progress::GetMetadata() const {
  return ::_pbi::AssignDescriptors(
      &descriptor_table_Cura_2eproto_getter, &descriptor_table_Cura_2eproto_once,
      file_level_metadata_Cura_2eproto[5]);
}

// ===================================================================

class Layer::_Internal {
 public:
};

Layer::Layer(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor(arena, is_message_owned);
  // @@protoc_insertion_point(arena_constructor:cura.proto.Layer)
}
Layer::Layer(const Layer& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  Layer* const _this = this; (void)_this;
  new (&_impl_) Impl_{
      decltype(_impl_.polygons_){from._impl_.polygons_}
    , decltype(_impl_.id_){}
    , decltype(_impl_.height_){}
    , decltype(_impl_.thickness_){}
    , /*decltype(_impl_._cached_size_)*/{}};

  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::memcpy(&_impl_.id_, &from._impl_.id_,
    static_cast<size_t>(reinterpret_cast<char*>(&_impl_.thickness_) -
    reinterpret_cast<char*>(&_impl_.id_)) + sizeof(_impl_.thickness_));
  // @@protoc_insertion_point(copy_constructor:cura.proto.Layer)
}

inline void Layer::SharedCtor(
    ::_pb::Arena* arena, bool is_message_owned) {
  (void)arena;
  (void)is_message_owned;
  new (&_impl_) Impl_{
      decltype(_impl_.polygons_){arena}
    , decltype(_impl_.id_){0}
    , decltype(_impl_.height_){0}
    , decltype(_impl_.thickness_){0}
    , /*decltype(_impl_._cached_size_)*/{}
  };
}

Layer::~Layer() {
  // @@protoc_insertion_point(destructor:cura.proto.Layer)
  if (auto *arena = _internal_metadata_.DeleteReturnArena<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>()) {
  (void)arena;
    return;
  }
  SharedDtor();
}

inline void Layer::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  _impl_.polygons_.~RepeatedPtrField();
}

void Layer::SetCachedSize(int size) const {
  _impl_._cached_size_.Set(size);
}

void Layer::Clear() {
// @@protoc_insertion_point(message_clear_start:cura.proto.Layer)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  _impl_.polygons_.Clear();
  ::memset(&_impl_.id_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&_impl_.thickness_) -
      reinterpret_cast<char*>(&_impl_.id_)) + sizeof(_impl_.thickness_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* Layer::_InternalParse(const char* ptr, ::_pbi::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::_pbi::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // int32 id = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 8)) {
          _impl_.id_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // float height = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 21)) {
          _impl_.height_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else
          goto handle_unusual;
        continue;
      // float thickness = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 29)) {
          _impl_.thickness_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else
          goto handle_unusual;
        continue;
      // repeated .cura.proto.Polygon polygons = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 34)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_polygons(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<34>(ptr));
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* Layer::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:cura.proto.Layer)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // int32 id = 1;
  if (this->_internal_id() != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteInt32ToArray(1, this->_internal_id(), target);
  }

  // float height = 2;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_height = this->_internal_height();
  uint32_t raw_height;
  memcpy(&raw_height, &tmp_height, sizeof(tmp_height));
  if (raw_height != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteFloatToArray(2, this->_internal_height(), target);
  }

  // float thickness = 3;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_thickness = this->_internal_thickness();
  uint32_t raw_thickness;
  memcpy(&raw_thickness, &tmp_thickness, sizeof(tmp_thickness));
  if (raw_thickness != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteFloatToArray(3, this->_internal_thickness(), target);
  }

  // repeated .cura.proto.Polygon polygons = 4;
  for (unsigned i = 0,
      n = static_cast<unsigned>(this->_internal_polygons_size()); i < n; i++) {
    const auto& repfield = this->_internal_polygons(i);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
        InternalWriteMessage(4, repfield, repfield.GetCachedSize(), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::_pbi::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:cura.proto.Layer)
  return target;
}

size_t Layer::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:cura.proto.Layer)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated .cura.proto.Polygon polygons = 4;
  total_size += 1UL * this->_internal_polygons_size();
  for (const auto& msg : this->_impl_.polygons_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  // int32 id = 1;
  if (this->_internal_id() != 0) {
    total_size += ::_pbi::WireFormatLite::Int32SizePlusOne(this->_internal_id());
  }

  // float height = 2;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_height = this->_internal_height();
  uint32_t raw_height;
  memcpy(&raw_height, &tmp_height, sizeof(tmp_height));
  if (raw_height != 0) {
    total_size += 1 + 4;
  }

  // float thickness = 3;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_thickness = this->_internal_thickness();
  uint32_t raw_thickness;
  memcpy(&raw_thickness, &tmp_thickness, sizeof(tmp_thickness));
  if (raw_thickness != 0) {
    total_size += 1 + 4;
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_impl_._cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData Layer::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSourceCheck,
    Layer::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*Layer::GetClassData() const { return &_class_data_; }


void Layer::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg) {
  auto* const _this = static_cast<Layer*>(&to_msg);
  auto& from = static_cast<const Layer&>(from_msg);
  // @@protoc_insertion_point(class_specific_merge_from_start:cura.proto.Layer)
  GOOGLE_DCHECK_NE(&from, _this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  _this->_impl_.polygons_.MergeFrom(from._impl_.polygons_);
  if (from._internal_id() != 0) {
    _this->_internal_set_id(from._internal_id());
  }
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_height = from._internal_height();
  uint32_t raw_height;
  memcpy(&raw_height, &tmp_height, sizeof(tmp_height));
  if (raw_height != 0) {
    _this->_internal_set_height(from._internal_height());
  }
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_thickness = from._internal_thickness();
  uint32_t raw_thickness;
  memcpy(&raw_thickness, &tmp_thickness, sizeof(tmp_thickness));
  if (raw_thickness != 0) {
    _this->_internal_set_thickness(from._internal_thickness());
  }
  _this->_internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void Layer::CopyFrom(const Layer& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:cura.proto.Layer)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool Layer::IsInitialized() const {
  return true;
}

void Layer::InternalSwap(Layer* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  _impl_.polygons_.InternalSwap(&other->_impl_.polygons_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(Layer, _impl_.thickness_)
      + sizeof(Layer::_impl_.thickness_)
      - PROTOBUF_FIELD_OFFSET(Layer, _impl_.id_)>(
          reinterpret_cast<char*>(&_impl_.id_),
          reinterpret_cast<char*>(&other->_impl_.id_));
}

::PROTOBUF_NAMESPACE_ID::Metadata Layer::GetMetadata() const {
  return ::_pbi::AssignDescriptors(
      &descriptor_table_Cura_2eproto_getter, &descriptor_table_Cura_2eproto_once,
      file_level_metadata_Cura_2eproto[6]);
}

// ===================================================================

class Polygon::_Internal {
 public:
};

Polygon::Polygon(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor(arena, is_message_owned);
  // @@protoc_insertion_point(arena_constructor:cura.proto.Polygon)
}
Polygon::Polygon(const Polygon& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  Polygon* const _this = this; (void)_this;
  new (&_impl_) Impl_{
      decltype(_impl_.points_){}
    , decltype(_impl_.type_){}
    , decltype(_impl_.line_width_){}
    , decltype(_impl_.line_thickness_){}
    , decltype(_impl_.line_feedrate_){}
    , /*decltype(_impl_._cached_size_)*/{}};

  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  _impl_.points_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.points_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_points().empty()) {
    _this->_impl_.points_.Set(from._internal_points(), 
      _this->GetArenaForAllocation());
  }
  ::memcpy(&_impl_.type_, &from._impl_.type_,
    static_cast<size_t>(reinterpret_cast<char*>(&_impl_.line_feedrate_) -
    reinterpret_cast<char*>(&_impl_.type_)) + sizeof(_impl_.line_feedrate_));
  // @@protoc_insertion_point(copy_constructor:cura.proto.Polygon)
}

inline void Polygon::SharedCtor(
    ::_pb::Arena* arena, bool is_message_owned) {
  (void)arena;
  (void)is_message_owned;
  new (&_impl_) Impl_{
      decltype(_impl_.points_){}
    , decltype(_impl_.type_){0}
    , decltype(_impl_.line_width_){0}
    , decltype(_impl_.line_thickness_){0}
    , decltype(_impl_.line_feedrate_){0}
    , /*decltype(_impl_._cached_size_)*/{}
  };
  _impl_.points_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.points_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
}

Polygon::~Polygon() {
  // @@protoc_insertion_point(destructor:cura.proto.Polygon)
  if (auto *arena = _internal_metadata_.DeleteReturnArena<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>()) {
  (void)arena;
    return;
  }
  SharedDtor();
}

inline void Polygon::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  _impl_.points_.Destroy();
}

void Polygon::SetCachedSize(int size) const {
  _impl_._cached_size_.Set(size);
}

void Polygon::Clear() {
// @@protoc_insertion_point(message_clear_start:cura.proto.Polygon)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  _impl_.points_.ClearToEmpty();
  ::memset(&_impl_.type_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&_impl_.line_feedrate_) -
      reinterpret_cast<char*>(&_impl_.type_)) + sizeof(_impl_.line_feedrate_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* Polygon::_InternalParse(const char* ptr, ::_pbi::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::_pbi::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // .cura.proto.Polygon.Type type = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 8)) {
          uint64_t val = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
          _internal_set_type(static_cast<::cura::proto::Polygon_Type>(val));
        } else
          goto handle_unusual;
        continue;
      // bytes points = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          auto str = _internal_mutable_points();
          ptr = ::_pbi::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // float line_width = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 29)) {
          _impl_.line_width_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else
          goto handle_unusual;
        continue;
      // float line_thickness = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 37)) {
          _impl_.line_thickness_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else
          goto handle_unusual;
        continue;
      // float line_feedrate = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 45)) {
          _impl_.line_feedrate_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* Polygon::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:cura.proto.Polygon)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // .cura.proto.Polygon.Type type = 1;
  if (this->_internal_type() != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteEnumToArray(
      1, this->_internal_type(), target);
  }

  // bytes points = 2;
  if (!this->_internal_points().empty()) {
    target = stream->WriteBytesMaybeAliased(
        2, this->_internal_points(), target);
  }

  // float line_width = 3;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_line_width = this->_internal_line_width();
  uint32_t raw_line_width;
  memcpy(&raw_line_width, &tmp_line_width, sizeof(tmp_line_width));
  if (raw_line_width != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteFloatToArray(3, this->_internal_line_width(), target);
  }

  // float line_thickness = 4;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_line_thickness = this->_internal_line_thickness();
  uint32_t raw_line_thickness;
  memcpy(&raw_line_thickness, &tmp_line_thickness, sizeof(tmp_line_thickness));
  if (raw_line_thickness != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteFloatToArray(4, this->_internal_line_thickness(), target);
  }

  // float line_feedrate = 5;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_line_feedrate = this->_internal_line_feedrate();
  uint32_t raw_line_feedrate;
  memcpy(&raw_line_feedrate, &tmp_line_feedrate, sizeof(tmp_line_feedrate));
  if (raw_line_feedrate != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteFloatToArray(5, this->_internal_line_feedrate(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::_pbi::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:cura.proto.Polygon)
  return target;
}

size_t Polygon::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:cura.proto.Polygon)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // bytes points = 2;
  if (!this->_internal_points().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::BytesSize(
        this->_internal_points());
  }

  // .cura.proto.Polygon.Type type = 1;
  if (this->_internal_type() != 0) {
    total_size += 1 +
      ::_pbi::WireFormatLite::EnumSize(this->_internal_type());
  }

  // float line_width = 3;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_line_width = this->_internal_line_width();
  uint32_t raw_line_width;
  memcpy(&raw_line_width, &tmp_line_width, sizeof(tmp_line_width));
  if (raw_line_width != 0) {
    total_size += 1 + 4;
  }

  // float line_thickness = 4;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_line_thickness = this->_internal_line_thickness();
  uint32_t raw_line_thickness;
  memcpy(&raw_line_thickness, &tmp_line_thickness, sizeof(tmp_line_thickness));
  if (raw_line_thickness != 0) {
    total_size += 1 + 4;
  }

  // float line_feedrate = 5;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_line_feedrate = this->_internal_line_feedrate();
  uint32_t raw_line_feedrate;
  memcpy(&raw_line_feedrate, &tmp_line_feedrate, sizeof(tmp_line_feedrate));
  if (raw_line_feedrate != 0) {
    total_size += 1 + 4;
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_impl_._cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData Polygon::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSourceCheck,
    Polygon::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*Polygon::GetClassData() const { return &_class_data_; }


void Polygon::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg) {
  auto* const _this = static_cast<Polygon*>(&to_msg);
  auto& from = static_cast<const Polygon&>(from_msg);
  // @@protoc_insertion_point(class_specific_merge_from_start:cura.proto.Polygon)
  GOOGLE_DCHECK_NE(&from, _this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (!from._internal_points().empty()) {
    _this->_internal_set_points(from._internal_points());
  }
  if (from._internal_type() != 0) {
    _this->_internal_set_type(from._internal_type());
  }
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_line_width = from._internal_line_width();
  uint32_t raw_line_width;
  memcpy(&raw_line_width, &tmp_line_width, sizeof(tmp_line_width));
  if (raw_line_width != 0) {
    _this->_internal_set_line_width(from._internal_line_width());
  }
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_line_thickness = from._internal_line_thickness();
  uint32_t raw_line_thickness;
  memcpy(&raw_line_thickness, &tmp_line_thickness, sizeof(tmp_line_thickness));
  if (raw_line_thickness != 0) {
    _this->_internal_set_line_thickness(from._internal_line_thickness());
  }
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_line_feedrate = from._internal_line_feedrate();
  uint32_t raw_line_feedrate;
  memcpy(&raw_line_feedrate, &tmp_line_feedrate, sizeof(tmp_line_feedrate));
  if (raw_line_feedrate != 0) {
    _this->_internal_set_line_feedrate(from._internal_line_feedrate());
  }
  _this->_internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void Polygon::CopyFrom(const Polygon& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:cura.proto.Polygon)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool Polygon::IsInitialized() const {
  return true;
}

void Polygon::InternalSwap(Polygon* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &_impl_.points_, lhs_arena,
      &other->_impl_.points_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(Polygon, _impl_.line_feedrate_)
      + sizeof(Polygon::_impl_.line_feedrate_)
      - PROTOBUF_FIELD_OFFSET(Polygon, _impl_.type_)>(
          reinterpret_cast<char*>(&_impl_.type_),
          reinterpret_cast<char*>(&other->_impl_.type_));
}

::PROTOBUF_NAMESPACE_ID::Metadata Polygon::GetMetadata() const {
  return ::_pbi::AssignDescriptors(
      &descriptor_table_Cura_2eproto_getter, &descriptor_table_Cura_2eproto_once,
      file_level_metadata_Cura_2eproto[7]);
}

// ===================================================================

class LayerOptimized::_Internal {
 public:
};

LayerOptimized::LayerOptimized(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor(arena, is_message_owned);
  // @@protoc_insertion_point(arena_constructor:cura.proto.LayerOptimized)
}
LayerOptimized::LayerOptimized(const LayerOptimized& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  LayerOptimized* const _this = this; (void)_this;
  new (&_impl_) Impl_{
      decltype(_impl_.path_segment_){from._impl_.path_segment_}
    , decltype(_impl_.id_){}
    , decltype(_impl_.height_){}
    , decltype(_impl_.thickness_){}
    , /*decltype(_impl_._cached_size_)*/{}};

  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::memcpy(&_impl_.id_, &from._impl_.id_,
    static_cast<size_t>(reinterpret_cast<char*>(&_impl_.thickness_) -
    reinterpret_cast<char*>(&_impl_.id_)) + sizeof(_impl_.thickness_));
  // @@protoc_insertion_point(copy_constructor:cura.proto.LayerOptimized)
}

inline void LayerOptimized::SharedCtor(
    ::_pb::Arena* arena, bool is_message_owned) {
  (void)arena;
  (void)is_message_owned;
  new (&_impl_) Impl_{
      decltype(_impl_.path_segment_){arena}
    , decltype(_impl_.id_){0}
    , decltype(_impl_.height_){0}
    , decltype(_impl_.thickness_){0}
    , /*decltype(_impl_._cached_size_)*/{}
  };
}

LayerOptimized::~LayerOptimized() {
  // @@protoc_insertion_point(destructor:cura.proto.LayerOptimized)
  if (auto *arena = _internal_metadata_.DeleteReturnArena<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>()) {
  (void)arena;
    return;
  }
  SharedDtor();
}

inline void LayerOptimized::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  _impl_.path_segment_.~RepeatedPtrField();
}

void LayerOptimized::SetCachedSize(int size) const {
  _impl_._cached_size_.Set(size);
}

void LayerOptimized::Clear() {
// @@protoc_insertion_point(message_clear_start:cura.proto.LayerOptimized)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  _impl_.path_segment_.Clear();
  ::memset(&_impl_.id_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&_impl_.thickness_) -
      reinterpret_cast<char*>(&_impl_.id_)) + sizeof(_impl_.thickness_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* LayerOptimized::_InternalParse(const char* ptr, ::_pbi::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::_pbi::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // int32 id = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 8)) {
          _impl_.id_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // float height = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 21)) {
          _impl_.height_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else
          goto handle_unusual;
        continue;
      // float thickness = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 29)) {
          _impl_.thickness_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else
          goto handle_unusual;
        continue;
      // repeated .cura.proto.PathSegment path_segment = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 34)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_path_segment(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<34>(ptr));
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* LayerOptimized::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:cura.proto.LayerOptimized)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // int32 id = 1;
  if (this->_internal_id() != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteInt32ToArray(1, this->_internal_id(), target);
  }

  // float height = 2;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_height = this->_internal_height();
  uint32_t raw_height;
  memcpy(&raw_height, &tmp_height, sizeof(tmp_height));
  if (raw_height != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteFloatToArray(2, this->_internal_height(), target);
  }

  // float thickness = 3;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_thickness = this->_internal_thickness();
  uint32_t raw_thickness;
  memcpy(&raw_thickness, &tmp_thickness, sizeof(tmp_thickness));
  if (raw_thickness != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteFloatToArray(3, this->_internal_thickness(), target);
  }

  // repeated .cura.proto.PathSegment path_segment = 4;
  for (unsigned i = 0,
      n = static_cast<unsigned>(this->_internal_path_segment_size()); i < n; i++) {
    const auto& repfield = this->_internal_path_segment(i);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
        InternalWriteMessage(4, repfield, repfield.GetCachedSize(), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::_pbi::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:cura.proto.LayerOptimized)
  return target;
}

size_t LayerOptimized::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:cura.proto.LayerOptimized)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated .cura.proto.PathSegment path_segment = 4;
  total_size += 1UL * this->_internal_path_segment_size();
  for (const auto& msg : this->_impl_.path_segment_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  // int32 id = 1;
  if (this->_internal_id() != 0) {
    total_size += ::_pbi::WireFormatLite::Int32SizePlusOne(this->_internal_id());
  }

  // float height = 2;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_height = this->_internal_height();
  uint32_t raw_height;
  memcpy(&raw_height, &tmp_height, sizeof(tmp_height));
  if (raw_height != 0) {
    total_size += 1 + 4;
  }

  // float thickness = 3;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_thickness = this->_internal_thickness();
  uint32_t raw_thickness;
  memcpy(&raw_thickness, &tmp_thickness, sizeof(tmp_thickness));
  if (raw_thickness != 0) {
    total_size += 1 + 4;
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_impl_._cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData LayerOptimized::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSourceCheck,
    LayerOptimized::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*LayerOptimized::GetClassData() const { return &_class_data_; }


void LayerOptimized::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg) {
  auto* const _this = static_cast<LayerOptimized*>(&to_msg);
  auto& from = static_cast<const LayerOptimized&>(from_msg);
  // @@protoc_insertion_point(class_specific_merge_from_start:cura.proto.LayerOptimized)
  GOOGLE_DCHECK_NE(&from, _this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  _this->_impl_.path_segment_.MergeFrom(from._impl_.path_segment_);
  if (from._internal_id() != 0) {
    _this->_internal_set_id(from._internal_id());
  }
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_height = from._internal_height();
  uint32_t raw_height;
  memcpy(&raw_height, &tmp_height, sizeof(tmp_height));
  if (raw_height != 0) {
    _this->_internal_set_height(from._internal_height());
  }
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_thickness = from._internal_thickness();
  uint32_t raw_thickness;
  memcpy(&raw_thickness, &tmp_thickness, sizeof(tmp_thickness));
  if (raw_thickness != 0) {
    _this->_internal_set_thickness(from._internal_thickness());
  }
  _this->_internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void LayerOptimized::CopyFrom(const LayerOptimized& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:cura.proto.LayerOptimized)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool LayerOptimized::IsInitialized() const {
  return true;
}

void LayerOptimized::InternalSwap(LayerOptimized* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  _impl_.path_segment_.InternalSwap(&other->_impl_.path_segment_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(LayerOptimized, _impl_.thickness_)
      + sizeof(LayerOptimized::_impl_.thickness_)
      - PROTOBUF_FIELD_OFFSET(LayerOptimized, _impl_.id_)>(
          reinterpret_cast<char*>(&_impl_.id_),
          reinterpret_cast<char*>(&other->_impl_.id_));
}

::PROTOBUF_NAMESPACE_ID::Metadata LayerOptimized::GetMetadata() const {
  return ::_pbi::AssignDescriptors(
      &descriptor_table_Cura_2eproto_getter, &descriptor_table_Cura_2eproto_once,
      file_level_metadata_Cura_2eproto[8]);
}

// ===================================================================

class PathSegment::_Internal {
 public:
};

PathSegment::PathSegment(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor(arena, is_message_owned);
  // @@protoc_insertion_point(arena_constructor:cura.proto.PathSegment)
}
PathSegment::PathSegment(const PathSegment& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  PathSegment* const _this = this; (void)_this;
  new (&_impl_) Impl_{
      decltype(_impl_.points_){}
    , decltype(_impl_.line_type_){}
    , decltype(_impl_.line_width_){}
    , decltype(_impl_.line_thickness_){}
    , decltype(_impl_.line_feedrate_){}
    , decltype(_impl_.extruder_){}
    , decltype(_impl_.point_type_){}
    , /*decltype(_impl_._cached_size_)*/{}};

  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  _impl_.points_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.points_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_points().empty()) {
    _this->_impl_.points_.Set(from._internal_points(), 
      _this->GetArenaForAllocation());
  }
  _impl_.line_type_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.line_type_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_line_type().empty()) {
    _this->_impl_.line_type_.Set(from._internal_line_type(), 
      _this->GetArenaForAllocation());
  }
  _impl_.line_width_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.line_width_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_line_width().empty()) {
    _this->_impl_.line_width_.Set(from._internal_line_width(), 
      _this->GetArenaForAllocation());
  }
  _impl_.line_thickness_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.line_thickness_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_line_thickness().empty()) {
    _this->_impl_.line_thickness_.Set(from._internal_line_thickness(), 
      _this->GetArenaForAllocation());
  }
  _impl_.line_feedrate_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.line_feedrate_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_line_feedrate().empty()) {
    _this->_impl_.line_feedrate_.Set(from._internal_line_feedrate(), 
      _this->GetArenaForAllocation());
  }
  ::memcpy(&_impl_.extruder_, &from._impl_.extruder_,
    static_cast<size_t>(reinterpret_cast<char*>(&_impl_.point_type_) -
    reinterpret_cast<char*>(&_impl_.extruder_)) + sizeof(_impl_.point_type_));
  // @@protoc_insertion_point(copy_constructor:cura.proto.PathSegment)
}

inline void PathSegment::SharedCtor(
    ::_pb::Arena* arena, bool is_message_owned) {
  (void)arena;
  (void)is_message_owned;
  new (&_impl_) Impl_{
      decltype(_impl_.points_){}
    , decltype(_impl_.line_type_){}
    , decltype(_impl_.line_width_){}
    , decltype(_impl_.line_thickness_){}
    , decltype(_impl_.line_feedrate_){}
    , decltype(_impl_.extruder_){0}
    , decltype(_impl_.point_type_){0}
    , /*decltype(_impl_._cached_size_)*/{}
  };
  _impl_.points_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.points_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  _impl_.line_type_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.line_type_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  _impl_.line_width_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.line_width_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  _impl_.line_thickness_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.line_thickness_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  _impl_.line_feedrate_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.line_feedrate_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
}

PathSegment::~PathSegment() {
  // @@protoc_insertion_point(destructor:cura.proto.PathSegment)
  if (auto *arena = _internal_metadata_.DeleteReturnArena<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>()) {
  (void)arena;
    return;
  }
  SharedDtor();
}

inline void PathSegment::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  _impl_.points_.Destroy();
  _impl_.line_type_.Destroy();
  _impl_.line_width_.Destroy();
  _impl_.line_thickness_.Destroy();
  _impl_.line_feedrate_.Destroy();
}

void PathSegment::SetCachedSize(int size) const {
  _impl_._cached_size_.Set(size);
}

void PathSegment::Clear() {
// @@protoc_insertion_point(message_clear_start:cura.proto.PathSegment)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  _impl_.points_.ClearToEmpty();
  _impl_.line_type_.ClearToEmpty();
  _impl_.line_width_.ClearToEmpty();
  _impl_.line_thickness_.ClearToEmpty();
  _impl_.line_feedrate_.ClearToEmpty();
  ::memset(&_impl_.extruder_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&_impl_.point_type_) -
      reinterpret_cast<char*>(&_impl_.extruder_)) + sizeof(_impl_.point_type_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* PathSegment::_InternalParse(const char* ptr, ::_pbi::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::_pbi::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // int32 extruder = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 8)) {
          _impl_.extruder_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .cura.proto.PathSegment.PointType point_type = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 16)) {
          uint64_t val = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
          _internal_set_point_type(static_cast<::cura::proto::PathSegment_PointType>(val));
        } else
          goto handle_unusual;
        continue;
      // bytes points = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 26)) {
          auto str = _internal_mutable_points();
          ptr = ::_pbi::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // bytes line_type = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 34)) {
          auto str = _internal_mutable_line_type();
          ptr = ::_pbi::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // bytes line_width = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 42)) {
          auto str = _internal_mutable_line_width();
          ptr = ::_pbi::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // bytes line_thickness = 6;
      case 6:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 50)) {
          auto str = _internal_mutable_line_thickness();
          ptr = ::_pbi::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // bytes line_feedrate = 7;
      case 7:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 58)) {
          auto str = _internal_mutable_line_feedrate();
          ptr = ::_pbi::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* PathSegment::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:cura.proto.PathSegment)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // int32 extruder = 1;
  if (this->_internal_extruder() != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteInt32ToArray(1, this->_internal_extruder(), target);
  }

  // .cura.proto.PathSegment.PointType point_type = 2;
  if (this->_internal_point_type() != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteEnumToArray(
      2, this->_internal_point_type(), target);
  }

  // bytes points = 3;
  if (!this->_internal_points().empty()) {
    target = stream->WriteBytesMaybeAliased(
        3, this->_internal_points(), target);
  }

  // bytes line_type = 4;
  if (!this->_internal_line_type().empty()) {
    target = stream->WriteBytesMaybeAliased(
        4, this->_internal_line_type(), target);
  }

  // bytes line_width = 5;
  if (!this->_internal_line_width().empty()) {
    target = stream->WriteBytesMaybeAliased(
        5, this->_internal_line_width(), target);
  }

  // bytes line_thickness = 6;
  if (!this->_internal_line_thickness().empty()) {
    target = stream->WriteBytesMaybeAliased(
        6, this->_internal_line_thickness(), target);
  }

  // bytes line_feedrate = 7;
  if (!this->_internal_line_feedrate().empty()) {
    target = stream->WriteBytesMaybeAliased(
        7, this->_internal_line_feedrate(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::_pbi::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:cura.proto.PathSegment)
  return target;
}

size_t PathSegment::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:cura.proto.PathSegment)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // bytes points = 3;
  if (!this->_internal_points().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::BytesSize(
        this->_internal_points());
  }

  // bytes line_type = 4;
  if (!this->_internal_line_type().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::BytesSize(
        this->_internal_line_type());
  }

  // bytes line_width = 5;
  if (!this->_internal_line_width().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::BytesSize(
        this->_internal_line_width());
  }

  // bytes line_thickness = 6;
  if (!this->_internal_line_thickness().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::BytesSize(
        this->_internal_line_thickness());
  }

  // bytes line_feedrate = 7;
  if (!this->_internal_line_feedrate().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::BytesSize(
        this->_internal_line_feedrate());
  }

  // int32 extruder = 1;
  if (this->_internal_extruder() != 0) {
    total_size += ::_pbi::WireFormatLite::Int32SizePlusOne(this->_internal_extruder());
  }

  // .cura.proto.PathSegment.PointType point_type = 2;
  if (this->_internal_point_type() != 0) {
    total_size += 1 +
      ::_pbi::WireFormatLite::EnumSize(this->_internal_point_type());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_impl_._cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData PathSegment::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSourceCheck,
    PathSegment::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*PathSegment::GetClassData() const { return &_class_data_; }


void PathSegment::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg) {
  auto* const _this = static_cast<PathSegment*>(&to_msg);
  auto& from = static_cast<const PathSegment&>(from_msg);
  // @@protoc_insertion_point(class_specific_merge_from_start:cura.proto.PathSegment)
  GOOGLE_DCHECK_NE(&from, _this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (!from._internal_points().empty()) {
    _this->_internal_set_points(from._internal_points());
  }
  if (!from._internal_line_type().empty()) {
    _this->_internal_set_line_type(from._internal_line_type());
  }
  if (!from._internal_line_width().empty()) {
    _this->_internal_set_line_width(from._internal_line_width());
  }
  if (!from._internal_line_thickness().empty()) {
    _this->_internal_set_line_thickness(from._internal_line_thickness());
  }
  if (!from._internal_line_feedrate().empty()) {
    _this->_internal_set_line_feedrate(from._internal_line_feedrate());
  }
  if (from._internal_extruder() != 0) {
    _this->_internal_set_extruder(from._internal_extruder());
  }
  if (from._internal_point_type() != 0) {
    _this->_internal_set_point_type(from._internal_point_type());
  }
  _this->_internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void PathSegment::CopyFrom(const PathSegment& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:cura.proto.PathSegment)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool PathSegment::IsInitialized() const {
  return true;
}

void PathSegment::InternalSwap(PathSegment* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &_impl_.points_, lhs_arena,
      &other->_impl_.points_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &_impl_.line_type_, lhs_arena,
      &other->_impl_.line_type_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &_impl_.line_width_, lhs_arena,
      &other->_impl_.line_width_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &_impl_.line_thickness_, lhs_arena,
      &other->_impl_.line_thickness_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &_impl_.line_feedrate_, lhs_arena,
      &other->_impl_.line_feedrate_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(PathSegment, _impl_.point_type_)
      + sizeof(PathSegment::_impl_.point_type_)
      - PROTOBUF_FIELD_OFFSET(PathSegment, _impl_.extruder_)>(
          reinterpret_cast<char*>(&_impl_.extruder_),
          reinterpret_cast<char*>(&other->_impl_.extruder_));
}

::PROTOBUF_NAMESPACE_ID::Metadata PathSegment::GetMetadata() const {
  return ::_pbi::AssignDescriptors(
      &descriptor_table_Cura_2eproto_getter, &descriptor_table_Cura_2eproto_once,
      file_level_metadata_Cura_2eproto[9]);
}

// ===================================================================

class GCodeLayer::_Internal {
 public:
};

GCodeLayer::GCodeLayer(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor(arena, is_message_owned);
  // @@protoc_insertion_point(arena_constructor:cura.proto.GCodeLayer)
}
GCodeLayer::GCodeLayer(const GCodeLayer& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  GCodeLayer* const _this = this; (void)_this;
  new (&_impl_) Impl_{
      decltype(_impl_.data_){}
    , /*decltype(_impl_._cached_size_)*/{}};

  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  _impl_.data_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.data_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_data().empty()) {
    _this->_impl_.data_.Set(from._internal_data(), 
      _this->GetArenaForAllocation());
  }
  // @@protoc_insertion_point(copy_constructor:cura.proto.GCodeLayer)
}

inline void GCodeLayer::SharedCtor(
    ::_pb::Arena* arena, bool is_message_owned) {
  (void)arena;
  (void)is_message_owned;
  new (&_impl_) Impl_{
      decltype(_impl_.data_){}
    , /*decltype(_impl_._cached_size_)*/{}
  };
  _impl_.data_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.data_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
}

GCodeLayer::~GCodeLayer() {
  // @@protoc_insertion_point(destructor:cura.proto.GCodeLayer)
  if (auto *arena = _internal_metadata_.DeleteReturnArena<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>()) {
  (void)arena;
    return;
  }
  SharedDtor();
}

inline void GCodeLayer::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  _impl_.data_.Destroy();
}

void GCodeLayer::SetCachedSize(int size) const {
  _impl_._cached_size_.Set(size);
}

void GCodeLayer::Clear() {
// @@protoc_insertion_point(message_clear_start:cura.proto.GCodeLayer)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  _impl_.data_.ClearToEmpty();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* GCodeLayer::_InternalParse(const char* ptr, ::_pbi::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::_pbi::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // bytes data = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          auto str = _internal_mutable_data();
          ptr = ::_pbi::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* GCodeLayer::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:cura.proto.GCodeLayer)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // bytes data = 2;
  if (!this->_internal_data().empty()) {
    target = stream->WriteBytesMaybeAliased(
        2, this->_internal_data(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::_pbi::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:cura.proto.GCodeLayer)
  return target;
}

size_t GCodeLayer::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:cura.proto.GCodeLayer)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // bytes data = 2;
  if (!this->_internal_data().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::BytesSize(
        this->_internal_data());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_impl_._cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData GCodeLayer::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSourceCheck,
    GCodeLayer::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GCodeLayer::GetClassData() const { return &_class_data_; }


void GCodeLayer::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg) {
  auto* const _this = static_cast<GCodeLayer*>(&to_msg);
  auto& from = static_cast<const GCodeLayer&>(from_msg);
  // @@protoc_insertion_point(class_specific_merge_from_start:cura.proto.GCodeLayer)
  GOOGLE_DCHECK_NE(&from, _this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (!from._internal_data().empty()) {
    _this->_internal_set_data(from._internal_data());
  }
  _this->_internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void GCodeLayer::CopyFrom(const GCodeLayer& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:cura.proto.GCodeLayer)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool GCodeLayer::IsInitialized() const {
  return true;
}

void GCodeLayer::InternalSwap(GCodeLayer* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &_impl_.data_, lhs_arena,
      &other->_impl_.data_, rhs_arena
  );
}

::PROTOBUF_NAMESPACE_ID::Metadata GCodeLayer::GetMetadata() const {
  return ::_pbi::AssignDescriptors(
      &descriptor_table_Cura_2eproto_getter, &descriptor_table_Cura_2eproto_once,
      file_level_metadata_Cura_2eproto[10]);
}

// ===================================================================

class PrintTimeMaterialEstimates::_Internal {
 public:
};

PrintTimeMaterialEstimates::PrintTimeMaterialEstimates(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor(arena, is_message_owned);
  // @@protoc_insertion_point(arena_constructor:cura.proto.PrintTimeMaterialEstimates)
}
PrintTimeMaterialEstimates::PrintTimeMaterialEstimates(const PrintTimeMaterialEstimates& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  PrintTimeMaterialEstimates* const _this = this; (void)_this;
  new (&_impl_) Impl_{
      decltype(_impl_.materialestimates_){from._impl_.materialestimates_}
    , decltype(_impl_.time_none_){}
    , decltype(_impl_.time_inset_0_){}
    , decltype(_impl_.time_inset_x_){}
    , decltype(_impl_.time_skin_){}
    , decltype(_impl_.time_support_){}
    , decltype(_impl_.time_skirt_){}
    , decltype(_impl_.time_infill_){}
    , decltype(_impl_.time_support_infill_){}
    , decltype(_impl_.time_travel_){}
    , decltype(_impl_.time_retract_){}
    , decltype(_impl_.time_support_interface_){}
    , decltype(_impl_.time_prime_tower_){}
    , /*decltype(_impl_._cached_size_)*/{}};

  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::memcpy(&_impl_.time_none_, &from._impl_.time_none_,
    static_cast<size_t>(reinterpret_cast<char*>(&_impl_.time_prime_tower_) -
    reinterpret_cast<char*>(&_impl_.time_none_)) + sizeof(_impl_.time_prime_tower_));
  // @@protoc_insertion_point(copy_constructor:cura.proto.PrintTimeMaterialEstimates)
}

inline void PrintTimeMaterialEstimates::SharedCtor(
    ::_pb::Arena* arena, bool is_message_owned) {
  (void)arena;
  (void)is_message_owned;
  new (&_impl_) Impl_{
      decltype(_impl_.materialestimates_){arena}
    , decltype(_impl_.time_none_){0}
    , decltype(_impl_.time_inset_0_){0}
    , decltype(_impl_.time_inset_x_){0}
    , decltype(_impl_.time_skin_){0}
    , decltype(_impl_.time_support_){0}
    , decltype(_impl_.time_skirt_){0}
    , decltype(_impl_.time_infill_){0}
    , decltype(_impl_.time_support_infill_){0}
    , decltype(_impl_.time_travel_){0}
    , decltype(_impl_.time_retract_){0}
    , decltype(_impl_.time_support_interface_){0}
    , decltype(_impl_.time_prime_tower_){0}
    , /*decltype(_impl_._cached_size_)*/{}
  };
}

PrintTimeMaterialEstimates::~PrintTimeMaterialEstimates() {
  // @@protoc_insertion_point(destructor:cura.proto.PrintTimeMaterialEstimates)
  if (auto *arena = _internal_metadata_.DeleteReturnArena<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>()) {
  (void)arena;
    return;
  }
  SharedDtor();
}

inline void PrintTimeMaterialEstimates::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  _impl_.materialestimates_.~RepeatedPtrField();
}

void PrintTimeMaterialEstimates::SetCachedSize(int size) const {
  _impl_._cached_size_.Set(size);
}

void PrintTimeMaterialEstimates::Clear() {
// @@protoc_insertion_point(message_clear_start:cura.proto.PrintTimeMaterialEstimates)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  _impl_.materialestimates_.Clear();
  ::memset(&_impl_.time_none_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&_impl_.time_prime_tower_) -
      reinterpret_cast<char*>(&_impl_.time_none_)) + sizeof(_impl_.time_prime_tower_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* PrintTimeMaterialEstimates::_InternalParse(const char* ptr, ::_pbi::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::_pbi::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // float time_none = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 13)) {
          _impl_.time_none_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else
          goto handle_unusual;
        continue;
      // float time_inset_0 = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 21)) {
          _impl_.time_inset_0_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else
          goto handle_unusual;
        continue;
      // float time_inset_x = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 29)) {
          _impl_.time_inset_x_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else
          goto handle_unusual;
        continue;
      // float time_skin = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 37)) {
          _impl_.time_skin_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else
          goto handle_unusual;
        continue;
      // float time_support = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 45)) {
          _impl_.time_support_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else
          goto handle_unusual;
        continue;
      // float time_skirt = 6;
      case 6:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 53)) {
          _impl_.time_skirt_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else
          goto handle_unusual;
        continue;
      // float time_infill = 7;
      case 7:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 61)) {
          _impl_.time_infill_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else
          goto handle_unusual;
        continue;
      // float time_support_infill = 8;
      case 8:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 69)) {
          _impl_.time_support_infill_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else
          goto handle_unusual;
        continue;
      // float time_travel = 9;
      case 9:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 77)) {
          _impl_.time_travel_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else
          goto handle_unusual;
        continue;
      // float time_retract = 10;
      case 10:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 85)) {
          _impl_.time_retract_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else
          goto handle_unusual;
        continue;
      // float time_support_interface = 11;
      case 11:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 93)) {
          _impl_.time_support_interface_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else
          goto handle_unusual;
        continue;
      // float time_prime_tower = 12;
      case 12:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 101)) {
          _impl_.time_prime_tower_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else
          goto handle_unusual;
        continue;
      // repeated .cura.proto.MaterialEstimates materialEstimates = 13;
      case 13:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 106)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_materialestimates(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<106>(ptr));
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* PrintTimeMaterialEstimates::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:cura.proto.PrintTimeMaterialEstimates)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // float time_none = 1;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_time_none = this->_internal_time_none();
  uint32_t raw_time_none;
  memcpy(&raw_time_none, &tmp_time_none, sizeof(tmp_time_none));
  if (raw_time_none != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteFloatToArray(1, this->_internal_time_none(), target);
  }

  // float time_inset_0 = 2;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_time_inset_0 = this->_internal_time_inset_0();
  uint32_t raw_time_inset_0;
  memcpy(&raw_time_inset_0, &tmp_time_inset_0, sizeof(tmp_time_inset_0));
  if (raw_time_inset_0 != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteFloatToArray(2, this->_internal_time_inset_0(), target);
  }

  // float time_inset_x = 3;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_time_inset_x = this->_internal_time_inset_x();
  uint32_t raw_time_inset_x;
  memcpy(&raw_time_inset_x, &tmp_time_inset_x, sizeof(tmp_time_inset_x));
  if (raw_time_inset_x != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteFloatToArray(3, this->_internal_time_inset_x(), target);
  }

  // float time_skin = 4;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_time_skin = this->_internal_time_skin();
  uint32_t raw_time_skin;
  memcpy(&raw_time_skin, &tmp_time_skin, sizeof(tmp_time_skin));
  if (raw_time_skin != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteFloatToArray(4, this->_internal_time_skin(), target);
  }

  // float time_support = 5;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_time_support = this->_internal_time_support();
  uint32_t raw_time_support;
  memcpy(&raw_time_support, &tmp_time_support, sizeof(tmp_time_support));
  if (raw_time_support != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteFloatToArray(5, this->_internal_time_support(), target);
  }

  // float time_skirt = 6;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_time_skirt = this->_internal_time_skirt();
  uint32_t raw_time_skirt;
  memcpy(&raw_time_skirt, &tmp_time_skirt, sizeof(tmp_time_skirt));
  if (raw_time_skirt != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteFloatToArray(6, this->_internal_time_skirt(), target);
  }

  // float time_infill = 7;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_time_infill = this->_internal_time_infill();
  uint32_t raw_time_infill;
  memcpy(&raw_time_infill, &tmp_time_infill, sizeof(tmp_time_infill));
  if (raw_time_infill != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteFloatToArray(7, this->_internal_time_infill(), target);
  }

  // float time_support_infill = 8;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_time_support_infill = this->_internal_time_support_infill();
  uint32_t raw_time_support_infill;
  memcpy(&raw_time_support_infill, &tmp_time_support_infill, sizeof(tmp_time_support_infill));
  if (raw_time_support_infill != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteFloatToArray(8, this->_internal_time_support_infill(), target);
  }

  // float time_travel = 9;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_time_travel = this->_internal_time_travel();
  uint32_t raw_time_travel;
  memcpy(&raw_time_travel, &tmp_time_travel, sizeof(tmp_time_travel));
  if (raw_time_travel != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteFloatToArray(9, this->_internal_time_travel(), target);
  }

  // float time_retract = 10;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_time_retract = this->_internal_time_retract();
  uint32_t raw_time_retract;
  memcpy(&raw_time_retract, &tmp_time_retract, sizeof(tmp_time_retract));
  if (raw_time_retract != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteFloatToArray(10, this->_internal_time_retract(), target);
  }

  // float time_support_interface = 11;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_time_support_interface = this->_internal_time_support_interface();
  uint32_t raw_time_support_interface;
  memcpy(&raw_time_support_interface, &tmp_time_support_interface, sizeof(tmp_time_support_interface));
  if (raw_time_support_interface != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteFloatToArray(11, this->_internal_time_support_interface(), target);
  }

  // float time_prime_tower = 12;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_time_prime_tower = this->_internal_time_prime_tower();
  uint32_t raw_time_prime_tower;
  memcpy(&raw_time_prime_tower, &tmp_time_prime_tower, sizeof(tmp_time_prime_tower));
  if (raw_time_prime_tower != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteFloatToArray(12, this->_internal_time_prime_tower(), target);
  }

  // repeated .cura.proto.MaterialEstimates materialEstimates = 13;
  for (unsigned i = 0,
      n = static_cast<unsigned>(this->_internal_materialestimates_size()); i < n; i++) {
    const auto& repfield = this->_internal_materialestimates(i);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
        InternalWriteMessage(13, repfield, repfield.GetCachedSize(), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::_pbi::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:cura.proto.PrintTimeMaterialEstimates)
  return target;
}

size_t PrintTimeMaterialEstimates::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:cura.proto.PrintTimeMaterialEstimates)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated .cura.proto.MaterialEstimates materialEstimates = 13;
  total_size += 1UL * this->_internal_materialestimates_size();
  for (const auto& msg : this->_impl_.materialestimates_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  // float time_none = 1;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_time_none = this->_internal_time_none();
  uint32_t raw_time_none;
  memcpy(&raw_time_none, &tmp_time_none, sizeof(tmp_time_none));
  if (raw_time_none != 0) {
    total_size += 1 + 4;
  }

  // float time_inset_0 = 2;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_time_inset_0 = this->_internal_time_inset_0();
  uint32_t raw_time_inset_0;
  memcpy(&raw_time_inset_0, &tmp_time_inset_0, sizeof(tmp_time_inset_0));
  if (raw_time_inset_0 != 0) {
    total_size += 1 + 4;
  }

  // float time_inset_x = 3;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_time_inset_x = this->_internal_time_inset_x();
  uint32_t raw_time_inset_x;
  memcpy(&raw_time_inset_x, &tmp_time_inset_x, sizeof(tmp_time_inset_x));
  if (raw_time_inset_x != 0) {
    total_size += 1 + 4;
  }

  // float time_skin = 4;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_time_skin = this->_internal_time_skin();
  uint32_t raw_time_skin;
  memcpy(&raw_time_skin, &tmp_time_skin, sizeof(tmp_time_skin));
  if (raw_time_skin != 0) {
    total_size += 1 + 4;
  }

  // float time_support = 5;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_time_support = this->_internal_time_support();
  uint32_t raw_time_support;
  memcpy(&raw_time_support, &tmp_time_support, sizeof(tmp_time_support));
  if (raw_time_support != 0) {
    total_size += 1 + 4;
  }

  // float time_skirt = 6;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_time_skirt = this->_internal_time_skirt();
  uint32_t raw_time_skirt;
  memcpy(&raw_time_skirt, &tmp_time_skirt, sizeof(tmp_time_skirt));
  if (raw_time_skirt != 0) {
    total_size += 1 + 4;
  }

  // float time_infill = 7;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_time_infill = this->_internal_time_infill();
  uint32_t raw_time_infill;
  memcpy(&raw_time_infill, &tmp_time_infill, sizeof(tmp_time_infill));
  if (raw_time_infill != 0) {
    total_size += 1 + 4;
  }

  // float time_support_infill = 8;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_time_support_infill = this->_internal_time_support_infill();
  uint32_t raw_time_support_infill;
  memcpy(&raw_time_support_infill, &tmp_time_support_infill, sizeof(tmp_time_support_infill));
  if (raw_time_support_infill != 0) {
    total_size += 1 + 4;
  }

  // float time_travel = 9;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_time_travel = this->_internal_time_travel();
  uint32_t raw_time_travel;
  memcpy(&raw_time_travel, &tmp_time_travel, sizeof(tmp_time_travel));
  if (raw_time_travel != 0) {
    total_size += 1 + 4;
  }

  // float time_retract = 10;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_time_retract = this->_internal_time_retract();
  uint32_t raw_time_retract;
  memcpy(&raw_time_retract, &tmp_time_retract, sizeof(tmp_time_retract));
  if (raw_time_retract != 0) {
    total_size += 1 + 4;
  }

  // float time_support_interface = 11;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_time_support_interface = this->_internal_time_support_interface();
  uint32_t raw_time_support_interface;
  memcpy(&raw_time_support_interface, &tmp_time_support_interface, sizeof(tmp_time_support_interface));
  if (raw_time_support_interface != 0) {
    total_size += 1 + 4;
  }

  // float time_prime_tower = 12;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_time_prime_tower = this->_internal_time_prime_tower();
  uint32_t raw_time_prime_tower;
  memcpy(&raw_time_prime_tower, &tmp_time_prime_tower, sizeof(tmp_time_prime_tower));
  if (raw_time_prime_tower != 0) {
    total_size += 1 + 4;
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_impl_._cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData PrintTimeMaterialEstimates::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSourceCheck,
    PrintTimeMaterialEstimates::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*PrintTimeMaterialEstimates::GetClassData() const { return &_class_data_; }


void PrintTimeMaterialEstimates::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg) {
  auto* const _this = static_cast<PrintTimeMaterialEstimates*>(&to_msg);
  auto& from = static_cast<const PrintTimeMaterialEstimates&>(from_msg);
  // @@protoc_insertion_point(class_specific_merge_from_start:cura.proto.PrintTimeMaterialEstimates)
  GOOGLE_DCHECK_NE(&from, _this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  _this->_impl_.materialestimates_.MergeFrom(from._impl_.materialestimates_);
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_time_none = from._internal_time_none();
  uint32_t raw_time_none;
  memcpy(&raw_time_none, &tmp_time_none, sizeof(tmp_time_none));
  if (raw_time_none != 0) {
    _this->_internal_set_time_none(from._internal_time_none());
  }
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_time_inset_0 = from._internal_time_inset_0();
  uint32_t raw_time_inset_0;
  memcpy(&raw_time_inset_0, &tmp_time_inset_0, sizeof(tmp_time_inset_0));
  if (raw_time_inset_0 != 0) {
    _this->_internal_set_time_inset_0(from._internal_time_inset_0());
  }
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_time_inset_x = from._internal_time_inset_x();
  uint32_t raw_time_inset_x;
  memcpy(&raw_time_inset_x, &tmp_time_inset_x, sizeof(tmp_time_inset_x));
  if (raw_time_inset_x != 0) {
    _this->_internal_set_time_inset_x(from._internal_time_inset_x());
  }
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_time_skin = from._internal_time_skin();
  uint32_t raw_time_skin;
  memcpy(&raw_time_skin, &tmp_time_skin, sizeof(tmp_time_skin));
  if (raw_time_skin != 0) {
    _this->_internal_set_time_skin(from._internal_time_skin());
  }
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_time_support = from._internal_time_support();
  uint32_t raw_time_support;
  memcpy(&raw_time_support, &tmp_time_support, sizeof(tmp_time_support));
  if (raw_time_support != 0) {
    _this->_internal_set_time_support(from._internal_time_support());
  }
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_time_skirt = from._internal_time_skirt();
  uint32_t raw_time_skirt;
  memcpy(&raw_time_skirt, &tmp_time_skirt, sizeof(tmp_time_skirt));
  if (raw_time_skirt != 0) {
    _this->_internal_set_time_skirt(from._internal_time_skirt());
  }
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_time_infill = from._internal_time_infill();
  uint32_t raw_time_infill;
  memcpy(&raw_time_infill, &tmp_time_infill, sizeof(tmp_time_infill));
  if (raw_time_infill != 0) {
    _this->_internal_set_time_infill(from._internal_time_infill());
  }
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_time_support_infill = from._internal_time_support_infill();
  uint32_t raw_time_support_infill;
  memcpy(&raw_time_support_infill, &tmp_time_support_infill, sizeof(tmp_time_support_infill));
  if (raw_time_support_infill != 0) {
    _this->_internal_set_time_support_infill(from._internal_time_support_infill());
  }
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_time_travel = from._internal_time_travel();
  uint32_t raw_time_travel;
  memcpy(&raw_time_travel, &tmp_time_travel, sizeof(tmp_time_travel));
  if (raw_time_travel != 0) {
    _this->_internal_set_time_travel(from._internal_time_travel());
  }
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_time_retract = from._internal_time_retract();
  uint32_t raw_time_retract;
  memcpy(&raw_time_retract, &tmp_time_retract, sizeof(tmp_time_retract));
  if (raw_time_retract != 0) {
    _this->_internal_set_time_retract(from._internal_time_retract());
  }
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_time_support_interface = from._internal_time_support_interface();
  uint32_t raw_time_support_interface;
  memcpy(&raw_time_support_interface, &tmp_time_support_interface, sizeof(tmp_time_support_interface));
  if (raw_time_support_interface != 0) {
    _this->_internal_set_time_support_interface(from._internal_time_support_interface());
  }
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_time_prime_tower = from._internal_time_prime_tower();
  uint32_t raw_time_prime_tower;
  memcpy(&raw_time_prime_tower, &tmp_time_prime_tower, sizeof(tmp_time_prime_tower));
  if (raw_time_prime_tower != 0) {
    _this->_internal_set_time_prime_tower(from._internal_time_prime_tower());
  }
  _this->_internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void PrintTimeMaterialEstimates::CopyFrom(const PrintTimeMaterialEstimates& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:cura.proto.PrintTimeMaterialEstimates)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool PrintTimeMaterialEstimates::IsInitialized() const {
  return true;
}

void PrintTimeMaterialEstimates::InternalSwap(PrintTimeMaterialEstimates* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  _impl_.materialestimates_.InternalSwap(&other->_impl_.materialestimates_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(PrintTimeMaterialEstimates, _impl_.time_prime_tower_)
      + sizeof(PrintTimeMaterialEstimates::_impl_.time_prime_tower_)
      - PROTOBUF_FIELD_OFFSET(PrintTimeMaterialEstimates, _impl_.time_none_)>(
          reinterpret_cast<char*>(&_impl_.time_none_),
          reinterpret_cast<char*>(&other->_impl_.time_none_));
}

::PROTOBUF_NAMESPACE_ID::Metadata PrintTimeMaterialEstimates::GetMetadata() const {
  return ::_pbi::AssignDescriptors(
      &descriptor_table_Cura_2eproto_getter, &descriptor_table_Cura_2eproto_once,
      file_level_metadata_Cura_2eproto[11]);
}

// ===================================================================

class MaterialEstimates::_Internal {
 public:
};

MaterialEstimates::MaterialEstimates(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor(arena, is_message_owned);
  // @@protoc_insertion_point(arena_constructor:cura.proto.MaterialEstimates)
}
MaterialEstimates::MaterialEstimates(const MaterialEstimates& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  MaterialEstimates* const _this = this; (void)_this;
  new (&_impl_) Impl_{
      decltype(_impl_.id_){}
    , decltype(_impl_.material_amount_){}
    , /*decltype(_impl_._cached_size_)*/{}};

  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::memcpy(&_impl_.id_, &from._impl_.id_,
    static_cast<size_t>(reinterpret_cast<char*>(&_impl_.material_amount_) -
    reinterpret_cast<char*>(&_impl_.id_)) + sizeof(_impl_.material_amount_));
  // @@protoc_insertion_point(copy_constructor:cura.proto.MaterialEstimates)
}

inline void MaterialEstimates::SharedCtor(
    ::_pb::Arena* arena, bool is_message_owned) {
  (void)arena;
  (void)is_message_owned;
  new (&_impl_) Impl_{
      decltype(_impl_.id_){int64_t{0}}
    , decltype(_impl_.material_amount_){0}
    , /*decltype(_impl_._cached_size_)*/{}
  };
}

MaterialEstimates::~MaterialEstimates() {
  // @@protoc_insertion_point(destructor:cura.proto.MaterialEstimates)
  if (auto *arena = _internal_metadata_.DeleteReturnArena<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>()) {
  (void)arena;
    return;
  }
  SharedDtor();
}

inline void MaterialEstimates::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void MaterialEstimates::SetCachedSize(int size) const {
  _impl_._cached_size_.Set(size);
}

void MaterialEstimates::Clear() {
// @@protoc_insertion_point(message_clear_start:cura.proto.MaterialEstimates)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  ::memset(&_impl_.id_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&_impl_.material_amount_) -
      reinterpret_cast<char*>(&_impl_.id_)) + sizeof(_impl_.material_amount_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* MaterialEstimates::_InternalParse(const char* ptr, ::_pbi::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::_pbi::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // int64 id = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 8)) {
          _impl_.id_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // float material_amount = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 21)) {
          _impl_.material_amount_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* MaterialEstimates::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:cura.proto.MaterialEstimates)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // int64 id = 1;
  if (this->_internal_id() != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteInt64ToArray(1, this->_internal_id(), target);
  }

  // float material_amount = 2;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_material_amount = this->_internal_material_amount();
  uint32_t raw_material_amount;
  memcpy(&raw_material_amount, &tmp_material_amount, sizeof(tmp_material_amount));
  if (raw_material_amount != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteFloatToArray(2, this->_internal_material_amount(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::_pbi::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:cura.proto.MaterialEstimates)
  return target;
}

size_t MaterialEstimates::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:cura.proto.MaterialEstimates)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // int64 id = 1;
  if (this->_internal_id() != 0) {
    total_size += ::_pbi::WireFormatLite::Int64SizePlusOne(this->_internal_id());
  }

  // float material_amount = 2;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_material_amount = this->_internal_material_amount();
  uint32_t raw_material_amount;
  memcpy(&raw_material_amount, &tmp_material_amount, sizeof(tmp_material_amount));
  if (raw_material_amount != 0) {
    total_size += 1 + 4;
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_impl_._cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData MaterialEstimates::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSourceCheck,
    MaterialEstimates::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*MaterialEstimates::GetClassData() const { return &_class_data_; }


void MaterialEstimates::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg) {
  auto* const _this = static_cast<MaterialEstimates*>(&to_msg);
  auto& from = static_cast<const MaterialEstimates&>(from_msg);
  // @@protoc_insertion_point(class_specific_merge_from_start:cura.proto.MaterialEstimates)
  GOOGLE_DCHECK_NE(&from, _this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_id() != 0) {
    _this->_internal_set_id(from._internal_id());
  }
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_material_amount = from._internal_material_amount();
  uint32_t raw_material_amount;
  memcpy(&raw_material_amount, &tmp_material_amount, sizeof(tmp_material_amount));
  if (raw_material_amount != 0) {
    _this->_internal_set_material_amount(from._internal_material_amount());
  }
  _this->_internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void MaterialEstimates::CopyFrom(const MaterialEstimates& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:cura.proto.MaterialEstimates)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool MaterialEstimates::IsInitialized() const {
  return true;
}

void MaterialEstimates::InternalSwap(MaterialEstimates* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(MaterialEstimates, _impl_.material_amount_)
      + sizeof(MaterialEstimates::_impl_.material_amount_)
      - PROTOBUF_FIELD_OFFSET(MaterialEstimates, _impl_.id_)>(
          reinterpret_cast<char*>(&_impl_.id_),
          reinterpret_cast<char*>(&other->_impl_.id_));
}

::PROTOBUF_NAMESPACE_ID::Metadata MaterialEstimates::GetMetadata() const {
  return ::_pbi::AssignDescriptors(
      &descriptor_table_Cura_2eproto_getter, &descriptor_table_Cura_2eproto_once,
      file_level_metadata_Cura_2eproto[12]);
}

// ===================================================================

class SettingList::_Internal {
 public:
};

SettingList::SettingList(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor(arena, is_message_owned);
  // @@protoc_insertion_point(arena_constructor:cura.proto.SettingList)
}
SettingList::SettingList(const SettingList& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  SettingList* const _this = this; (void)_this;
  new (&_impl_) Impl_{
      decltype(_impl_.settings_){from._impl_.settings_}
    , /*decltype(_impl_._cached_size_)*/{}};

  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  // @@protoc_insertion_point(copy_constructor:cura.proto.SettingList)
}

inline void SettingList::SharedCtor(
    ::_pb::Arena* arena, bool is_message_owned) {
  (void)arena;
  (void)is_message_owned;
  new (&_impl_) Impl_{
      decltype(_impl_.settings_){arena}
    , /*decltype(_impl_._cached_size_)*/{}
  };
}

SettingList::~SettingList() {
  // @@protoc_insertion_point(destructor:cura.proto.SettingList)
  if (auto *arena = _internal_metadata_.DeleteReturnArena<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>()) {
  (void)arena;
    return;
  }
  SharedDtor();
}

inline void SettingList::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  _impl_.settings_.~RepeatedPtrField();
}

void SettingList::SetCachedSize(int size) const {
  _impl_._cached_size_.Set(size);
}

void SettingList::Clear() {
// @@protoc_insertion_point(message_clear_start:cura.proto.SettingList)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  _impl_.settings_.Clear();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* SettingList::_InternalParse(const char* ptr, ::_pbi::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::_pbi::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // repeated .cura.proto.Setting settings = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_settings(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<10>(ptr));
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* SettingList::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:cura.proto.SettingList)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated .cura.proto.Setting settings = 1;
  for (unsigned i = 0,
      n = static_cast<unsigned>(this->_internal_settings_size()); i < n; i++) {
    const auto& repfield = this->_internal_settings(i);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
        InternalWriteMessage(1, repfield, repfield.GetCachedSize(), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::_pbi::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:cura.proto.SettingList)
  return target;
}

size_t SettingList::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:cura.proto.SettingList)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated .cura.proto.Setting settings = 1;
  total_size += 1UL * this->_internal_settings_size();
  for (const auto& msg : this->_impl_.settings_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_impl_._cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData SettingList::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSourceCheck,
    SettingList::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*SettingList::GetClassData() const { return &_class_data_; }


void SettingList::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg) {
  auto* const _this = static_cast<SettingList*>(&to_msg);
  auto& from = static_cast<const SettingList&>(from_msg);
  // @@protoc_insertion_point(class_specific_merge_from_start:cura.proto.SettingList)
  GOOGLE_DCHECK_NE(&from, _this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  _this->_impl_.settings_.MergeFrom(from._impl_.settings_);
  _this->_internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void SettingList::CopyFrom(const SettingList& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:cura.proto.SettingList)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool SettingList::IsInitialized() const {
  return true;
}

void SettingList::InternalSwap(SettingList* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  _impl_.settings_.InternalSwap(&other->_impl_.settings_);
}

::PROTOBUF_NAMESPACE_ID::Metadata SettingList::GetMetadata() const {
  return ::_pbi::AssignDescriptors(
      &descriptor_table_Cura_2eproto_getter, &descriptor_table_Cura_2eproto_once,
      file_level_metadata_Cura_2eproto[13]);
}

// ===================================================================

class Setting::_Internal {
 public:
};

Setting::Setting(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor(arena, is_message_owned);
  // @@protoc_insertion_point(arena_constructor:cura.proto.Setting)
}
Setting::Setting(const Setting& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  Setting* const _this = this; (void)_this;
  new (&_impl_) Impl_{
      decltype(_impl_.name_){}
    , decltype(_impl_.value_){}
    , /*decltype(_impl_._cached_size_)*/{}};

  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  _impl_.name_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.name_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_name().empty()) {
    _this->_impl_.name_.Set(from._internal_name(), 
      _this->GetArenaForAllocation());
  }
  _impl_.value_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.value_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_value().empty()) {
    _this->_impl_.value_.Set(from._internal_value(), 
      _this->GetArenaForAllocation());
  }
  // @@protoc_insertion_point(copy_constructor:cura.proto.Setting)
}

inline void Setting::SharedCtor(
    ::_pb::Arena* arena, bool is_message_owned) {
  (void)arena;
  (void)is_message_owned;
  new (&_impl_) Impl_{
      decltype(_impl_.name_){}
    , decltype(_impl_.value_){}
    , /*decltype(_impl_._cached_size_)*/{}
  };
  _impl_.name_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.name_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  _impl_.value_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.value_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
}

Setting::~Setting() {
  // @@protoc_insertion_point(destructor:cura.proto.Setting)
  if (auto *arena = _internal_metadata_.DeleteReturnArena<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>()) {
  (void)arena;
    return;
  }
  SharedDtor();
}

inline void Setting::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  _impl_.name_.Destroy();
  _impl_.value_.Destroy();
}

void Setting::SetCachedSize(int size) const {
  _impl_._cached_size_.Set(size);
}

void Setting::Clear() {
// @@protoc_insertion_point(message_clear_start:cura.proto.Setting)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  _impl_.name_.ClearToEmpty();
  _impl_.value_.ClearToEmpty();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* Setting::_InternalParse(const char* ptr, ::_pbi::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::_pbi::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // string name = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          auto str = _internal_mutable_name();
          ptr = ::_pbi::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(ptr);
          CHK_(::_pbi::VerifyUTF8(str, "cura.proto.Setting.name"));
        } else
          goto handle_unusual;
        continue;
      // bytes value = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          auto str = _internal_mutable_value();
          ptr = ::_pbi::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* Setting::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:cura.proto.Setting)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // string name = 1;
  if (!this->_internal_name().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_name().data(), static_cast<int>(this->_internal_name().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "cura.proto.Setting.name");
    target = stream->WriteStringMaybeAliased(
        1, this->_internal_name(), target);
  }

  // bytes value = 2;
  if (!this->_internal_value().empty()) {
    target = stream->WriteBytesMaybeAliased(
        2, this->_internal_value(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::_pbi::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:cura.proto.Setting)
  return target;
}

size_t Setting::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:cura.proto.Setting)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // string name = 1;
  if (!this->_internal_name().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_name());
  }

  // bytes value = 2;
  if (!this->_internal_value().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::BytesSize(
        this->_internal_value());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_impl_._cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData Setting::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSourceCheck,
    Setting::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*Setting::GetClassData() const { return &_class_data_; }


void Setting::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg) {
  auto* const _this = static_cast<Setting*>(&to_msg);
  auto& from = static_cast<const Setting&>(from_msg);
  // @@protoc_insertion_point(class_specific_merge_from_start:cura.proto.Setting)
  GOOGLE_DCHECK_NE(&from, _this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (!from._internal_name().empty()) {
    _this->_internal_set_name(from._internal_name());
  }
  if (!from._internal_value().empty()) {
    _this->_internal_set_value(from._internal_value());
  }
  _this->_internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void Setting::CopyFrom(const Setting& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:cura.proto.Setting)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool Setting::IsInitialized() const {
  return true;
}

void Setting::InternalSwap(Setting* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &_impl_.name_, lhs_arena,
      &other->_impl_.name_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &_impl_.value_, lhs_arena,
      &other->_impl_.value_, rhs_arena
  );
}

::PROTOBUF_NAMESPACE_ID::Metadata Setting::GetMetadata() const {
  return ::_pbi::AssignDescriptors(
      &descriptor_table_Cura_2eproto_getter, &descriptor_table_Cura_2eproto_once,
      file_level_metadata_Cura_2eproto[14]);
}

// ===================================================================

class SettingExtruder::_Internal {
 public:
};

SettingExtruder::SettingExtruder(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor(arena, is_message_owned);
  // @@protoc_insertion_point(arena_constructor:cura.proto.SettingExtruder)
}
SettingExtruder::SettingExtruder(const SettingExtruder& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  SettingExtruder* const _this = this; (void)_this;
  new (&_impl_) Impl_{
      decltype(_impl_.name_){}
    , decltype(_impl_.extruder_){}
    , /*decltype(_impl_._cached_size_)*/{}};

  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  _impl_.name_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.name_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_name().empty()) {
    _this->_impl_.name_.Set(from._internal_name(), 
      _this->GetArenaForAllocation());
  }
  _this->_impl_.extruder_ = from._impl_.extruder_;
  // @@protoc_insertion_point(copy_constructor:cura.proto.SettingExtruder)
}

inline void SettingExtruder::SharedCtor(
    ::_pb::Arena* arena, bool is_message_owned) {
  (void)arena;
  (void)is_message_owned;
  new (&_impl_) Impl_{
      decltype(_impl_.name_){}
    , decltype(_impl_.extruder_){0}
    , /*decltype(_impl_._cached_size_)*/{}
  };
  _impl_.name_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.name_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
}

SettingExtruder::~SettingExtruder() {
  // @@protoc_insertion_point(destructor:cura.proto.SettingExtruder)
  if (auto *arena = _internal_metadata_.DeleteReturnArena<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>()) {
  (void)arena;
    return;
  }
  SharedDtor();
}

inline void SettingExtruder::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  _impl_.name_.Destroy();
}

void SettingExtruder::SetCachedSize(int size) const {
  _impl_._cached_size_.Set(size);
}

void SettingExtruder::Clear() {
// @@protoc_insertion_point(message_clear_start:cura.proto.SettingExtruder)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  _impl_.name_.ClearToEmpty();
  _impl_.extruder_ = 0;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* SettingExtruder::_InternalParse(const char* ptr, ::_pbi::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::_pbi::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // string name = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          auto str = _internal_mutable_name();
          ptr = ::_pbi::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(ptr);
          CHK_(::_pbi::VerifyUTF8(str, "cura.proto.SettingExtruder.name"));
        } else
          goto handle_unusual;
        continue;
      // int32 extruder = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 16)) {
          _impl_.extruder_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* SettingExtruder::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:cura.proto.SettingExtruder)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // string name = 1;
  if (!this->_internal_name().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_name().data(), static_cast<int>(this->_internal_name().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "cura.proto.SettingExtruder.name");
    target = stream->WriteStringMaybeAliased(
        1, this->_internal_name(), target);
  }

  // int32 extruder = 2;
  if (this->_internal_extruder() != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteInt32ToArray(2, this->_internal_extruder(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::_pbi::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:cura.proto.SettingExtruder)
  return target;
}

size_t SettingExtruder::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:cura.proto.SettingExtruder)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // string name = 1;
  if (!this->_internal_name().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_name());
  }

  // int32 extruder = 2;
  if (this->_internal_extruder() != 0) {
    total_size += ::_pbi::WireFormatLite::Int32SizePlusOne(this->_internal_extruder());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_impl_._cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData SettingExtruder::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSourceCheck,
    SettingExtruder::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*SettingExtruder::GetClassData() const { return &_class_data_; }


void SettingExtruder::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg) {
  auto* const _this = static_cast<SettingExtruder*>(&to_msg);
  auto& from = static_cast<const SettingExtruder&>(from_msg);
  // @@protoc_insertion_point(class_specific_merge_from_start:cura.proto.SettingExtruder)
  GOOGLE_DCHECK_NE(&from, _this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (!from._internal_name().empty()) {
    _this->_internal_set_name(from._internal_name());
  }
  if (from._internal_extruder() != 0) {
    _this->_internal_set_extruder(from._internal_extruder());
  }
  _this->_internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void SettingExtruder::CopyFrom(const SettingExtruder& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:cura.proto.SettingExtruder)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool SettingExtruder::IsInitialized() const {
  return true;
}

void SettingExtruder::InternalSwap(SettingExtruder* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &_impl_.name_, lhs_arena,
      &other->_impl_.name_, rhs_arena
  );
  swap(_impl_.extruder_, other->_impl_.extruder_);
}

::PROTOBUF_NAMESPACE_ID::Metadata SettingExtruder::GetMetadata() const {
  return ::_pbi::AssignDescriptors(
      &descriptor_table_Cura_2eproto_getter, &descriptor_table_Cura_2eproto_once,
      file_level_metadata_Cura_2eproto[15]);
}

// ===================================================================

class GCodePrefix::_Internal {
 public:
};

GCodePrefix::GCodePrefix(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor(arena, is_message_owned);
  // @@protoc_insertion_point(arena_constructor:cura.proto.GCodePrefix)
}
GCodePrefix::GCodePrefix(const GCodePrefix& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  GCodePrefix* const _this = this; (void)_this;
  new (&_impl_) Impl_{
      decltype(_impl_.data_){}
    , /*decltype(_impl_._cached_size_)*/{}};

  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  _impl_.data_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.data_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_data().empty()) {
    _this->_impl_.data_.Set(from._internal_data(), 
      _this->GetArenaForAllocation());
  }
  // @@protoc_insertion_point(copy_constructor:cura.proto.GCodePrefix)
}

inline void GCodePrefix::SharedCtor(
    ::_pb::Arena* arena, bool is_message_owned) {
  (void)arena;
  (void)is_message_owned;
  new (&_impl_) Impl_{
      decltype(_impl_.data_){}
    , /*decltype(_impl_._cached_size_)*/{}
  };
  _impl_.data_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.data_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
}

GCodePrefix::~GCodePrefix() {
  // @@protoc_insertion_point(destructor:cura.proto.GCodePrefix)
  if (auto *arena = _internal_metadata_.DeleteReturnArena<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>()) {
  (void)arena;
    return;
  }
  SharedDtor();
}

inline void GCodePrefix::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  _impl_.data_.Destroy();
}

void GCodePrefix::SetCachedSize(int size) const {
  _impl_._cached_size_.Set(size);
}

void GCodePrefix::Clear() {
// @@protoc_insertion_point(message_clear_start:cura.proto.GCodePrefix)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  _impl_.data_.ClearToEmpty();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* GCodePrefix::_InternalParse(const char* ptr, ::_pbi::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::_pbi::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // bytes data = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          auto str = _internal_mutable_data();
          ptr = ::_pbi::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* GCodePrefix::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:cura.proto.GCodePrefix)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // bytes data = 2;
  if (!this->_internal_data().empty()) {
    target = stream->WriteBytesMaybeAliased(
        2, this->_internal_data(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::_pbi::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:cura.proto.GCodePrefix)
  return target;
}

size_t GCodePrefix::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:cura.proto.GCodePrefix)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // bytes data = 2;
  if (!this->_internal_data().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::BytesSize(
        this->_internal_data());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_impl_._cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData GCodePrefix::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSourceCheck,
    GCodePrefix::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GCodePrefix::GetClassData() const { return &_class_data_; }


void GCodePrefix::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg) {
  auto* const _this = static_cast<GCodePrefix*>(&to_msg);
  auto& from = static_cast<const GCodePrefix&>(from_msg);
  // @@protoc_insertion_point(class_specific_merge_from_start:cura.proto.GCodePrefix)
  GOOGLE_DCHECK_NE(&from, _this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (!from._internal_data().empty()) {
    _this->_internal_set_data(from._internal_data());
  }
  _this->_internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void GCodePrefix::CopyFrom(const GCodePrefix& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:cura.proto.GCodePrefix)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool GCodePrefix::IsInitialized() const {
  return true;
}

void GCodePrefix::InternalSwap(GCodePrefix* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &_impl_.data_, lhs_arena,
      &other->_impl_.data_, rhs_arena
  );
}

::PROTOBUF_NAMESPACE_ID::Metadata GCodePrefix::GetMetadata() const {
  return ::_pbi::AssignDescriptors(
      &descriptor_table_Cura_2eproto_getter, &descriptor_table_Cura_2eproto_once,
      file_level_metadata_Cura_2eproto[16]);
}

// ===================================================================

class SliceUUID::_Internal {
 public:
};

SliceUUID::SliceUUID(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor(arena, is_message_owned);
  // @@protoc_insertion_point(arena_constructor:cura.proto.SliceUUID)
}
SliceUUID::SliceUUID(const SliceUUID& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  SliceUUID* const _this = this; (void)_this;
  new (&_impl_) Impl_{
      decltype(_impl_.slice_uuid_){}
    , /*decltype(_impl_._cached_size_)*/{}};

  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  _impl_.slice_uuid_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.slice_uuid_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_slice_uuid().empty()) {
    _this->_impl_.slice_uuid_.Set(from._internal_slice_uuid(), 
      _this->GetArenaForAllocation());
  }
  // @@protoc_insertion_point(copy_constructor:cura.proto.SliceUUID)
}

inline void SliceUUID::SharedCtor(
    ::_pb::Arena* arena, bool is_message_owned) {
  (void)arena;
  (void)is_message_owned;
  new (&_impl_) Impl_{
      decltype(_impl_.slice_uuid_){}
    , /*decltype(_impl_._cached_size_)*/{}
  };
  _impl_.slice_uuid_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.slice_uuid_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
}

SliceUUID::~SliceUUID() {
  // @@protoc_insertion_point(destructor:cura.proto.SliceUUID)
  if (auto *arena = _internal_metadata_.DeleteReturnArena<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>()) {
  (void)arena;
    return;
  }
  SharedDtor();
}

inline void SliceUUID::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  _impl_.slice_uuid_.Destroy();
}

void SliceUUID::SetCachedSize(int size) const {
  _impl_._cached_size_.Set(size);
}

void SliceUUID::Clear() {
// @@protoc_insertion_point(message_clear_start:cura.proto.SliceUUID)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  _impl_.slice_uuid_.ClearToEmpty();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* SliceUUID::_InternalParse(const char* ptr, ::_pbi::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::_pbi::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // string slice_uuid = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          auto str = _internal_mutable_slice_uuid();
          ptr = ::_pbi::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(ptr);
          CHK_(::_pbi::VerifyUTF8(str, "cura.proto.SliceUUID.slice_uuid"));
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* SliceUUID::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:cura.proto.SliceUUID)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // string slice_uuid = 1;
  if (!this->_internal_slice_uuid().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_slice_uuid().data(), static_cast<int>(this->_internal_slice_uuid().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "cura.proto.SliceUUID.slice_uuid");
    target = stream->WriteStringMaybeAliased(
        1, this->_internal_slice_uuid(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::_pbi::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:cura.proto.SliceUUID)
  return target;
}

size_t SliceUUID::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:cura.proto.SliceUUID)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // string slice_uuid = 1;
  if (!this->_internal_slice_uuid().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_slice_uuid());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_impl_._cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData SliceUUID::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSourceCheck,
    SliceUUID::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*SliceUUID::GetClassData() const { return &_class_data_; }


void SliceUUID::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg) {
  auto* const _this = static_cast<SliceUUID*>(&to_msg);
  auto& from = static_cast<const SliceUUID&>(from_msg);
  // @@protoc_insertion_point(class_specific_merge_from_start:cura.proto.SliceUUID)
  GOOGLE_DCHECK_NE(&from, _this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (!from._internal_slice_uuid().empty()) {
    _this->_internal_set_slice_uuid(from._internal_slice_uuid());
  }
  _this->_internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void SliceUUID::CopyFrom(const SliceUUID& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:cura.proto.SliceUUID)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool SliceUUID::IsInitialized() const {
  return true;
}

void SliceUUID::InternalSwap(SliceUUID* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &_impl_.slice_uuid_, lhs_arena,
      &other->_impl_.slice_uuid_, rhs_arena
  );
}

::PROTOBUF_NAMESPACE_ID::Metadata SliceUUID::GetMetadata() const {
  return ::_pbi::AssignDescriptors(
      &descriptor_table_Cura_2eproto_getter, &descriptor_table_Cura_2eproto_once,
      file_level_metadata_Cura_2eproto[17]);
}

// ===================================================================

class SlicingFinished::_Internal {
 public:
};

SlicingFinished::SlicingFinished(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase(arena, is_message_owned) {
  // @@protoc_insertion_point(arena_constructor:cura.proto.SlicingFinished)
}
SlicingFinished::SlicingFinished(const SlicingFinished& from)
  : ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase() {
  SlicingFinished* const _this = this; (void)_this;
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  // @@protoc_insertion_point(copy_constructor:cura.proto.SlicingFinished)
}





const ::PROTOBUF_NAMESPACE_ID::Message::ClassData SlicingFinished::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::CopyImpl,
    ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::MergeImpl,
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*SlicingFinished::GetClassData() const { return &_class_data_; }







::PROTOBUF_NAMESPACE_ID::Metadata SlicingFinished::GetMetadata() const {
  return ::_pbi::AssignDescriptors(
      &descriptor_table_Cura_2eproto_getter, &descriptor_table_Cura_2eproto_once,
      file_level_metadata_Cura_2eproto[18]);
}

// @@protoc_insertion_point(namespace_scope)
}  // namespace proto
}  // namespace cura
PROTOBUF_NAMESPACE_OPEN
template<> PROTOBUF_NOINLINE ::cura::proto::ObjectList*
Arena::CreateMaybeMessage< ::cura::proto::ObjectList >(Arena* arena) {
  return Arena::CreateMessageInternal< ::cura::proto::ObjectList >(arena);
}
template<> PROTOBUF_NOINLINE ::cura::proto::EnginePlugin*
Arena::CreateMaybeMessage< ::cura::proto::EnginePlugin >(Arena* arena) {
  return Arena::CreateMessageInternal< ::cura::proto::EnginePlugin >(arena);
}
template<> PROTOBUF_NOINLINE ::cura::proto::Slice*
Arena::CreateMaybeMessage< ::cura::proto::Slice >(Arena* arena) {
  return Arena::CreateMessageInternal< ::cura::proto::Slice >(arena);
}
template<> PROTOBUF_NOINLINE ::cura::proto::Extruder*
Arena::CreateMaybeMessage< ::cura::proto::Extruder >(Arena* arena) {
  return Arena::CreateMessageInternal< ::cura::proto::Extruder >(arena);
}
template<> PROTOBUF_NOINLINE ::cura::proto::Object*
Arena::CreateMaybeMessage< ::cura::proto::Object >(Arena* arena) {
  return Arena::CreateMessageInternal< ::cura::proto::Object >(arena);
}
template<> PROTOBUF_NOINLINE ::cura::proto::Progress*
Arena::CreateMaybeMessage< ::cura::proto::Progress >(Arena* arena) {
  return Arena::CreateMessageInternal< ::cura::proto::Progress >(arena);
}
template<> PROTOBUF_NOINLINE ::cura::proto::Layer*
Arena::CreateMaybeMessage< ::cura::proto::Layer >(Arena* arena) {
  return Arena::CreateMessageInternal< ::cura::proto::Layer >(arena);
}
template<> PROTOBUF_NOINLINE ::cura::proto::Polygon*
Arena::CreateMaybeMessage< ::cura::proto::Polygon >(Arena* arena) {
  return Arena::CreateMessageInternal< ::cura::proto::Polygon >(arena);
}
template<> PROTOBUF_NOINLINE ::cura::proto::LayerOptimized*
Arena::CreateMaybeMessage< ::cura::proto::LayerOptimized >(Arena* arena) {
  return Arena::CreateMessageInternal< ::cura::proto::LayerOptimized >(arena);
}
template<> PROTOBUF_NOINLINE ::cura::proto::PathSegment*
Arena::CreateMaybeMessage< ::cura::proto::PathSegment >(Arena* arena) {
  return Arena::CreateMessageInternal< ::cura::proto::PathSegment >(arena);
}
template<> PROTOBUF_NOINLINE ::cura::proto::GCodeLayer*
Arena::CreateMaybeMessage< ::cura::proto::GCodeLayer >(Arena* arena) {
  return Arena::CreateMessageInternal< ::cura::proto::GCodeLayer >(arena);
}
template<> PROTOBUF_NOINLINE ::cura::proto::PrintTimeMaterialEstimates*
Arena::CreateMaybeMessage< ::cura::proto::PrintTimeMaterialEstimates >(Arena* arena) {
  return Arena::CreateMessageInternal< ::cura::proto::PrintTimeMaterialEstimates >(arena);
}
template<> PROTOBUF_NOINLINE ::cura::proto::MaterialEstimates*
Arena::CreateMaybeMessage< ::cura::proto::MaterialEstimates >(Arena* arena) {
  return Arena::CreateMessageInternal< ::cura::proto::MaterialEstimates >(arena);
}
template<> PROTOBUF_NOINLINE ::cura::proto::SettingList*
Arena::CreateMaybeMessage< ::cura::proto::SettingList >(Arena* arena) {
  return Arena::CreateMessageInternal< ::cura::proto::SettingList >(arena);
}
template<> PROTOBUF_NOINLINE ::cura::proto::Setting*
Arena::CreateMaybeMessage< ::cura::proto::Setting >(Arena* arena) {
  return Arena::CreateMessageInternal< ::cura::proto::Setting >(arena);
}
template<> PROTOBUF_NOINLINE ::cura::proto::SettingExtruder*
Arena::CreateMaybeMessage< ::cura::proto::SettingExtruder >(Arena* arena) {
  return Arena::CreateMessageInternal< ::cura::proto::SettingExtruder >(arena);
}
template<> PROTOBUF_NOINLINE ::cura::proto::GCodePrefix*
Arena::CreateMaybeMessage< ::cura::proto::GCodePrefix >(Arena* arena) {
  return Arena::CreateMessageInternal< ::cura::proto::GCodePrefix >(arena);
}
template<> PROTOBUF_NOINLINE ::cura::proto::SliceUUID*
Arena::CreateMaybeMessage< ::cura::proto::SliceUUID >(Arena* arena) {
  return Arena::CreateMessageInternal< ::cura::proto::SliceUUID >(arena);
}
template<> PROTOBUF_NOINLINE ::cura::proto::SlicingFinished*
Arena::CreateMaybeMessage< ::cura::proto::SlicingFinished >(Arena* arena) {
  return Arena::CreateMessageInternal< ::cura::proto::SlicingFinished >(arena);
}
PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)
#include <google/protobuf/port_undef.inc>

# CMAKE generated file: DO NOT EDIT!
# Generated by "Ninja" Generator, CMake Version 4.1

# This file contains all the build statements describing the
# compilation DAG.

# =============================================================================
# Write statements declared in CMakeLists.txt:
# 
# Which is the root file.
# =============================================================================

# =============================================================================
# Project: CuraEngine
# Configurations: Release
# =============================================================================

#############################################
# Minimal version of Ninja required by this file

ninja_required_version = 1.5


#############################################
# Set configuration variable for custom commands.

CONFIGURATION = Release
# =============================================================================
# Include auxiliary files.


#############################################
# Include rules file.

include CMakeFiles\rules.ninja

# =============================================================================

#############################################
# Logical path to working directory; prefix for absolute paths.

cmake_ninja_workdir = C$:\Users\wsd07\vscode\Cura-Dev\CuraEngine\build\Release\
# =============================================================================
# Object build statements for STATIC_LIBRARY target _CuraEngine


#############################################
# Order-only phony target for _CuraEngine

build cmake_object_order_depends_target__CuraEngine: phony || Cura.pb.cc Cura.pb.h

build CMakeFiles\_CuraEngine.dir\src\Application.cpp.obj: CXX_COMPILER___CuraEngine_unscanned_Release C$:\Users\wsd07\vscode\Cura-Dev\CuraEngine\src\Application.cpp || cmake_object_order_depends_target__CuraEngine
  CONFIG = Release
  DEFINES = -DAGRPC_BOOST_ASIO -DARCUS -DBOOST_ALL_NO_LIB -DBOOST_STACKTRACE_USE_NOOP -DBOOST_STACKTRACE_USE_WINDBG -DBOOST_STACKTRACE_USE_WINDBG_CACHED -DCARES_STATICLIB -DCURA_ENGINE_HASH=\"unknown\" -DCURA_ENGINE_VERSION=\"5.11.0-alpha.0\" -DENABLE_PLUGINS -DNOMINMAX -DSENTRY_ENVIRONMENT=\"development\" -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSTB_TEXTEDIT_KEYTYPE=unsigned
  FLAGS = /DWIN32 /D_WINDOWS /EHsc /O2 /Ob2 /DNDEBUG -std:c++20 -MD /utf-8 /permissive-
  INCLUDES = -IC:\Users\<USER>\vscode\Cura-Dev\CuraEngine\include -IC:\Users\<USER>\vscode\Cura-Dev\CuraEngine\build\Release -external:IC:\Users\<USER>\.conan2\p\b\arcusee7666f716199\p\include -external:IC:\Users\<USER>\.conan2\p\proto47a676cb9257b\p\include -external:IC:\Users\<USER>\.conan2\p\zlibcfb9789dc2a53\p\include -external:IC:\Users\<USER>\.conan2\p\spdlo2d4b540ab0f22\p\include -external:IC:\Users\<USER>\.conan2\p\fmtdb20aad6e1bd4\p\include -external:IC:\Users\<USER>\.conan2\p\range0301bf3d76d5d\p\include -external:IC:\Users\<USER>\.conan2\p\b\clippf8ddcd4a5d961\p\include -external:IC:\Users\<USER>\.conan2\p\mapbo34ee9aeb574f9\p\include -external:IC:\Users\<USER>\.conan2\p\mapbofe72fb50cf7c4\p\include -external:IC:\Users\<USER>\.conan2\p\mapbo1b62b48748b82\p\include -external:IC:\Users\<USER>\.conan2\p\rapidf7a3355ba53c4\p\include -external:IC:\Users\<USER>\.conan2\p\stb6342cecb318f5\p\include -external:IC:\Users\<USER>\.conan2\p\boosta79d738920bc3\p\include -external:IC:\Users\<USER>\.conan2\p\scrip4cfd014ad4660\p\include -external:IC:\Users\<USER>\.conan2\p\nearg388d58da7a54c\p\include -external:IC:\Users\<USER>\.conan2\p\b\curaebb245e64b5ac7\p\include -external:IC:\Users\<USER>\.conan2\p\asio-c31dc2a380bf0\p\include -external:IC:\Users\<USER>\.conan2\p\grpc8b6c73b4e5756\p\include -external:IC:\Users\<USER>\.conan2\p\absei6a15444d60d47\p\include -external:W0
  OBJECT_DIR = CMakeFiles\_CuraEngine.dir
  OBJECT_FILE_DIR = CMakeFiles\_CuraEngine.dir\src
  TARGET_COMPILE_PDB = CMakeFiles\_CuraEngine.dir\_CuraEngine.pdb
  TARGET_PDB = _CuraEngine.pdb

build CMakeFiles\_CuraEngine.dir\src\bridge.cpp.obj: CXX_COMPILER___CuraEngine_unscanned_Release C$:\Users\wsd07\vscode\Cura-Dev\CuraEngine\src\bridge.cpp || cmake_object_order_depends_target__CuraEngine
  CONFIG = Release
  DEFINES = -DAGRPC_BOOST_ASIO -DARCUS -DBOOST_ALL_NO_LIB -DBOOST_STACKTRACE_USE_NOOP -DBOOST_STACKTRACE_USE_WINDBG -DBOOST_STACKTRACE_USE_WINDBG_CACHED -DCARES_STATICLIB -DCURA_ENGINE_HASH=\"unknown\" -DCURA_ENGINE_VERSION=\"5.11.0-alpha.0\" -DENABLE_PLUGINS -DNOMINMAX -DSENTRY_ENVIRONMENT=\"development\" -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSTB_TEXTEDIT_KEYTYPE=unsigned
  FLAGS = /DWIN32 /D_WINDOWS /EHsc /O2 /Ob2 /DNDEBUG -std:c++20 -MD /utf-8 /permissive-
  INCLUDES = -IC:\Users\<USER>\vscode\Cura-Dev\CuraEngine\include -IC:\Users\<USER>\vscode\Cura-Dev\CuraEngine\build\Release -external:IC:\Users\<USER>\.conan2\p\b\arcusee7666f716199\p\include -external:IC:\Users\<USER>\.conan2\p\proto47a676cb9257b\p\include -external:IC:\Users\<USER>\.conan2\p\zlibcfb9789dc2a53\p\include -external:IC:\Users\<USER>\.conan2\p\spdlo2d4b540ab0f22\p\include -external:IC:\Users\<USER>\.conan2\p\fmtdb20aad6e1bd4\p\include -external:IC:\Users\<USER>\.conan2\p\range0301bf3d76d5d\p\include -external:IC:\Users\<USER>\.conan2\p\b\clippf8ddcd4a5d961\p\include -external:IC:\Users\<USER>\.conan2\p\mapbo34ee9aeb574f9\p\include -external:IC:\Users\<USER>\.conan2\p\mapbofe72fb50cf7c4\p\include -external:IC:\Users\<USER>\.conan2\p\mapbo1b62b48748b82\p\include -external:IC:\Users\<USER>\.conan2\p\rapidf7a3355ba53c4\p\include -external:IC:\Users\<USER>\.conan2\p\stb6342cecb318f5\p\include -external:IC:\Users\<USER>\.conan2\p\boosta79d738920bc3\p\include -external:IC:\Users\<USER>\.conan2\p\scrip4cfd014ad4660\p\include -external:IC:\Users\<USER>\.conan2\p\nearg388d58da7a54c\p\include -external:IC:\Users\<USER>\.conan2\p\b\curaebb245e64b5ac7\p\include -external:IC:\Users\<USER>\.conan2\p\asio-c31dc2a380bf0\p\include -external:IC:\Users\<USER>\.conan2\p\grpc8b6c73b4e5756\p\include -external:IC:\Users\<USER>\.conan2\p\absei6a15444d60d47\p\include -external:W0
  OBJECT_DIR = CMakeFiles\_CuraEngine.dir
  OBJECT_FILE_DIR = CMakeFiles\_CuraEngine.dir\src
  TARGET_COMPILE_PDB = CMakeFiles\_CuraEngine.dir\_CuraEngine.pdb
  TARGET_PDB = _CuraEngine.pdb

build CMakeFiles\_CuraEngine.dir\src\ConicalOverhang.cpp.obj: CXX_COMPILER___CuraEngine_unscanned_Release C$:\Users\wsd07\vscode\Cura-Dev\CuraEngine\src\ConicalOverhang.cpp || cmake_object_order_depends_target__CuraEngine
  CONFIG = Release
  DEFINES = -DAGRPC_BOOST_ASIO -DARCUS -DBOOST_ALL_NO_LIB -DBOOST_STACKTRACE_USE_NOOP -DBOOST_STACKTRACE_USE_WINDBG -DBOOST_STACKTRACE_USE_WINDBG_CACHED -DCARES_STATICLIB -DCURA_ENGINE_HASH=\"unknown\" -DCURA_ENGINE_VERSION=\"5.11.0-alpha.0\" -DENABLE_PLUGINS -DNOMINMAX -DSENTRY_ENVIRONMENT=\"development\" -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSTB_TEXTEDIT_KEYTYPE=unsigned
  FLAGS = /DWIN32 /D_WINDOWS /EHsc /O2 /Ob2 /DNDEBUG -std:c++20 -MD /utf-8 /permissive-
  INCLUDES = -IC:\Users\<USER>\vscode\Cura-Dev\CuraEngine\include -IC:\Users\<USER>\vscode\Cura-Dev\CuraEngine\build\Release -external:IC:\Users\<USER>\.conan2\p\b\arcusee7666f716199\p\include -external:IC:\Users\<USER>\.conan2\p\proto47a676cb9257b\p\include -external:IC:\Users\<USER>\.conan2\p\zlibcfb9789dc2a53\p\include -external:IC:\Users\<USER>\.conan2\p\spdlo2d4b540ab0f22\p\include -external:IC:\Users\<USER>\.conan2\p\fmtdb20aad6e1bd4\p\include -external:IC:\Users\<USER>\.conan2\p\range0301bf3d76d5d\p\include -external:IC:\Users\<USER>\.conan2\p\b\clippf8ddcd4a5d961\p\include -external:IC:\Users\<USER>\.conan2\p\mapbo34ee9aeb574f9\p\include -external:IC:\Users\<USER>\.conan2\p\mapbofe72fb50cf7c4\p\include -external:IC:\Users\<USER>\.conan2\p\mapbo1b62b48748b82\p\include -external:IC:\Users\<USER>\.conan2\p\rapidf7a3355ba53c4\p\include -external:IC:\Users\<USER>\.conan2\p\stb6342cecb318f5\p\include -external:IC:\Users\<USER>\.conan2\p\boosta79d738920bc3\p\include -external:IC:\Users\<USER>\.conan2\p\scrip4cfd014ad4660\p\include -external:IC:\Users\<USER>\.conan2\p\nearg388d58da7a54c\p\include -external:IC:\Users\<USER>\.conan2\p\b\curaebb245e64b5ac7\p\include -external:IC:\Users\<USER>\.conan2\p\asio-c31dc2a380bf0\p\include -external:IC:\Users\<USER>\.conan2\p\grpc8b6c73b4e5756\p\include -external:IC:\Users\<USER>\.conan2\p\absei6a15444d60d47\p\include -external:W0
  OBJECT_DIR = CMakeFiles\_CuraEngine.dir
  OBJECT_FILE_DIR = CMakeFiles\_CuraEngine.dir\src
  TARGET_COMPILE_PDB = CMakeFiles\_CuraEngine.dir\_CuraEngine.pdb
  TARGET_PDB = _CuraEngine.pdb

build CMakeFiles\_CuraEngine.dir\src\ExtruderPlan.cpp.obj: CXX_COMPILER___CuraEngine_unscanned_Release C$:\Users\wsd07\vscode\Cura-Dev\CuraEngine\src\ExtruderPlan.cpp || cmake_object_order_depends_target__CuraEngine
  CONFIG = Release
  DEFINES = -DAGRPC_BOOST_ASIO -DARCUS -DBOOST_ALL_NO_LIB -DBOOST_STACKTRACE_USE_NOOP -DBOOST_STACKTRACE_USE_WINDBG -DBOOST_STACKTRACE_USE_WINDBG_CACHED -DCARES_STATICLIB -DCURA_ENGINE_HASH=\"unknown\" -DCURA_ENGINE_VERSION=\"5.11.0-alpha.0\" -DENABLE_PLUGINS -DNOMINMAX -DSENTRY_ENVIRONMENT=\"development\" -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSTB_TEXTEDIT_KEYTYPE=unsigned
  FLAGS = /DWIN32 /D_WINDOWS /EHsc /O2 /Ob2 /DNDEBUG -std:c++20 -MD /utf-8 /permissive-
  INCLUDES = -IC:\Users\<USER>\vscode\Cura-Dev\CuraEngine\include -IC:\Users\<USER>\vscode\Cura-Dev\CuraEngine\build\Release -external:IC:\Users\<USER>\.conan2\p\b\arcusee7666f716199\p\include -external:IC:\Users\<USER>\.conan2\p\proto47a676cb9257b\p\include -external:IC:\Users\<USER>\.conan2\p\zlibcfb9789dc2a53\p\include -external:IC:\Users\<USER>\.conan2\p\spdlo2d4b540ab0f22\p\include -external:IC:\Users\<USER>\.conan2\p\fmtdb20aad6e1bd4\p\include -external:IC:\Users\<USER>\.conan2\p\range0301bf3d76d5d\p\include -external:IC:\Users\<USER>\.conan2\p\b\clippf8ddcd4a5d961\p\include -external:IC:\Users\<USER>\.conan2\p\mapbo34ee9aeb574f9\p\include -external:IC:\Users\<USER>\.conan2\p\mapbofe72fb50cf7c4\p\include -external:IC:\Users\<USER>\.conan2\p\mapbo1b62b48748b82\p\include -external:IC:\Users\<USER>\.conan2\p\rapidf7a3355ba53c4\p\include -external:IC:\Users\<USER>\.conan2\p\stb6342cecb318f5\p\include -external:IC:\Users\<USER>\.conan2\p\boosta79d738920bc3\p\include -external:IC:\Users\<USER>\.conan2\p\scrip4cfd014ad4660\p\include -external:IC:\Users\<USER>\.conan2\p\nearg388d58da7a54c\p\include -external:IC:\Users\<USER>\.conan2\p\b\curaebb245e64b5ac7\p\include -external:IC:\Users\<USER>\.conan2\p\asio-c31dc2a380bf0\p\include -external:IC:\Users\<USER>\.conan2\p\grpc8b6c73b4e5756\p\include -external:IC:\Users\<USER>\.conan2\p\absei6a15444d60d47\p\include -external:W0
  OBJECT_DIR = CMakeFiles\_CuraEngine.dir
  OBJECT_FILE_DIR = CMakeFiles\_CuraEngine.dir\src
  TARGET_COMPILE_PDB = CMakeFiles\_CuraEngine.dir\_CuraEngine.pdb
  TARGET_PDB = _CuraEngine.pdb

build CMakeFiles\_CuraEngine.dir\src\ExtruderTrain.cpp.obj: CXX_COMPILER___CuraEngine_unscanned_Release C$:\Users\wsd07\vscode\Cura-Dev\CuraEngine\src\ExtruderTrain.cpp || cmake_object_order_depends_target__CuraEngine
  CONFIG = Release
  DEFINES = -DAGRPC_BOOST_ASIO -DARCUS -DBOOST_ALL_NO_LIB -DBOOST_STACKTRACE_USE_NOOP -DBOOST_STACKTRACE_USE_WINDBG -DBOOST_STACKTRACE_USE_WINDBG_CACHED -DCARES_STATICLIB -DCURA_ENGINE_HASH=\"unknown\" -DCURA_ENGINE_VERSION=\"5.11.0-alpha.0\" -DENABLE_PLUGINS -DNOMINMAX -DSENTRY_ENVIRONMENT=\"development\" -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSTB_TEXTEDIT_KEYTYPE=unsigned
  FLAGS = /DWIN32 /D_WINDOWS /EHsc /O2 /Ob2 /DNDEBUG -std:c++20 -MD /utf-8 /permissive-
  INCLUDES = -IC:\Users\<USER>\vscode\Cura-Dev\CuraEngine\include -IC:\Users\<USER>\vscode\Cura-Dev\CuraEngine\build\Release -external:IC:\Users\<USER>\.conan2\p\b\arcusee7666f716199\p\include -external:IC:\Users\<USER>\.conan2\p\proto47a676cb9257b\p\include -external:IC:\Users\<USER>\.conan2\p\zlibcfb9789dc2a53\p\include -external:IC:\Users\<USER>\.conan2\p\spdlo2d4b540ab0f22\p\include -external:IC:\Users\<USER>\.conan2\p\fmtdb20aad6e1bd4\p\include -external:IC:\Users\<USER>\.conan2\p\range0301bf3d76d5d\p\include -external:IC:\Users\<USER>\.conan2\p\b\clippf8ddcd4a5d961\p\include -external:IC:\Users\<USER>\.conan2\p\mapbo34ee9aeb574f9\p\include -external:IC:\Users\<USER>\.conan2\p\mapbofe72fb50cf7c4\p\include -external:IC:\Users\<USER>\.conan2\p\mapbo1b62b48748b82\p\include -external:IC:\Users\<USER>\.conan2\p\rapidf7a3355ba53c4\p\include -external:IC:\Users\<USER>\.conan2\p\stb6342cecb318f5\p\include -external:IC:\Users\<USER>\.conan2\p\boosta79d738920bc3\p\include -external:IC:\Users\<USER>\.conan2\p\scrip4cfd014ad4660\p\include -external:IC:\Users\<USER>\.conan2\p\nearg388d58da7a54c\p\include -external:IC:\Users\<USER>\.conan2\p\b\curaebb245e64b5ac7\p\include -external:IC:\Users\<USER>\.conan2\p\asio-c31dc2a380bf0\p\include -external:IC:\Users\<USER>\.conan2\p\grpc8b6c73b4e5756\p\include -external:IC:\Users\<USER>\.conan2\p\absei6a15444d60d47\p\include -external:W0
  OBJECT_DIR = CMakeFiles\_CuraEngine.dir
  OBJECT_FILE_DIR = CMakeFiles\_CuraEngine.dir\src
  TARGET_COMPILE_PDB = CMakeFiles\_CuraEngine.dir\_CuraEngine.pdb
  TARGET_PDB = _CuraEngine.pdb

build CMakeFiles\_CuraEngine.dir\src\FffGcodeWriter.cpp.obj: CXX_COMPILER___CuraEngine_unscanned_Release C$:\Users\wsd07\vscode\Cura-Dev\CuraEngine\src\FffGcodeWriter.cpp || cmake_object_order_depends_target__CuraEngine
  CONFIG = Release
  DEFINES = -DAGRPC_BOOST_ASIO -DARCUS -DBOOST_ALL_NO_LIB -DBOOST_STACKTRACE_USE_NOOP -DBOOST_STACKTRACE_USE_WINDBG -DBOOST_STACKTRACE_USE_WINDBG_CACHED -DCARES_STATICLIB -DCURA_ENGINE_HASH=\"unknown\" -DCURA_ENGINE_VERSION=\"5.11.0-alpha.0\" -DENABLE_PLUGINS -DNOMINMAX -DSENTRY_ENVIRONMENT=\"development\" -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSTB_TEXTEDIT_KEYTYPE=unsigned
  FLAGS = /DWIN32 /D_WINDOWS /EHsc /O2 /Ob2 /DNDEBUG -std:c++20 -MD /utf-8 /permissive-
  INCLUDES = -IC:\Users\<USER>\vscode\Cura-Dev\CuraEngine\include -IC:\Users\<USER>\vscode\Cura-Dev\CuraEngine\build\Release -external:IC:\Users\<USER>\.conan2\p\b\arcusee7666f716199\p\include -external:IC:\Users\<USER>\.conan2\p\proto47a676cb9257b\p\include -external:IC:\Users\<USER>\.conan2\p\zlibcfb9789dc2a53\p\include -external:IC:\Users\<USER>\.conan2\p\spdlo2d4b540ab0f22\p\include -external:IC:\Users\<USER>\.conan2\p\fmtdb20aad6e1bd4\p\include -external:IC:\Users\<USER>\.conan2\p\range0301bf3d76d5d\p\include -external:IC:\Users\<USER>\.conan2\p\b\clippf8ddcd4a5d961\p\include -external:IC:\Users\<USER>\.conan2\p\mapbo34ee9aeb574f9\p\include -external:IC:\Users\<USER>\.conan2\p\mapbofe72fb50cf7c4\p\include -external:IC:\Users\<USER>\.conan2\p\mapbo1b62b48748b82\p\include -external:IC:\Users\<USER>\.conan2\p\rapidf7a3355ba53c4\p\include -external:IC:\Users\<USER>\.conan2\p\stb6342cecb318f5\p\include -external:IC:\Users\<USER>\.conan2\p\boosta79d738920bc3\p\include -external:IC:\Users\<USER>\.conan2\p\scrip4cfd014ad4660\p\include -external:IC:\Users\<USER>\.conan2\p\nearg388d58da7a54c\p\include -external:IC:\Users\<USER>\.conan2\p\b\curaebb245e64b5ac7\p\include -external:IC:\Users\<USER>\.conan2\p\asio-c31dc2a380bf0\p\include -external:IC:\Users\<USER>\.conan2\p\grpc8b6c73b4e5756\p\include -external:IC:\Users\<USER>\.conan2\p\absei6a15444d60d47\p\include -external:W0
  OBJECT_DIR = CMakeFiles\_CuraEngine.dir
  OBJECT_FILE_DIR = CMakeFiles\_CuraEngine.dir\src
  TARGET_COMPILE_PDB = CMakeFiles\_CuraEngine.dir\_CuraEngine.pdb
  TARGET_PDB = _CuraEngine.pdb

build CMakeFiles\_CuraEngine.dir\src\FffPolygonGenerator.cpp.obj: CXX_COMPILER___CuraEngine_unscanned_Release C$:\Users\wsd07\vscode\Cura-Dev\CuraEngine\src\FffPolygonGenerator.cpp || cmake_object_order_depends_target__CuraEngine
  CONFIG = Release
  DEFINES = -DAGRPC_BOOST_ASIO -DARCUS -DBOOST_ALL_NO_LIB -DBOOST_STACKTRACE_USE_NOOP -DBOOST_STACKTRACE_USE_WINDBG -DBOOST_STACKTRACE_USE_WINDBG_CACHED -DCARES_STATICLIB -DCURA_ENGINE_HASH=\"unknown\" -DCURA_ENGINE_VERSION=\"5.11.0-alpha.0\" -DENABLE_PLUGINS -DNOMINMAX -DSENTRY_ENVIRONMENT=\"development\" -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSTB_TEXTEDIT_KEYTYPE=unsigned
  FLAGS = /DWIN32 /D_WINDOWS /EHsc /O2 /Ob2 /DNDEBUG -std:c++20 -MD /utf-8 /permissive-
  INCLUDES = -IC:\Users\<USER>\vscode\Cura-Dev\CuraEngine\include -IC:\Users\<USER>\vscode\Cura-Dev\CuraEngine\build\Release -external:IC:\Users\<USER>\.conan2\p\b\arcusee7666f716199\p\include -external:IC:\Users\<USER>\.conan2\p\proto47a676cb9257b\p\include -external:IC:\Users\<USER>\.conan2\p\zlibcfb9789dc2a53\p\include -external:IC:\Users\<USER>\.conan2\p\spdlo2d4b540ab0f22\p\include -external:IC:\Users\<USER>\.conan2\p\fmtdb20aad6e1bd4\p\include -external:IC:\Users\<USER>\.conan2\p\range0301bf3d76d5d\p\include -external:IC:\Users\<USER>\.conan2\p\b\clippf8ddcd4a5d961\p\include -external:IC:\Users\<USER>\.conan2\p\mapbo34ee9aeb574f9\p\include -external:IC:\Users\<USER>\.conan2\p\mapbofe72fb50cf7c4\p\include -external:IC:\Users\<USER>\.conan2\p\mapbo1b62b48748b82\p\include -external:IC:\Users\<USER>\.conan2\p\rapidf7a3355ba53c4\p\include -external:IC:\Users\<USER>\.conan2\p\stb6342cecb318f5\p\include -external:IC:\Users\<USER>\.conan2\p\boosta79d738920bc3\p\include -external:IC:\Users\<USER>\.conan2\p\scrip4cfd014ad4660\p\include -external:IC:\Users\<USER>\.conan2\p\nearg388d58da7a54c\p\include -external:IC:\Users\<USER>\.conan2\p\b\curaebb245e64b5ac7\p\include -external:IC:\Users\<USER>\.conan2\p\asio-c31dc2a380bf0\p\include -external:IC:\Users\<USER>\.conan2\p\grpc8b6c73b4e5756\p\include -external:IC:\Users\<USER>\.conan2\p\absei6a15444d60d47\p\include -external:W0
  OBJECT_DIR = CMakeFiles\_CuraEngine.dir
  OBJECT_FILE_DIR = CMakeFiles\_CuraEngine.dir\src
  TARGET_COMPILE_PDB = CMakeFiles\_CuraEngine.dir\_CuraEngine.pdb
  TARGET_PDB = _CuraEngine.pdb

build CMakeFiles\_CuraEngine.dir\src\FffProcessor.cpp.obj: CXX_COMPILER___CuraEngine_unscanned_Release C$:\Users\wsd07\vscode\Cura-Dev\CuraEngine\src\FffProcessor.cpp || cmake_object_order_depends_target__CuraEngine
  CONFIG = Release
  DEFINES = -DAGRPC_BOOST_ASIO -DARCUS -DBOOST_ALL_NO_LIB -DBOOST_STACKTRACE_USE_NOOP -DBOOST_STACKTRACE_USE_WINDBG -DBOOST_STACKTRACE_USE_WINDBG_CACHED -DCARES_STATICLIB -DCURA_ENGINE_HASH=\"unknown\" -DCURA_ENGINE_VERSION=\"5.11.0-alpha.0\" -DENABLE_PLUGINS -DNOMINMAX -DSENTRY_ENVIRONMENT=\"development\" -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSTB_TEXTEDIT_KEYTYPE=unsigned
  FLAGS = /DWIN32 /D_WINDOWS /EHsc /O2 /Ob2 /DNDEBUG -std:c++20 -MD /utf-8 /permissive-
  INCLUDES = -IC:\Users\<USER>\vscode\Cura-Dev\CuraEngine\include -IC:\Users\<USER>\vscode\Cura-Dev\CuraEngine\build\Release -external:IC:\Users\<USER>\.conan2\p\b\arcusee7666f716199\p\include -external:IC:\Users\<USER>\.conan2\p\proto47a676cb9257b\p\include -external:IC:\Users\<USER>\.conan2\p\zlibcfb9789dc2a53\p\include -external:IC:\Users\<USER>\.conan2\p\spdlo2d4b540ab0f22\p\include -external:IC:\Users\<USER>\.conan2\p\fmtdb20aad6e1bd4\p\include -external:IC:\Users\<USER>\.conan2\p\range0301bf3d76d5d\p\include -external:IC:\Users\<USER>\.conan2\p\b\clippf8ddcd4a5d961\p\include -external:IC:\Users\<USER>\.conan2\p\mapbo34ee9aeb574f9\p\include -external:IC:\Users\<USER>\.conan2\p\mapbofe72fb50cf7c4\p\include -external:IC:\Users\<USER>\.conan2\p\mapbo1b62b48748b82\p\include -external:IC:\Users\<USER>\.conan2\p\rapidf7a3355ba53c4\p\include -external:IC:\Users\<USER>\.conan2\p\stb6342cecb318f5\p\include -external:IC:\Users\<USER>\.conan2\p\boosta79d738920bc3\p\include -external:IC:\Users\<USER>\.conan2\p\scrip4cfd014ad4660\p\include -external:IC:\Users\<USER>\.conan2\p\nearg388d58da7a54c\p\include -external:IC:\Users\<USER>\.conan2\p\b\curaebb245e64b5ac7\p\include -external:IC:\Users\<USER>\.conan2\p\asio-c31dc2a380bf0\p\include -external:IC:\Users\<USER>\.conan2\p\grpc8b6c73b4e5756\p\include -external:IC:\Users\<USER>\.conan2\p\absei6a15444d60d47\p\include -external:W0
  OBJECT_DIR = CMakeFiles\_CuraEngine.dir
  OBJECT_FILE_DIR = CMakeFiles\_CuraEngine.dir\src
  TARGET_COMPILE_PDB = CMakeFiles\_CuraEngine.dir\_CuraEngine.pdb
  TARGET_PDB = _CuraEngine.pdb

build CMakeFiles\_CuraEngine.dir\src\gcodeExport.cpp.obj: CXX_COMPILER___CuraEngine_unscanned_Release C$:\Users\wsd07\vscode\Cura-Dev\CuraEngine\src\gcodeExport.cpp || cmake_object_order_depends_target__CuraEngine
  CONFIG = Release
  DEFINES = -DAGRPC_BOOST_ASIO -DARCUS -DBOOST_ALL_NO_LIB -DBOOST_STACKTRACE_USE_NOOP -DBOOST_STACKTRACE_USE_WINDBG -DBOOST_STACKTRACE_USE_WINDBG_CACHED -DCARES_STATICLIB -DCURA_ENGINE_HASH=\"unknown\" -DCURA_ENGINE_VERSION=\"5.11.0-alpha.0\" -DENABLE_PLUGINS -DNOMINMAX -DSENTRY_ENVIRONMENT=\"development\" -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSTB_TEXTEDIT_KEYTYPE=unsigned
  FLAGS = /DWIN32 /D_WINDOWS /EHsc /O2 /Ob2 /DNDEBUG -std:c++20 -MD /utf-8 /permissive-
  INCLUDES = -IC:\Users\<USER>\vscode\Cura-Dev\CuraEngine\include -IC:\Users\<USER>\vscode\Cura-Dev\CuraEngine\build\Release -external:IC:\Users\<USER>\.conan2\p\b\arcusee7666f716199\p\include -external:IC:\Users\<USER>\.conan2\p\proto47a676cb9257b\p\include -external:IC:\Users\<USER>\.conan2\p\zlibcfb9789dc2a53\p\include -external:IC:\Users\<USER>\.conan2\p\spdlo2d4b540ab0f22\p\include -external:IC:\Users\<USER>\.conan2\p\fmtdb20aad6e1bd4\p\include -external:IC:\Users\<USER>\.conan2\p\range0301bf3d76d5d\p\include -external:IC:\Users\<USER>\.conan2\p\b\clippf8ddcd4a5d961\p\include -external:IC:\Users\<USER>\.conan2\p\mapbo34ee9aeb574f9\p\include -external:IC:\Users\<USER>\.conan2\p\mapbofe72fb50cf7c4\p\include -external:IC:\Users\<USER>\.conan2\p\mapbo1b62b48748b82\p\include -external:IC:\Users\<USER>\.conan2\p\rapidf7a3355ba53c4\p\include -external:IC:\Users\<USER>\.conan2\p\stb6342cecb318f5\p\include -external:IC:\Users\<USER>\.conan2\p\boosta79d738920bc3\p\include -external:IC:\Users\<USER>\.conan2\p\scrip4cfd014ad4660\p\include -external:IC:\Users\<USER>\.conan2\p\nearg388d58da7a54c\p\include -external:IC:\Users\<USER>\.conan2\p\b\curaebb245e64b5ac7\p\include -external:IC:\Users\<USER>\.conan2\p\asio-c31dc2a380bf0\p\include -external:IC:\Users\<USER>\.conan2\p\grpc8b6c73b4e5756\p\include -external:IC:\Users\<USER>\.conan2\p\absei6a15444d60d47\p\include -external:W0
  OBJECT_DIR = CMakeFiles\_CuraEngine.dir
  OBJECT_FILE_DIR = CMakeFiles\_CuraEngine.dir\src
  TARGET_COMPILE_PDB = CMakeFiles\_CuraEngine.dir\_CuraEngine.pdb
  TARGET_PDB = _CuraEngine.pdb

build CMakeFiles\_CuraEngine.dir\src\GCodePathConfig.cpp.obj: CXX_COMPILER___CuraEngine_unscanned_Release C$:\Users\wsd07\vscode\Cura-Dev\CuraEngine\src\GCodePathConfig.cpp || cmake_object_order_depends_target__CuraEngine
  CONFIG = Release
  DEFINES = -DAGRPC_BOOST_ASIO -DARCUS -DBOOST_ALL_NO_LIB -DBOOST_STACKTRACE_USE_NOOP -DBOOST_STACKTRACE_USE_WINDBG -DBOOST_STACKTRACE_USE_WINDBG_CACHED -DCARES_STATICLIB -DCURA_ENGINE_HASH=\"unknown\" -DCURA_ENGINE_VERSION=\"5.11.0-alpha.0\" -DENABLE_PLUGINS -DNOMINMAX -DSENTRY_ENVIRONMENT=\"development\" -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSTB_TEXTEDIT_KEYTYPE=unsigned
  FLAGS = /DWIN32 /D_WINDOWS /EHsc /O2 /Ob2 /DNDEBUG -std:c++20 -MD /utf-8 /permissive-
  INCLUDES = -IC:\Users\<USER>\vscode\Cura-Dev\CuraEngine\include -IC:\Users\<USER>\vscode\Cura-Dev\CuraEngine\build\Release -external:IC:\Users\<USER>\.conan2\p\b\arcusee7666f716199\p\include -external:IC:\Users\<USER>\.conan2\p\proto47a676cb9257b\p\include -external:IC:\Users\<USER>\.conan2\p\zlibcfb9789dc2a53\p\include -external:IC:\Users\<USER>\.conan2\p\spdlo2d4b540ab0f22\p\include -external:IC:\Users\<USER>\.conan2\p\fmtdb20aad6e1bd4\p\include -external:IC:\Users\<USER>\.conan2\p\range0301bf3d76d5d\p\include -external:IC:\Users\<USER>\.conan2\p\b\clippf8ddcd4a5d961\p\include -external:IC:\Users\<USER>\.conan2\p\mapbo34ee9aeb574f9\p\include -external:IC:\Users\<USER>\.conan2\p\mapbofe72fb50cf7c4\p\include -external:IC:\Users\<USER>\.conan2\p\mapbo1b62b48748b82\p\include -external:IC:\Users\<USER>\.conan2\p\rapidf7a3355ba53c4\p\include -external:IC:\Users\<USER>\.conan2\p\stb6342cecb318f5\p\include -external:IC:\Users\<USER>\.conan2\p\boosta79d738920bc3\p\include -external:IC:\Users\<USER>\.conan2\p\scrip4cfd014ad4660\p\include -external:IC:\Users\<USER>\.conan2\p\nearg388d58da7a54c\p\include -external:IC:\Users\<USER>\.conan2\p\b\curaebb245e64b5ac7\p\include -external:IC:\Users\<USER>\.conan2\p\asio-c31dc2a380bf0\p\include -external:IC:\Users\<USER>\.conan2\p\grpc8b6c73b4e5756\p\include -external:IC:\Users\<USER>\.conan2\p\absei6a15444d60d47\p\include -external:W0
  OBJECT_DIR = CMakeFiles\_CuraEngine.dir
  OBJECT_FILE_DIR = CMakeFiles\_CuraEngine.dir\src
  TARGET_COMPILE_PDB = CMakeFiles\_CuraEngine.dir\_CuraEngine.pdb
  TARGET_PDB = _CuraEngine.pdb

build CMakeFiles\_CuraEngine.dir\src\infill.cpp.obj: CXX_COMPILER___CuraEngine_unscanned_Release C$:\Users\wsd07\vscode\Cura-Dev\CuraEngine\src\infill.cpp || cmake_object_order_depends_target__CuraEngine
  CONFIG = Release
  DEFINES = -DAGRPC_BOOST_ASIO -DARCUS -DBOOST_ALL_NO_LIB -DBOOST_STACKTRACE_USE_NOOP -DBOOST_STACKTRACE_USE_WINDBG -DBOOST_STACKTRACE_USE_WINDBG_CACHED -DCARES_STATICLIB -DCURA_ENGINE_HASH=\"unknown\" -DCURA_ENGINE_VERSION=\"5.11.0-alpha.0\" -DENABLE_PLUGINS -DNOMINMAX -DSENTRY_ENVIRONMENT=\"development\" -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSTB_TEXTEDIT_KEYTYPE=unsigned
  FLAGS = /DWIN32 /D_WINDOWS /EHsc /O2 /Ob2 /DNDEBUG -std:c++20 -MD /utf-8 /permissive-
  INCLUDES = -IC:\Users\<USER>\vscode\Cura-Dev\CuraEngine\include -IC:\Users\<USER>\vscode\Cura-Dev\CuraEngine\build\Release -external:IC:\Users\<USER>\.conan2\p\b\arcusee7666f716199\p\include -external:IC:\Users\<USER>\.conan2\p\proto47a676cb9257b\p\include -external:IC:\Users\<USER>\.conan2\p\zlibcfb9789dc2a53\p\include -external:IC:\Users\<USER>\.conan2\p\spdlo2d4b540ab0f22\p\include -external:IC:\Users\<USER>\.conan2\p\fmtdb20aad6e1bd4\p\include -external:IC:\Users\<USER>\.conan2\p\range0301bf3d76d5d\p\include -external:IC:\Users\<USER>\.conan2\p\b\clippf8ddcd4a5d961\p\include -external:IC:\Users\<USER>\.conan2\p\mapbo34ee9aeb574f9\p\include -external:IC:\Users\<USER>\.conan2\p\mapbofe72fb50cf7c4\p\include -external:IC:\Users\<USER>\.conan2\p\mapbo1b62b48748b82\p\include -external:IC:\Users\<USER>\.conan2\p\rapidf7a3355ba53c4\p\include -external:IC:\Users\<USER>\.conan2\p\stb6342cecb318f5\p\include -external:IC:\Users\<USER>\.conan2\p\boosta79d738920bc3\p\include -external:IC:\Users\<USER>\.conan2\p\scrip4cfd014ad4660\p\include -external:IC:\Users\<USER>\.conan2\p\nearg388d58da7a54c\p\include -external:IC:\Users\<USER>\.conan2\p\b\curaebb245e64b5ac7\p\include -external:IC:\Users\<USER>\.conan2\p\asio-c31dc2a380bf0\p\include -external:IC:\Users\<USER>\.conan2\p\grpc8b6c73b4e5756\p\include -external:IC:\Users\<USER>\.conan2\p\absei6a15444d60d47\p\include -external:W0
  OBJECT_DIR = CMakeFiles\_CuraEngine.dir
  OBJECT_FILE_DIR = CMakeFiles\_CuraEngine.dir\src
  TARGET_COMPILE_PDB = CMakeFiles\_CuraEngine.dir\_CuraEngine.pdb
  TARGET_PDB = _CuraEngine.pdb

build CMakeFiles\_CuraEngine.dir\src\InterlockingGenerator.cpp.obj: CXX_COMPILER___CuraEngine_unscanned_Release C$:\Users\wsd07\vscode\Cura-Dev\CuraEngine\src\InterlockingGenerator.cpp || cmake_object_order_depends_target__CuraEngine
  CONFIG = Release
  DEFINES = -DAGRPC_BOOST_ASIO -DARCUS -DBOOST_ALL_NO_LIB -DBOOST_STACKTRACE_USE_NOOP -DBOOST_STACKTRACE_USE_WINDBG -DBOOST_STACKTRACE_USE_WINDBG_CACHED -DCARES_STATICLIB -DCURA_ENGINE_HASH=\"unknown\" -DCURA_ENGINE_VERSION=\"5.11.0-alpha.0\" -DENABLE_PLUGINS -DNOMINMAX -DSENTRY_ENVIRONMENT=\"development\" -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSTB_TEXTEDIT_KEYTYPE=unsigned
  FLAGS = /DWIN32 /D_WINDOWS /EHsc /O2 /Ob2 /DNDEBUG -std:c++20 -MD /utf-8 /permissive-
  INCLUDES = -IC:\Users\<USER>\vscode\Cura-Dev\CuraEngine\include -IC:\Users\<USER>\vscode\Cura-Dev\CuraEngine\build\Release -external:IC:\Users\<USER>\.conan2\p\b\arcusee7666f716199\p\include -external:IC:\Users\<USER>\.conan2\p\proto47a676cb9257b\p\include -external:IC:\Users\<USER>\.conan2\p\zlibcfb9789dc2a53\p\include -external:IC:\Users\<USER>\.conan2\p\spdlo2d4b540ab0f22\p\include -external:IC:\Users\<USER>\.conan2\p\fmtdb20aad6e1bd4\p\include -external:IC:\Users\<USER>\.conan2\p\range0301bf3d76d5d\p\include -external:IC:\Users\<USER>\.conan2\p\b\clippf8ddcd4a5d961\p\include -external:IC:\Users\<USER>\.conan2\p\mapbo34ee9aeb574f9\p\include -external:IC:\Users\<USER>\.conan2\p\mapbofe72fb50cf7c4\p\include -external:IC:\Users\<USER>\.conan2\p\mapbo1b62b48748b82\p\include -external:IC:\Users\<USER>\.conan2\p\rapidf7a3355ba53c4\p\include -external:IC:\Users\<USER>\.conan2\p\stb6342cecb318f5\p\include -external:IC:\Users\<USER>\.conan2\p\boosta79d738920bc3\p\include -external:IC:\Users\<USER>\.conan2\p\scrip4cfd014ad4660\p\include -external:IC:\Users\<USER>\.conan2\p\nearg388d58da7a54c\p\include -external:IC:\Users\<USER>\.conan2\p\b\curaebb245e64b5ac7\p\include -external:IC:\Users\<USER>\.conan2\p\asio-c31dc2a380bf0\p\include -external:IC:\Users\<USER>\.conan2\p\grpc8b6c73b4e5756\p\include -external:IC:\Users\<USER>\.conan2\p\absei6a15444d60d47\p\include -external:W0
  OBJECT_DIR = CMakeFiles\_CuraEngine.dir
  OBJECT_FILE_DIR = CMakeFiles\_CuraEngine.dir\src
  TARGET_COMPILE_PDB = CMakeFiles\_CuraEngine.dir\_CuraEngine.pdb
  TARGET_PDB = _CuraEngine.pdb

build CMakeFiles\_CuraEngine.dir\src\InsetOrderOptimizer.cpp.obj: CXX_COMPILER___CuraEngine_unscanned_Release C$:\Users\wsd07\vscode\Cura-Dev\CuraEngine\src\InsetOrderOptimizer.cpp || cmake_object_order_depends_target__CuraEngine
  CONFIG = Release
  DEFINES = -DAGRPC_BOOST_ASIO -DARCUS -DBOOST_ALL_NO_LIB -DBOOST_STACKTRACE_USE_NOOP -DBOOST_STACKTRACE_USE_WINDBG -DBOOST_STACKTRACE_USE_WINDBG_CACHED -DCARES_STATICLIB -DCURA_ENGINE_HASH=\"unknown\" -DCURA_ENGINE_VERSION=\"5.11.0-alpha.0\" -DENABLE_PLUGINS -DNOMINMAX -DSENTRY_ENVIRONMENT=\"development\" -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSTB_TEXTEDIT_KEYTYPE=unsigned
  FLAGS = /DWIN32 /D_WINDOWS /EHsc /O2 /Ob2 /DNDEBUG -std:c++20 -MD /utf-8 /permissive-
  INCLUDES = -IC:\Users\<USER>\vscode\Cura-Dev\CuraEngine\include -IC:\Users\<USER>\vscode\Cura-Dev\CuraEngine\build\Release -external:IC:\Users\<USER>\.conan2\p\b\arcusee7666f716199\p\include -external:IC:\Users\<USER>\.conan2\p\proto47a676cb9257b\p\include -external:IC:\Users\<USER>\.conan2\p\zlibcfb9789dc2a53\p\include -external:IC:\Users\<USER>\.conan2\p\spdlo2d4b540ab0f22\p\include -external:IC:\Users\<USER>\.conan2\p\fmtdb20aad6e1bd4\p\include -external:IC:\Users\<USER>\.conan2\p\range0301bf3d76d5d\p\include -external:IC:\Users\<USER>\.conan2\p\b\clippf8ddcd4a5d961\p\include -external:IC:\Users\<USER>\.conan2\p\mapbo34ee9aeb574f9\p\include -external:IC:\Users\<USER>\.conan2\p\mapbofe72fb50cf7c4\p\include -external:IC:\Users\<USER>\.conan2\p\mapbo1b62b48748b82\p\include -external:IC:\Users\<USER>\.conan2\p\rapidf7a3355ba53c4\p\include -external:IC:\Users\<USER>\.conan2\p\stb6342cecb318f5\p\include -external:IC:\Users\<USER>\.conan2\p\boosta79d738920bc3\p\include -external:IC:\Users\<USER>\.conan2\p\scrip4cfd014ad4660\p\include -external:IC:\Users\<USER>\.conan2\p\nearg388d58da7a54c\p\include -external:IC:\Users\<USER>\.conan2\p\b\curaebb245e64b5ac7\p\include -external:IC:\Users\<USER>\.conan2\p\asio-c31dc2a380bf0\p\include -external:IC:\Users\<USER>\.conan2\p\grpc8b6c73b4e5756\p\include -external:IC:\Users\<USER>\.conan2\p\absei6a15444d60d47\p\include -external:W0
  OBJECT_DIR = CMakeFiles\_CuraEngine.dir
  OBJECT_FILE_DIR = CMakeFiles\_CuraEngine.dir\src
  TARGET_COMPILE_PDB = CMakeFiles\_CuraEngine.dir\_CuraEngine.pdb
  TARGET_PDB = _CuraEngine.pdb

build CMakeFiles\_CuraEngine.dir\src\layerPart.cpp.obj: CXX_COMPILER___CuraEngine_unscanned_Release C$:\Users\wsd07\vscode\Cura-Dev\CuraEngine\src\layerPart.cpp || cmake_object_order_depends_target__CuraEngine
  CONFIG = Release
  DEFINES = -DAGRPC_BOOST_ASIO -DARCUS -DBOOST_ALL_NO_LIB -DBOOST_STACKTRACE_USE_NOOP -DBOOST_STACKTRACE_USE_WINDBG -DBOOST_STACKTRACE_USE_WINDBG_CACHED -DCARES_STATICLIB -DCURA_ENGINE_HASH=\"unknown\" -DCURA_ENGINE_VERSION=\"5.11.0-alpha.0\" -DENABLE_PLUGINS -DNOMINMAX -DSENTRY_ENVIRONMENT=\"development\" -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSTB_TEXTEDIT_KEYTYPE=unsigned
  FLAGS = /DWIN32 /D_WINDOWS /EHsc /O2 /Ob2 /DNDEBUG -std:c++20 -MD /utf-8 /permissive-
  INCLUDES = -IC:\Users\<USER>\vscode\Cura-Dev\CuraEngine\include -IC:\Users\<USER>\vscode\Cura-Dev\CuraEngine\build\Release -external:IC:\Users\<USER>\.conan2\p\b\arcusee7666f716199\p\include -external:IC:\Users\<USER>\.conan2\p\proto47a676cb9257b\p\include -external:IC:\Users\<USER>\.conan2\p\zlibcfb9789dc2a53\p\include -external:IC:\Users\<USER>\.conan2\p\spdlo2d4b540ab0f22\p\include -external:IC:\Users\<USER>\.conan2\p\fmtdb20aad6e1bd4\p\include -external:IC:\Users\<USER>\.conan2\p\range0301bf3d76d5d\p\include -external:IC:\Users\<USER>\.conan2\p\b\clippf8ddcd4a5d961\p\include -external:IC:\Users\<USER>\.conan2\p\mapbo34ee9aeb574f9\p\include -external:IC:\Users\<USER>\.conan2\p\mapbofe72fb50cf7c4\p\include -external:IC:\Users\<USER>\.conan2\p\mapbo1b62b48748b82\p\include -external:IC:\Users\<USER>\.conan2\p\rapidf7a3355ba53c4\p\include -external:IC:\Users\<USER>\.conan2\p\stb6342cecb318f5\p\include -external:IC:\Users\<USER>\.conan2\p\boosta79d738920bc3\p\include -external:IC:\Users\<USER>\.conan2\p\scrip4cfd014ad4660\p\include -external:IC:\Users\<USER>\.conan2\p\nearg388d58da7a54c\p\include -external:IC:\Users\<USER>\.conan2\p\b\curaebb245e64b5ac7\p\include -external:IC:\Users\<USER>\.conan2\p\asio-c31dc2a380bf0\p\include -external:IC:\Users\<USER>\.conan2\p\grpc8b6c73b4e5756\p\include -external:IC:\Users\<USER>\.conan2\p\absei6a15444d60d47\p\include -external:W0
  OBJECT_DIR = CMakeFiles\_CuraEngine.dir
  OBJECT_FILE_DIR = CMakeFiles\_CuraEngine.dir\src
  TARGET_COMPILE_PDB = CMakeFiles\_CuraEngine.dir\_CuraEngine.pdb
  TARGET_PDB = _CuraEngine.pdb

build CMakeFiles\_CuraEngine.dir\src\LayerPlan.cpp.obj: CXX_COMPILER___CuraEngine_unscanned_Release C$:\Users\wsd07\vscode\Cura-Dev\CuraEngine\src\LayerPlan.cpp || cmake_object_order_depends_target__CuraEngine
  CONFIG = Release
  DEFINES = -DAGRPC_BOOST_ASIO -DARCUS -DBOOST_ALL_NO_LIB -DBOOST_STACKTRACE_USE_NOOP -DBOOST_STACKTRACE_USE_WINDBG -DBOOST_STACKTRACE_USE_WINDBG_CACHED -DCARES_STATICLIB -DCURA_ENGINE_HASH=\"unknown\" -DCURA_ENGINE_VERSION=\"5.11.0-alpha.0\" -DENABLE_PLUGINS -DNOMINMAX -DSENTRY_ENVIRONMENT=\"development\" -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSTB_TEXTEDIT_KEYTYPE=unsigned
  FLAGS = /DWIN32 /D_WINDOWS /EHsc /O2 /Ob2 /DNDEBUG -std:c++20 -MD /utf-8 /permissive-
  INCLUDES = -IC:\Users\<USER>\vscode\Cura-Dev\CuraEngine\include -IC:\Users\<USER>\vscode\Cura-Dev\CuraEngine\build\Release -external:IC:\Users\<USER>\.conan2\p\b\arcusee7666f716199\p\include -external:IC:\Users\<USER>\.conan2\p\proto47a676cb9257b\p\include -external:IC:\Users\<USER>\.conan2\p\zlibcfb9789dc2a53\p\include -external:IC:\Users\<USER>\.conan2\p\spdlo2d4b540ab0f22\p\include -external:IC:\Users\<USER>\.conan2\p\fmtdb20aad6e1bd4\p\include -external:IC:\Users\<USER>\.conan2\p\range0301bf3d76d5d\p\include -external:IC:\Users\<USER>\.conan2\p\b\clippf8ddcd4a5d961\p\include -external:IC:\Users\<USER>\.conan2\p\mapbo34ee9aeb574f9\p\include -external:IC:\Users\<USER>\.conan2\p\mapbofe72fb50cf7c4\p\include -external:IC:\Users\<USER>\.conan2\p\mapbo1b62b48748b82\p\include -external:IC:\Users\<USER>\.conan2\p\rapidf7a3355ba53c4\p\include -external:IC:\Users\<USER>\.conan2\p\stb6342cecb318f5\p\include -external:IC:\Users\<USER>\.conan2\p\boosta79d738920bc3\p\include -external:IC:\Users\<USER>\.conan2\p\scrip4cfd014ad4660\p\include -external:IC:\Users\<USER>\.conan2\p\nearg388d58da7a54c\p\include -external:IC:\Users\<USER>\.conan2\p\b\curaebb245e64b5ac7\p\include -external:IC:\Users\<USER>\.conan2\p\asio-c31dc2a380bf0\p\include -external:IC:\Users\<USER>\.conan2\p\grpc8b6c73b4e5756\p\include -external:IC:\Users\<USER>\.conan2\p\absei6a15444d60d47\p\include -external:W0
  OBJECT_DIR = CMakeFiles\_CuraEngine.dir
  OBJECT_FILE_DIR = CMakeFiles\_CuraEngine.dir\src
  TARGET_COMPILE_PDB = CMakeFiles\_CuraEngine.dir\_CuraEngine.pdb
  TARGET_PDB = _CuraEngine.pdb

build CMakeFiles\_CuraEngine.dir\src\LayerPlanBuffer.cpp.obj: CXX_COMPILER___CuraEngine_unscanned_Release C$:\Users\wsd07\vscode\Cura-Dev\CuraEngine\src\LayerPlanBuffer.cpp || cmake_object_order_depends_target__CuraEngine
  CONFIG = Release
  DEFINES = -DAGRPC_BOOST_ASIO -DARCUS -DBOOST_ALL_NO_LIB -DBOOST_STACKTRACE_USE_NOOP -DBOOST_STACKTRACE_USE_WINDBG -DBOOST_STACKTRACE_USE_WINDBG_CACHED -DCARES_STATICLIB -DCURA_ENGINE_HASH=\"unknown\" -DCURA_ENGINE_VERSION=\"5.11.0-alpha.0\" -DENABLE_PLUGINS -DNOMINMAX -DSENTRY_ENVIRONMENT=\"development\" -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSTB_TEXTEDIT_KEYTYPE=unsigned
  FLAGS = /DWIN32 /D_WINDOWS /EHsc /O2 /Ob2 /DNDEBUG -std:c++20 -MD /utf-8 /permissive-
  INCLUDES = -IC:\Users\<USER>\vscode\Cura-Dev\CuraEngine\include -IC:\Users\<USER>\vscode\Cura-Dev\CuraEngine\build\Release -external:IC:\Users\<USER>\.conan2\p\b\arcusee7666f716199\p\include -external:IC:\Users\<USER>\.conan2\p\proto47a676cb9257b\p\include -external:IC:\Users\<USER>\.conan2\p\zlibcfb9789dc2a53\p\include -external:IC:\Users\<USER>\.conan2\p\spdlo2d4b540ab0f22\p\include -external:IC:\Users\<USER>\.conan2\p\fmtdb20aad6e1bd4\p\include -external:IC:\Users\<USER>\.conan2\p\range0301bf3d76d5d\p\include -external:IC:\Users\<USER>\.conan2\p\b\clippf8ddcd4a5d961\p\include -external:IC:\Users\<USER>\.conan2\p\mapbo34ee9aeb574f9\p\include -external:IC:\Users\<USER>\.conan2\p\mapbofe72fb50cf7c4\p\include -external:IC:\Users\<USER>\.conan2\p\mapbo1b62b48748b82\p\include -external:IC:\Users\<USER>\.conan2\p\rapidf7a3355ba53c4\p\include -external:IC:\Users\<USER>\.conan2\p\stb6342cecb318f5\p\include -external:IC:\Users\<USER>\.conan2\p\boosta79d738920bc3\p\include -external:IC:\Users\<USER>\.conan2\p\scrip4cfd014ad4660\p\include -external:IC:\Users\<USER>\.conan2\p\nearg388d58da7a54c\p\include -external:IC:\Users\<USER>\.conan2\p\b\curaebb245e64b5ac7\p\include -external:IC:\Users\<USER>\.conan2\p\asio-c31dc2a380bf0\p\include -external:IC:\Users\<USER>\.conan2\p\grpc8b6c73b4e5756\p\include -external:IC:\Users\<USER>\.conan2\p\absei6a15444d60d47\p\include -external:W0
  OBJECT_DIR = CMakeFiles\_CuraEngine.dir
  OBJECT_FILE_DIR = CMakeFiles\_CuraEngine.dir\src
  TARGET_COMPILE_PDB = CMakeFiles\_CuraEngine.dir\_CuraEngine.pdb
  TARGET_PDB = _CuraEngine.pdb

build CMakeFiles\_CuraEngine.dir\src\mesh.cpp.obj: CXX_COMPILER___CuraEngine_unscanned_Release C$:\Users\wsd07\vscode\Cura-Dev\CuraEngine\src\mesh.cpp || cmake_object_order_depends_target__CuraEngine
  CONFIG = Release
  DEFINES = -DAGRPC_BOOST_ASIO -DARCUS -DBOOST_ALL_NO_LIB -DBOOST_STACKTRACE_USE_NOOP -DBOOST_STACKTRACE_USE_WINDBG -DBOOST_STACKTRACE_USE_WINDBG_CACHED -DCARES_STATICLIB -DCURA_ENGINE_HASH=\"unknown\" -DCURA_ENGINE_VERSION=\"5.11.0-alpha.0\" -DENABLE_PLUGINS -DNOMINMAX -DSENTRY_ENVIRONMENT=\"development\" -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSTB_TEXTEDIT_KEYTYPE=unsigned
  FLAGS = /DWIN32 /D_WINDOWS /EHsc /O2 /Ob2 /DNDEBUG -std:c++20 -MD /utf-8 /permissive-
  INCLUDES = -IC:\Users\<USER>\vscode\Cura-Dev\CuraEngine\include -IC:\Users\<USER>\vscode\Cura-Dev\CuraEngine\build\Release -external:IC:\Users\<USER>\.conan2\p\b\arcusee7666f716199\p\include -external:IC:\Users\<USER>\.conan2\p\proto47a676cb9257b\p\include -external:IC:\Users\<USER>\.conan2\p\zlibcfb9789dc2a53\p\include -external:IC:\Users\<USER>\.conan2\p\spdlo2d4b540ab0f22\p\include -external:IC:\Users\<USER>\.conan2\p\fmtdb20aad6e1bd4\p\include -external:IC:\Users\<USER>\.conan2\p\range0301bf3d76d5d\p\include -external:IC:\Users\<USER>\.conan2\p\b\clippf8ddcd4a5d961\p\include -external:IC:\Users\<USER>\.conan2\p\mapbo34ee9aeb574f9\p\include -external:IC:\Users\<USER>\.conan2\p\mapbofe72fb50cf7c4\p\include -external:IC:\Users\<USER>\.conan2\p\mapbo1b62b48748b82\p\include -external:IC:\Users\<USER>\.conan2\p\rapidf7a3355ba53c4\p\include -external:IC:\Users\<USER>\.conan2\p\stb6342cecb318f5\p\include -external:IC:\Users\<USER>\.conan2\p\boosta79d738920bc3\p\include -external:IC:\Users\<USER>\.conan2\p\scrip4cfd014ad4660\p\include -external:IC:\Users\<USER>\.conan2\p\nearg388d58da7a54c\p\include -external:IC:\Users\<USER>\.conan2\p\b\curaebb245e64b5ac7\p\include -external:IC:\Users\<USER>\.conan2\p\asio-c31dc2a380bf0\p\include -external:IC:\Users\<USER>\.conan2\p\grpc8b6c73b4e5756\p\include -external:IC:\Users\<USER>\.conan2\p\absei6a15444d60d47\p\include -external:W0
  OBJECT_DIR = CMakeFiles\_CuraEngine.dir
  OBJECT_FILE_DIR = CMakeFiles\_CuraEngine.dir\src
  TARGET_COMPILE_PDB = CMakeFiles\_CuraEngine.dir\_CuraEngine.pdb
  TARGET_PDB = _CuraEngine.pdb

build CMakeFiles\_CuraEngine.dir\src\MeshGroup.cpp.obj: CXX_COMPILER___CuraEngine_unscanned_Release C$:\Users\wsd07\vscode\Cura-Dev\CuraEngine\src\MeshGroup.cpp || cmake_object_order_depends_target__CuraEngine
  CONFIG = Release
  DEFINES = -DAGRPC_BOOST_ASIO -DARCUS -DBOOST_ALL_NO_LIB -DBOOST_STACKTRACE_USE_NOOP -DBOOST_STACKTRACE_USE_WINDBG -DBOOST_STACKTRACE_USE_WINDBG_CACHED -DCARES_STATICLIB -DCURA_ENGINE_HASH=\"unknown\" -DCURA_ENGINE_VERSION=\"5.11.0-alpha.0\" -DENABLE_PLUGINS -DNOMINMAX -DSENTRY_ENVIRONMENT=\"development\" -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSTB_TEXTEDIT_KEYTYPE=unsigned
  FLAGS = /DWIN32 /D_WINDOWS /EHsc /O2 /Ob2 /DNDEBUG -std:c++20 -MD /utf-8 /permissive-
  INCLUDES = -IC:\Users\<USER>\vscode\Cura-Dev\CuraEngine\include -IC:\Users\<USER>\vscode\Cura-Dev\CuraEngine\build\Release -external:IC:\Users\<USER>\.conan2\p\b\arcusee7666f716199\p\include -external:IC:\Users\<USER>\.conan2\p\proto47a676cb9257b\p\include -external:IC:\Users\<USER>\.conan2\p\zlibcfb9789dc2a53\p\include -external:IC:\Users\<USER>\.conan2\p\spdlo2d4b540ab0f22\p\include -external:IC:\Users\<USER>\.conan2\p\fmtdb20aad6e1bd4\p\include -external:IC:\Users\<USER>\.conan2\p\range0301bf3d76d5d\p\include -external:IC:\Users\<USER>\.conan2\p\b\clippf8ddcd4a5d961\p\include -external:IC:\Users\<USER>\.conan2\p\mapbo34ee9aeb574f9\p\include -external:IC:\Users\<USER>\.conan2\p\mapbofe72fb50cf7c4\p\include -external:IC:\Users\<USER>\.conan2\p\mapbo1b62b48748b82\p\include -external:IC:\Users\<USER>\.conan2\p\rapidf7a3355ba53c4\p\include -external:IC:\Users\<USER>\.conan2\p\stb6342cecb318f5\p\include -external:IC:\Users\<USER>\.conan2\p\boosta79d738920bc3\p\include -external:IC:\Users\<USER>\.conan2\p\scrip4cfd014ad4660\p\include -external:IC:\Users\<USER>\.conan2\p\nearg388d58da7a54c\p\include -external:IC:\Users\<USER>\.conan2\p\b\curaebb245e64b5ac7\p\include -external:IC:\Users\<USER>\.conan2\p\asio-c31dc2a380bf0\p\include -external:IC:\Users\<USER>\.conan2\p\grpc8b6c73b4e5756\p\include -external:IC:\Users\<USER>\.conan2\p\absei6a15444d60d47\p\include -external:W0
  OBJECT_DIR = CMakeFiles\_CuraEngine.dir
  OBJECT_FILE_DIR = CMakeFiles\_CuraEngine.dir\src
  TARGET_COMPILE_PDB = CMakeFiles\_CuraEngine.dir\_CuraEngine.pdb
  TARGET_PDB = _CuraEngine.pdb

build CMakeFiles\_CuraEngine.dir\src\Mold.cpp.obj: CXX_COMPILER___CuraEngine_unscanned_Release C$:\Users\wsd07\vscode\Cura-Dev\CuraEngine\src\Mold.cpp || cmake_object_order_depends_target__CuraEngine
  CONFIG = Release
  DEFINES = -DAGRPC_BOOST_ASIO -DARCUS -DBOOST_ALL_NO_LIB -DBOOST_STACKTRACE_USE_NOOP -DBOOST_STACKTRACE_USE_WINDBG -DBOOST_STACKTRACE_USE_WINDBG_CACHED -DCARES_STATICLIB -DCURA_ENGINE_HASH=\"unknown\" -DCURA_ENGINE_VERSION=\"5.11.0-alpha.0\" -DENABLE_PLUGINS -DNOMINMAX -DSENTRY_ENVIRONMENT=\"development\" -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSTB_TEXTEDIT_KEYTYPE=unsigned
  FLAGS = /DWIN32 /D_WINDOWS /EHsc /O2 /Ob2 /DNDEBUG -std:c++20 -MD /utf-8 /permissive-
  INCLUDES = -IC:\Users\<USER>\vscode\Cura-Dev\CuraEngine\include -IC:\Users\<USER>\vscode\Cura-Dev\CuraEngine\build\Release -external:IC:\Users\<USER>\.conan2\p\b\arcusee7666f716199\p\include -external:IC:\Users\<USER>\.conan2\p\proto47a676cb9257b\p\include -external:IC:\Users\<USER>\.conan2\p\zlibcfb9789dc2a53\p\include -external:IC:\Users\<USER>\.conan2\p\spdlo2d4b540ab0f22\p\include -external:IC:\Users\<USER>\.conan2\p\fmtdb20aad6e1bd4\p\include -external:IC:\Users\<USER>\.conan2\p\range0301bf3d76d5d\p\include -external:IC:\Users\<USER>\.conan2\p\b\clippf8ddcd4a5d961\p\include -external:IC:\Users\<USER>\.conan2\p\mapbo34ee9aeb574f9\p\include -external:IC:\Users\<USER>\.conan2\p\mapbofe72fb50cf7c4\p\include -external:IC:\Users\<USER>\.conan2\p\mapbo1b62b48748b82\p\include -external:IC:\Users\<USER>\.conan2\p\rapidf7a3355ba53c4\p\include -external:IC:\Users\<USER>\.conan2\p\stb6342cecb318f5\p\include -external:IC:\Users\<USER>\.conan2\p\boosta79d738920bc3\p\include -external:IC:\Users\<USER>\.conan2\p\scrip4cfd014ad4660\p\include -external:IC:\Users\<USER>\.conan2\p\nearg388d58da7a54c\p\include -external:IC:\Users\<USER>\.conan2\p\b\curaebb245e64b5ac7\p\include -external:IC:\Users\<USER>\.conan2\p\asio-c31dc2a380bf0\p\include -external:IC:\Users\<USER>\.conan2\p\grpc8b6c73b4e5756\p\include -external:IC:\Users\<USER>\.conan2\p\absei6a15444d60d47\p\include -external:W0
  OBJECT_DIR = CMakeFiles\_CuraEngine.dir
  OBJECT_FILE_DIR = CMakeFiles\_CuraEngine.dir\src
  TARGET_COMPILE_PDB = CMakeFiles\_CuraEngine.dir\_CuraEngine.pdb
  TARGET_PDB = _CuraEngine.pdb

build CMakeFiles\_CuraEngine.dir\src\multiVolumes.cpp.obj: CXX_COMPILER___CuraEngine_unscanned_Release C$:\Users\wsd07\vscode\Cura-Dev\CuraEngine\src\multiVolumes.cpp || cmake_object_order_depends_target__CuraEngine
  CONFIG = Release
  DEFINES = -DAGRPC_BOOST_ASIO -DARCUS -DBOOST_ALL_NO_LIB -DBOOST_STACKTRACE_USE_NOOP -DBOOST_STACKTRACE_USE_WINDBG -DBOOST_STACKTRACE_USE_WINDBG_CACHED -DCARES_STATICLIB -DCURA_ENGINE_HASH=\"unknown\" -DCURA_ENGINE_VERSION=\"5.11.0-alpha.0\" -DENABLE_PLUGINS -DNOMINMAX -DSENTRY_ENVIRONMENT=\"development\" -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSTB_TEXTEDIT_KEYTYPE=unsigned
  FLAGS = /DWIN32 /D_WINDOWS /EHsc /O2 /Ob2 /DNDEBUG -std:c++20 -MD /utf-8 /permissive-
  INCLUDES = -IC:\Users\<USER>\vscode\Cura-Dev\CuraEngine\include -IC:\Users\<USER>\vscode\Cura-Dev\CuraEngine\build\Release -external:IC:\Users\<USER>\.conan2\p\b\arcusee7666f716199\p\include -external:IC:\Users\<USER>\.conan2\p\proto47a676cb9257b\p\include -external:IC:\Users\<USER>\.conan2\p\zlibcfb9789dc2a53\p\include -external:IC:\Users\<USER>\.conan2\p\spdlo2d4b540ab0f22\p\include -external:IC:\Users\<USER>\.conan2\p\fmtdb20aad6e1bd4\p\include -external:IC:\Users\<USER>\.conan2\p\range0301bf3d76d5d\p\include -external:IC:\Users\<USER>\.conan2\p\b\clippf8ddcd4a5d961\p\include -external:IC:\Users\<USER>\.conan2\p\mapbo34ee9aeb574f9\p\include -external:IC:\Users\<USER>\.conan2\p\mapbofe72fb50cf7c4\p\include -external:IC:\Users\<USER>\.conan2\p\mapbo1b62b48748b82\p\include -external:IC:\Users\<USER>\.conan2\p\rapidf7a3355ba53c4\p\include -external:IC:\Users\<USER>\.conan2\p\stb6342cecb318f5\p\include -external:IC:\Users\<USER>\.conan2\p\boosta79d738920bc3\p\include -external:IC:\Users\<USER>\.conan2\p\scrip4cfd014ad4660\p\include -external:IC:\Users\<USER>\.conan2\p\nearg388d58da7a54c\p\include -external:IC:\Users\<USER>\.conan2\p\b\curaebb245e64b5ac7\p\include -external:IC:\Users\<USER>\.conan2\p\asio-c31dc2a380bf0\p\include -external:IC:\Users\<USER>\.conan2\p\grpc8b6c73b4e5756\p\include -external:IC:\Users\<USER>\.conan2\p\absei6a15444d60d47\p\include -external:W0
  OBJECT_DIR = CMakeFiles\_CuraEngine.dir
  OBJECT_FILE_DIR = CMakeFiles\_CuraEngine.dir\src
  TARGET_COMPILE_PDB = CMakeFiles\_CuraEngine.dir\_CuraEngine.pdb
  TARGET_PDB = _CuraEngine.pdb

build CMakeFiles\_CuraEngine.dir\src\path_ordering.cpp.obj: CXX_COMPILER___CuraEngine_unscanned_Release C$:\Users\wsd07\vscode\Cura-Dev\CuraEngine\src\path_ordering.cpp || cmake_object_order_depends_target__CuraEngine
  CONFIG = Release
  DEFINES = -DAGRPC_BOOST_ASIO -DARCUS -DBOOST_ALL_NO_LIB -DBOOST_STACKTRACE_USE_NOOP -DBOOST_STACKTRACE_USE_WINDBG -DBOOST_STACKTRACE_USE_WINDBG_CACHED -DCARES_STATICLIB -DCURA_ENGINE_HASH=\"unknown\" -DCURA_ENGINE_VERSION=\"5.11.0-alpha.0\" -DENABLE_PLUGINS -DNOMINMAX -DSENTRY_ENVIRONMENT=\"development\" -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSTB_TEXTEDIT_KEYTYPE=unsigned
  FLAGS = /DWIN32 /D_WINDOWS /EHsc /O2 /Ob2 /DNDEBUG -std:c++20 -MD /utf-8 /permissive-
  INCLUDES = -IC:\Users\<USER>\vscode\Cura-Dev\CuraEngine\include -IC:\Users\<USER>\vscode\Cura-Dev\CuraEngine\build\Release -external:IC:\Users\<USER>\.conan2\p\b\arcusee7666f716199\p\include -external:IC:\Users\<USER>\.conan2\p\proto47a676cb9257b\p\include -external:IC:\Users\<USER>\.conan2\p\zlibcfb9789dc2a53\p\include -external:IC:\Users\<USER>\.conan2\p\spdlo2d4b540ab0f22\p\include -external:IC:\Users\<USER>\.conan2\p\fmtdb20aad6e1bd4\p\include -external:IC:\Users\<USER>\.conan2\p\range0301bf3d76d5d\p\include -external:IC:\Users\<USER>\.conan2\p\b\clippf8ddcd4a5d961\p\include -external:IC:\Users\<USER>\.conan2\p\mapbo34ee9aeb574f9\p\include -external:IC:\Users\<USER>\.conan2\p\mapbofe72fb50cf7c4\p\include -external:IC:\Users\<USER>\.conan2\p\mapbo1b62b48748b82\p\include -external:IC:\Users\<USER>\.conan2\p\rapidf7a3355ba53c4\p\include -external:IC:\Users\<USER>\.conan2\p\stb6342cecb318f5\p\include -external:IC:\Users\<USER>\.conan2\p\boosta79d738920bc3\p\include -external:IC:\Users\<USER>\.conan2\p\scrip4cfd014ad4660\p\include -external:IC:\Users\<USER>\.conan2\p\nearg388d58da7a54c\p\include -external:IC:\Users\<USER>\.conan2\p\b\curaebb245e64b5ac7\p\include -external:IC:\Users\<USER>\.conan2\p\asio-c31dc2a380bf0\p\include -external:IC:\Users\<USER>\.conan2\p\grpc8b6c73b4e5756\p\include -external:IC:\Users\<USER>\.conan2\p\absei6a15444d60d47\p\include -external:W0
  OBJECT_DIR = CMakeFiles\_CuraEngine.dir
  OBJECT_FILE_DIR = CMakeFiles\_CuraEngine.dir\src
  TARGET_COMPILE_PDB = CMakeFiles\_CuraEngine.dir\_CuraEngine.pdb
  TARGET_PDB = _CuraEngine.pdb

build CMakeFiles\_CuraEngine.dir\src\PathAdapter.cpp.obj: CXX_COMPILER___CuraEngine_unscanned_Release C$:\Users\wsd07\vscode\Cura-Dev\CuraEngine\src\PathAdapter.cpp || cmake_object_order_depends_target__CuraEngine
  CONFIG = Release
  DEFINES = -DAGRPC_BOOST_ASIO -DARCUS -DBOOST_ALL_NO_LIB -DBOOST_STACKTRACE_USE_NOOP -DBOOST_STACKTRACE_USE_WINDBG -DBOOST_STACKTRACE_USE_WINDBG_CACHED -DCARES_STATICLIB -DCURA_ENGINE_HASH=\"unknown\" -DCURA_ENGINE_VERSION=\"5.11.0-alpha.0\" -DENABLE_PLUGINS -DNOMINMAX -DSENTRY_ENVIRONMENT=\"development\" -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSTB_TEXTEDIT_KEYTYPE=unsigned
  FLAGS = /DWIN32 /D_WINDOWS /EHsc /O2 /Ob2 /DNDEBUG -std:c++20 -MD /utf-8 /permissive-
  INCLUDES = -IC:\Users\<USER>\vscode\Cura-Dev\CuraEngine\include -IC:\Users\<USER>\vscode\Cura-Dev\CuraEngine\build\Release -external:IC:\Users\<USER>\.conan2\p\b\arcusee7666f716199\p\include -external:IC:\Users\<USER>\.conan2\p\proto47a676cb9257b\p\include -external:IC:\Users\<USER>\.conan2\p\zlibcfb9789dc2a53\p\include -external:IC:\Users\<USER>\.conan2\p\spdlo2d4b540ab0f22\p\include -external:IC:\Users\<USER>\.conan2\p\fmtdb20aad6e1bd4\p\include -external:IC:\Users\<USER>\.conan2\p\range0301bf3d76d5d\p\include -external:IC:\Users\<USER>\.conan2\p\b\clippf8ddcd4a5d961\p\include -external:IC:\Users\<USER>\.conan2\p\mapbo34ee9aeb574f9\p\include -external:IC:\Users\<USER>\.conan2\p\mapbofe72fb50cf7c4\p\include -external:IC:\Users\<USER>\.conan2\p\mapbo1b62b48748b82\p\include -external:IC:\Users\<USER>\.conan2\p\rapidf7a3355ba53c4\p\include -external:IC:\Users\<USER>\.conan2\p\stb6342cecb318f5\p\include -external:IC:\Users\<USER>\.conan2\p\boosta79d738920bc3\p\include -external:IC:\Users\<USER>\.conan2\p\scrip4cfd014ad4660\p\include -external:IC:\Users\<USER>\.conan2\p\nearg388d58da7a54c\p\include -external:IC:\Users\<USER>\.conan2\p\b\curaebb245e64b5ac7\p\include -external:IC:\Users\<USER>\.conan2\p\asio-c31dc2a380bf0\p\include -external:IC:\Users\<USER>\.conan2\p\grpc8b6c73b4e5756\p\include -external:IC:\Users\<USER>\.conan2\p\absei6a15444d60d47\p\include -external:W0
  OBJECT_DIR = CMakeFiles\_CuraEngine.dir
  OBJECT_FILE_DIR = CMakeFiles\_CuraEngine.dir\src
  TARGET_COMPILE_PDB = CMakeFiles\_CuraEngine.dir\_CuraEngine.pdb
  TARGET_PDB = _CuraEngine.pdb

build CMakeFiles\_CuraEngine.dir\src\Preheat.cpp.obj: CXX_COMPILER___CuraEngine_unscanned_Release C$:\Users\wsd07\vscode\Cura-Dev\CuraEngine\src\Preheat.cpp || cmake_object_order_depends_target__CuraEngine
  CONFIG = Release
  DEFINES = -DAGRPC_BOOST_ASIO -DARCUS -DBOOST_ALL_NO_LIB -DBOOST_STACKTRACE_USE_NOOP -DBOOST_STACKTRACE_USE_WINDBG -DBOOST_STACKTRACE_USE_WINDBG_CACHED -DCARES_STATICLIB -DCURA_ENGINE_HASH=\"unknown\" -DCURA_ENGINE_VERSION=\"5.11.0-alpha.0\" -DENABLE_PLUGINS -DNOMINMAX -DSENTRY_ENVIRONMENT=\"development\" -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSTB_TEXTEDIT_KEYTYPE=unsigned
  FLAGS = /DWIN32 /D_WINDOWS /EHsc /O2 /Ob2 /DNDEBUG -std:c++20 -MD /utf-8 /permissive-
  INCLUDES = -IC:\Users\<USER>\vscode\Cura-Dev\CuraEngine\include -IC:\Users\<USER>\vscode\Cura-Dev\CuraEngine\build\Release -external:IC:\Users\<USER>\.conan2\p\b\arcusee7666f716199\p\include -external:IC:\Users\<USER>\.conan2\p\proto47a676cb9257b\p\include -external:IC:\Users\<USER>\.conan2\p\zlibcfb9789dc2a53\p\include -external:IC:\Users\<USER>\.conan2\p\spdlo2d4b540ab0f22\p\include -external:IC:\Users\<USER>\.conan2\p\fmtdb20aad6e1bd4\p\include -external:IC:\Users\<USER>\.conan2\p\range0301bf3d76d5d\p\include -external:IC:\Users\<USER>\.conan2\p\b\clippf8ddcd4a5d961\p\include -external:IC:\Users\<USER>\.conan2\p\mapbo34ee9aeb574f9\p\include -external:IC:\Users\<USER>\.conan2\p\mapbofe72fb50cf7c4\p\include -external:IC:\Users\<USER>\.conan2\p\mapbo1b62b48748b82\p\include -external:IC:\Users\<USER>\.conan2\p\rapidf7a3355ba53c4\p\include -external:IC:\Users\<USER>\.conan2\p\stb6342cecb318f5\p\include -external:IC:\Users\<USER>\.conan2\p\boosta79d738920bc3\p\include -external:IC:\Users\<USER>\.conan2\p\scrip4cfd014ad4660\p\include -external:IC:\Users\<USER>\.conan2\p\nearg388d58da7a54c\p\include -external:IC:\Users\<USER>\.conan2\p\b\curaebb245e64b5ac7\p\include -external:IC:\Users\<USER>\.conan2\p\asio-c31dc2a380bf0\p\include -external:IC:\Users\<USER>\.conan2\p\grpc8b6c73b4e5756\p\include -external:IC:\Users\<USER>\.conan2\p\absei6a15444d60d47\p\include -external:W0
  OBJECT_DIR = CMakeFiles\_CuraEngine.dir
  OBJECT_FILE_DIR = CMakeFiles\_CuraEngine.dir\src
  TARGET_COMPILE_PDB = CMakeFiles\_CuraEngine.dir\_CuraEngine.pdb
  TARGET_PDB = _CuraEngine.pdb

build CMakeFiles\_CuraEngine.dir\src\PrimeTower\PrimeTower.cpp.obj: CXX_COMPILER___CuraEngine_unscanned_Release C$:\Users\wsd07\vscode\Cura-Dev\CuraEngine\src\PrimeTower\PrimeTower.cpp || cmake_object_order_depends_target__CuraEngine
  CONFIG = Release
  DEFINES = -DAGRPC_BOOST_ASIO -DARCUS -DBOOST_ALL_NO_LIB -DBOOST_STACKTRACE_USE_NOOP -DBOOST_STACKTRACE_USE_WINDBG -DBOOST_STACKTRACE_USE_WINDBG_CACHED -DCARES_STATICLIB -DCURA_ENGINE_HASH=\"unknown\" -DCURA_ENGINE_VERSION=\"5.11.0-alpha.0\" -DENABLE_PLUGINS -DNOMINMAX -DSENTRY_ENVIRONMENT=\"development\" -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSTB_TEXTEDIT_KEYTYPE=unsigned
  FLAGS = /DWIN32 /D_WINDOWS /EHsc /O2 /Ob2 /DNDEBUG -std:c++20 -MD /utf-8 /permissive-
  INCLUDES = -IC:\Users\<USER>\vscode\Cura-Dev\CuraEngine\include -IC:\Users\<USER>\vscode\Cura-Dev\CuraEngine\build\Release -external:IC:\Users\<USER>\.conan2\p\b\arcusee7666f716199\p\include -external:IC:\Users\<USER>\.conan2\p\proto47a676cb9257b\p\include -external:IC:\Users\<USER>\.conan2\p\zlibcfb9789dc2a53\p\include -external:IC:\Users\<USER>\.conan2\p\spdlo2d4b540ab0f22\p\include -external:IC:\Users\<USER>\.conan2\p\fmtdb20aad6e1bd4\p\include -external:IC:\Users\<USER>\.conan2\p\range0301bf3d76d5d\p\include -external:IC:\Users\<USER>\.conan2\p\b\clippf8ddcd4a5d961\p\include -external:IC:\Users\<USER>\.conan2\p\mapbo34ee9aeb574f9\p\include -external:IC:\Users\<USER>\.conan2\p\mapbofe72fb50cf7c4\p\include -external:IC:\Users\<USER>\.conan2\p\mapbo1b62b48748b82\p\include -external:IC:\Users\<USER>\.conan2\p\rapidf7a3355ba53c4\p\include -external:IC:\Users\<USER>\.conan2\p\stb6342cecb318f5\p\include -external:IC:\Users\<USER>\.conan2\p\boosta79d738920bc3\p\include -external:IC:\Users\<USER>\.conan2\p\scrip4cfd014ad4660\p\include -external:IC:\Users\<USER>\.conan2\p\nearg388d58da7a54c\p\include -external:IC:\Users\<USER>\.conan2\p\b\curaebb245e64b5ac7\p\include -external:IC:\Users\<USER>\.conan2\p\asio-c31dc2a380bf0\p\include -external:IC:\Users\<USER>\.conan2\p\grpc8b6c73b4e5756\p\include -external:IC:\Users\<USER>\.conan2\p\absei6a15444d60d47\p\include -external:W0
  OBJECT_DIR = CMakeFiles\_CuraEngine.dir
  OBJECT_FILE_DIR = CMakeFiles\_CuraEngine.dir\src\PrimeTower
  TARGET_COMPILE_PDB = CMakeFiles\_CuraEngine.dir\_CuraEngine.pdb
  TARGET_PDB = _CuraEngine.pdb

build CMakeFiles\_CuraEngine.dir\src\PrimeTower\PrimeTowerNormal.cpp.obj: CXX_COMPILER___CuraEngine_unscanned_Release C$:\Users\wsd07\vscode\Cura-Dev\CuraEngine\src\PrimeTower\PrimeTowerNormal.cpp || cmake_object_order_depends_target__CuraEngine
  CONFIG = Release
  DEFINES = -DAGRPC_BOOST_ASIO -DARCUS -DBOOST_ALL_NO_LIB -DBOOST_STACKTRACE_USE_NOOP -DBOOST_STACKTRACE_USE_WINDBG -DBOOST_STACKTRACE_USE_WINDBG_CACHED -DCARES_STATICLIB -DCURA_ENGINE_HASH=\"unknown\" -DCURA_ENGINE_VERSION=\"5.11.0-alpha.0\" -DENABLE_PLUGINS -DNOMINMAX -DSENTRY_ENVIRONMENT=\"development\" -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSTB_TEXTEDIT_KEYTYPE=unsigned
  FLAGS = /DWIN32 /D_WINDOWS /EHsc /O2 /Ob2 /DNDEBUG -std:c++20 -MD /utf-8 /permissive-
  INCLUDES = -IC:\Users\<USER>\vscode\Cura-Dev\CuraEngine\include -IC:\Users\<USER>\vscode\Cura-Dev\CuraEngine\build\Release -external:IC:\Users\<USER>\.conan2\p\b\arcusee7666f716199\p\include -external:IC:\Users\<USER>\.conan2\p\proto47a676cb9257b\p\include -external:IC:\Users\<USER>\.conan2\p\zlibcfb9789dc2a53\p\include -external:IC:\Users\<USER>\.conan2\p\spdlo2d4b540ab0f22\p\include -external:IC:\Users\<USER>\.conan2\p\fmtdb20aad6e1bd4\p\include -external:IC:\Users\<USER>\.conan2\p\range0301bf3d76d5d\p\include -external:IC:\Users\<USER>\.conan2\p\b\clippf8ddcd4a5d961\p\include -external:IC:\Users\<USER>\.conan2\p\mapbo34ee9aeb574f9\p\include -external:IC:\Users\<USER>\.conan2\p\mapbofe72fb50cf7c4\p\include -external:IC:\Users\<USER>\.conan2\p\mapbo1b62b48748b82\p\include -external:IC:\Users\<USER>\.conan2\p\rapidf7a3355ba53c4\p\include -external:IC:\Users\<USER>\.conan2\p\stb6342cecb318f5\p\include -external:IC:\Users\<USER>\.conan2\p\boosta79d738920bc3\p\include -external:IC:\Users\<USER>\.conan2\p\scrip4cfd014ad4660\p\include -external:IC:\Users\<USER>\.conan2\p\nearg388d58da7a54c\p\include -external:IC:\Users\<USER>\.conan2\p\b\curaebb245e64b5ac7\p\include -external:IC:\Users\<USER>\.conan2\p\asio-c31dc2a380bf0\p\include -external:IC:\Users\<USER>\.conan2\p\grpc8b6c73b4e5756\p\include -external:IC:\Users\<USER>\.conan2\p\absei6a15444d60d47\p\include -external:W0
  OBJECT_DIR = CMakeFiles\_CuraEngine.dir
  OBJECT_FILE_DIR = CMakeFiles\_CuraEngine.dir\src\PrimeTower
  TARGET_COMPILE_PDB = CMakeFiles\_CuraEngine.dir\_CuraEngine.pdb
  TARGET_PDB = _CuraEngine.pdb

build CMakeFiles\_CuraEngine.dir\src\PrimeTower\PrimeTowerInterleaved.cpp.obj: CXX_COMPILER___CuraEngine_unscanned_Release C$:\Users\wsd07\vscode\Cura-Dev\CuraEngine\src\PrimeTower\PrimeTowerInterleaved.cpp || cmake_object_order_depends_target__CuraEngine
  CONFIG = Release
  DEFINES = -DAGRPC_BOOST_ASIO -DARCUS -DBOOST_ALL_NO_LIB -DBOOST_STACKTRACE_USE_NOOP -DBOOST_STACKTRACE_USE_WINDBG -DBOOST_STACKTRACE_USE_WINDBG_CACHED -DCARES_STATICLIB -DCURA_ENGINE_HASH=\"unknown\" -DCURA_ENGINE_VERSION=\"5.11.0-alpha.0\" -DENABLE_PLUGINS -DNOMINMAX -DSENTRY_ENVIRONMENT=\"development\" -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSTB_TEXTEDIT_KEYTYPE=unsigned
  FLAGS = /DWIN32 /D_WINDOWS /EHsc /O2 /Ob2 /DNDEBUG -std:c++20 -MD /utf-8 /permissive-
  INCLUDES = -IC:\Users\<USER>\vscode\Cura-Dev\CuraEngine\include -IC:\Users\<USER>\vscode\Cura-Dev\CuraEngine\build\Release -external:IC:\Users\<USER>\.conan2\p\b\arcusee7666f716199\p\include -external:IC:\Users\<USER>\.conan2\p\proto47a676cb9257b\p\include -external:IC:\Users\<USER>\.conan2\p\zlibcfb9789dc2a53\p\include -external:IC:\Users\<USER>\.conan2\p\spdlo2d4b540ab0f22\p\include -external:IC:\Users\<USER>\.conan2\p\fmtdb20aad6e1bd4\p\include -external:IC:\Users\<USER>\.conan2\p\range0301bf3d76d5d\p\include -external:IC:\Users\<USER>\.conan2\p\b\clippf8ddcd4a5d961\p\include -external:IC:\Users\<USER>\.conan2\p\mapbo34ee9aeb574f9\p\include -external:IC:\Users\<USER>\.conan2\p\mapbofe72fb50cf7c4\p\include -external:IC:\Users\<USER>\.conan2\p\mapbo1b62b48748b82\p\include -external:IC:\Users\<USER>\.conan2\p\rapidf7a3355ba53c4\p\include -external:IC:\Users\<USER>\.conan2\p\stb6342cecb318f5\p\include -external:IC:\Users\<USER>\.conan2\p\boosta79d738920bc3\p\include -external:IC:\Users\<USER>\.conan2\p\scrip4cfd014ad4660\p\include -external:IC:\Users\<USER>\.conan2\p\nearg388d58da7a54c\p\include -external:IC:\Users\<USER>\.conan2\p\b\curaebb245e64b5ac7\p\include -external:IC:\Users\<USER>\.conan2\p\asio-c31dc2a380bf0\p\include -external:IC:\Users\<USER>\.conan2\p\grpc8b6c73b4e5756\p\include -external:IC:\Users\<USER>\.conan2\p\absei6a15444d60d47\p\include -external:W0
  OBJECT_DIR = CMakeFiles\_CuraEngine.dir
  OBJECT_FILE_DIR = CMakeFiles\_CuraEngine.dir\src\PrimeTower
  TARGET_COMPILE_PDB = CMakeFiles\_CuraEngine.dir\_CuraEngine.pdb
  TARGET_PDB = _CuraEngine.pdb

build CMakeFiles\_CuraEngine.dir\src\raft.cpp.obj: CXX_COMPILER___CuraEngine_unscanned_Release C$:\Users\wsd07\vscode\Cura-Dev\CuraEngine\src\raft.cpp || cmake_object_order_depends_target__CuraEngine
  CONFIG = Release
  DEFINES = -DAGRPC_BOOST_ASIO -DARCUS -DBOOST_ALL_NO_LIB -DBOOST_STACKTRACE_USE_NOOP -DBOOST_STACKTRACE_USE_WINDBG -DBOOST_STACKTRACE_USE_WINDBG_CACHED -DCARES_STATICLIB -DCURA_ENGINE_HASH=\"unknown\" -DCURA_ENGINE_VERSION=\"5.11.0-alpha.0\" -DENABLE_PLUGINS -DNOMINMAX -DSENTRY_ENVIRONMENT=\"development\" -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSTB_TEXTEDIT_KEYTYPE=unsigned
  FLAGS = /DWIN32 /D_WINDOWS /EHsc /O2 /Ob2 /DNDEBUG -std:c++20 -MD /utf-8 /permissive-
  INCLUDES = -IC:\Users\<USER>\vscode\Cura-Dev\CuraEngine\include -IC:\Users\<USER>\vscode\Cura-Dev\CuraEngine\build\Release -external:IC:\Users\<USER>\.conan2\p\b\arcusee7666f716199\p\include -external:IC:\Users\<USER>\.conan2\p\proto47a676cb9257b\p\include -external:IC:\Users\<USER>\.conan2\p\zlibcfb9789dc2a53\p\include -external:IC:\Users\<USER>\.conan2\p\spdlo2d4b540ab0f22\p\include -external:IC:\Users\<USER>\.conan2\p\fmtdb20aad6e1bd4\p\include -external:IC:\Users\<USER>\.conan2\p\range0301bf3d76d5d\p\include -external:IC:\Users\<USER>\.conan2\p\b\clippf8ddcd4a5d961\p\include -external:IC:\Users\<USER>\.conan2\p\mapbo34ee9aeb574f9\p\include -external:IC:\Users\<USER>\.conan2\p\mapbofe72fb50cf7c4\p\include -external:IC:\Users\<USER>\.conan2\p\mapbo1b62b48748b82\p\include -external:IC:\Users\<USER>\.conan2\p\rapidf7a3355ba53c4\p\include -external:IC:\Users\<USER>\.conan2\p\stb6342cecb318f5\p\include -external:IC:\Users\<USER>\.conan2\p\boosta79d738920bc3\p\include -external:IC:\Users\<USER>\.conan2\p\scrip4cfd014ad4660\p\include -external:IC:\Users\<USER>\.conan2\p\nearg388d58da7a54c\p\include -external:IC:\Users\<USER>\.conan2\p\b\curaebb245e64b5ac7\p\include -external:IC:\Users\<USER>\.conan2\p\asio-c31dc2a380bf0\p\include -external:IC:\Users\<USER>\.conan2\p\grpc8b6c73b4e5756\p\include -external:IC:\Users\<USER>\.conan2\p\absei6a15444d60d47\p\include -external:W0
  OBJECT_DIR = CMakeFiles\_CuraEngine.dir
  OBJECT_FILE_DIR = CMakeFiles\_CuraEngine.dir\src
  TARGET_COMPILE_PDB = CMakeFiles\_CuraEngine.dir\_CuraEngine.pdb
  TARGET_PDB = _CuraEngine.pdb

build CMakeFiles\_CuraEngine.dir\src\Scene.cpp.obj: CXX_COMPILER___CuraEngine_unscanned_Release C$:\Users\wsd07\vscode\Cura-Dev\CuraEngine\src\Scene.cpp || cmake_object_order_depends_target__CuraEngine
  CONFIG = Release
  DEFINES = -DAGRPC_BOOST_ASIO -DARCUS -DBOOST_ALL_NO_LIB -DBOOST_STACKTRACE_USE_NOOP -DBOOST_STACKTRACE_USE_WINDBG -DBOOST_STACKTRACE_USE_WINDBG_CACHED -DCARES_STATICLIB -DCURA_ENGINE_HASH=\"unknown\" -DCURA_ENGINE_VERSION=\"5.11.0-alpha.0\" -DENABLE_PLUGINS -DNOMINMAX -DSENTRY_ENVIRONMENT=\"development\" -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSTB_TEXTEDIT_KEYTYPE=unsigned
  FLAGS = /DWIN32 /D_WINDOWS /EHsc /O2 /Ob2 /DNDEBUG -std:c++20 -MD /utf-8 /permissive-
  INCLUDES = -IC:\Users\<USER>\vscode\Cura-Dev\CuraEngine\include -IC:\Users\<USER>\vscode\Cura-Dev\CuraEngine\build\Release -external:IC:\Users\<USER>\.conan2\p\b\arcusee7666f716199\p\include -external:IC:\Users\<USER>\.conan2\p\proto47a676cb9257b\p\include -external:IC:\Users\<USER>\.conan2\p\zlibcfb9789dc2a53\p\include -external:IC:\Users\<USER>\.conan2\p\spdlo2d4b540ab0f22\p\include -external:IC:\Users\<USER>\.conan2\p\fmtdb20aad6e1bd4\p\include -external:IC:\Users\<USER>\.conan2\p\range0301bf3d76d5d\p\include -external:IC:\Users\<USER>\.conan2\p\b\clippf8ddcd4a5d961\p\include -external:IC:\Users\<USER>\.conan2\p\mapbo34ee9aeb574f9\p\include -external:IC:\Users\<USER>\.conan2\p\mapbofe72fb50cf7c4\p\include -external:IC:\Users\<USER>\.conan2\p\mapbo1b62b48748b82\p\include -external:IC:\Users\<USER>\.conan2\p\rapidf7a3355ba53c4\p\include -external:IC:\Users\<USER>\.conan2\p\stb6342cecb318f5\p\include -external:IC:\Users\<USER>\.conan2\p\boosta79d738920bc3\p\include -external:IC:\Users\<USER>\.conan2\p\scrip4cfd014ad4660\p\include -external:IC:\Users\<USER>\.conan2\p\nearg388d58da7a54c\p\include -external:IC:\Users\<USER>\.conan2\p\b\curaebb245e64b5ac7\p\include -external:IC:\Users\<USER>\.conan2\p\asio-c31dc2a380bf0\p\include -external:IC:\Users\<USER>\.conan2\p\grpc8b6c73b4e5756\p\include -external:IC:\Users\<USER>\.conan2\p\absei6a15444d60d47\p\include -external:W0
  OBJECT_DIR = CMakeFiles\_CuraEngine.dir
  OBJECT_FILE_DIR = CMakeFiles\_CuraEngine.dir\src
  TARGET_COMPILE_PDB = CMakeFiles\_CuraEngine.dir\_CuraEngine.pdb
  TARGET_PDB = _CuraEngine.pdb

build CMakeFiles\_CuraEngine.dir\src\SkeletalTrapezoidation.cpp.obj: CXX_COMPILER___CuraEngine_unscanned_Release C$:\Users\wsd07\vscode\Cura-Dev\CuraEngine\src\SkeletalTrapezoidation.cpp || cmake_object_order_depends_target__CuraEngine
  CONFIG = Release
  DEFINES = -DAGRPC_BOOST_ASIO -DARCUS -DBOOST_ALL_NO_LIB -DBOOST_STACKTRACE_USE_NOOP -DBOOST_STACKTRACE_USE_WINDBG -DBOOST_STACKTRACE_USE_WINDBG_CACHED -DCARES_STATICLIB -DCURA_ENGINE_HASH=\"unknown\" -DCURA_ENGINE_VERSION=\"5.11.0-alpha.0\" -DENABLE_PLUGINS -DNOMINMAX -DSENTRY_ENVIRONMENT=\"development\" -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSTB_TEXTEDIT_KEYTYPE=unsigned
  FLAGS = /DWIN32 /D_WINDOWS /EHsc /O2 /Ob2 /DNDEBUG -std:c++20 -MD /utf-8 /permissive-
  INCLUDES = -IC:\Users\<USER>\vscode\Cura-Dev\CuraEngine\include -IC:\Users\<USER>\vscode\Cura-Dev\CuraEngine\build\Release -external:IC:\Users\<USER>\.conan2\p\b\arcusee7666f716199\p\include -external:IC:\Users\<USER>\.conan2\p\proto47a676cb9257b\p\include -external:IC:\Users\<USER>\.conan2\p\zlibcfb9789dc2a53\p\include -external:IC:\Users\<USER>\.conan2\p\spdlo2d4b540ab0f22\p\include -external:IC:\Users\<USER>\.conan2\p\fmtdb20aad6e1bd4\p\include -external:IC:\Users\<USER>\.conan2\p\range0301bf3d76d5d\p\include -external:IC:\Users\<USER>\.conan2\p\b\clippf8ddcd4a5d961\p\include -external:IC:\Users\<USER>\.conan2\p\mapbo34ee9aeb574f9\p\include -external:IC:\Users\<USER>\.conan2\p\mapbofe72fb50cf7c4\p\include -external:IC:\Users\<USER>\.conan2\p\mapbo1b62b48748b82\p\include -external:IC:\Users\<USER>\.conan2\p\rapidf7a3355ba53c4\p\include -external:IC:\Users\<USER>\.conan2\p\stb6342cecb318f5\p\include -external:IC:\Users\<USER>\.conan2\p\boosta79d738920bc3\p\include -external:IC:\Users\<USER>\.conan2\p\scrip4cfd014ad4660\p\include -external:IC:\Users\<USER>\.conan2\p\nearg388d58da7a54c\p\include -external:IC:\Users\<USER>\.conan2\p\b\curaebb245e64b5ac7\p\include -external:IC:\Users\<USER>\.conan2\p\asio-c31dc2a380bf0\p\include -external:IC:\Users\<USER>\.conan2\p\grpc8b6c73b4e5756\p\include -external:IC:\Users\<USER>\.conan2\p\absei6a15444d60d47\p\include -external:W0
  OBJECT_DIR = CMakeFiles\_CuraEngine.dir
  OBJECT_FILE_DIR = CMakeFiles\_CuraEngine.dir\src
  TARGET_COMPILE_PDB = CMakeFiles\_CuraEngine.dir\_CuraEngine.pdb
  TARGET_PDB = _CuraEngine.pdb

build CMakeFiles\_CuraEngine.dir\src\SkeletalTrapezoidationGraph.cpp.obj: CXX_COMPILER___CuraEngine_unscanned_Release C$:\Users\wsd07\vscode\Cura-Dev\CuraEngine\src\SkeletalTrapezoidationGraph.cpp || cmake_object_order_depends_target__CuraEngine
  CONFIG = Release
  DEFINES = -DAGRPC_BOOST_ASIO -DARCUS -DBOOST_ALL_NO_LIB -DBOOST_STACKTRACE_USE_NOOP -DBOOST_STACKTRACE_USE_WINDBG -DBOOST_STACKTRACE_USE_WINDBG_CACHED -DCARES_STATICLIB -DCURA_ENGINE_HASH=\"unknown\" -DCURA_ENGINE_VERSION=\"5.11.0-alpha.0\" -DENABLE_PLUGINS -DNOMINMAX -DSENTRY_ENVIRONMENT=\"development\" -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSTB_TEXTEDIT_KEYTYPE=unsigned
  FLAGS = /DWIN32 /D_WINDOWS /EHsc /O2 /Ob2 /DNDEBUG -std:c++20 -MD /utf-8 /permissive-
  INCLUDES = -IC:\Users\<USER>\vscode\Cura-Dev\CuraEngine\include -IC:\Users\<USER>\vscode\Cura-Dev\CuraEngine\build\Release -external:IC:\Users\<USER>\.conan2\p\b\arcusee7666f716199\p\include -external:IC:\Users\<USER>\.conan2\p\proto47a676cb9257b\p\include -external:IC:\Users\<USER>\.conan2\p\zlibcfb9789dc2a53\p\include -external:IC:\Users\<USER>\.conan2\p\spdlo2d4b540ab0f22\p\include -external:IC:\Users\<USER>\.conan2\p\fmtdb20aad6e1bd4\p\include -external:IC:\Users\<USER>\.conan2\p\range0301bf3d76d5d\p\include -external:IC:\Users\<USER>\.conan2\p\b\clippf8ddcd4a5d961\p\include -external:IC:\Users\<USER>\.conan2\p\mapbo34ee9aeb574f9\p\include -external:IC:\Users\<USER>\.conan2\p\mapbofe72fb50cf7c4\p\include -external:IC:\Users\<USER>\.conan2\p\mapbo1b62b48748b82\p\include -external:IC:\Users\<USER>\.conan2\p\rapidf7a3355ba53c4\p\include -external:IC:\Users\<USER>\.conan2\p\stb6342cecb318f5\p\include -external:IC:\Users\<USER>\.conan2\p\boosta79d738920bc3\p\include -external:IC:\Users\<USER>\.conan2\p\scrip4cfd014ad4660\p\include -external:IC:\Users\<USER>\.conan2\p\nearg388d58da7a54c\p\include -external:IC:\Users\<USER>\.conan2\p\b\curaebb245e64b5ac7\p\include -external:IC:\Users\<USER>\.conan2\p\asio-c31dc2a380bf0\p\include -external:IC:\Users\<USER>\.conan2\p\grpc8b6c73b4e5756\p\include -external:IC:\Users\<USER>\.conan2\p\absei6a15444d60d47\p\include -external:W0
  OBJECT_DIR = CMakeFiles\_CuraEngine.dir
  OBJECT_FILE_DIR = CMakeFiles\_CuraEngine.dir\src
  TARGET_COMPILE_PDB = CMakeFiles\_CuraEngine.dir\_CuraEngine.pdb
  TARGET_PDB = _CuraEngine.pdb

build CMakeFiles\_CuraEngine.dir\src\skin.cpp.obj: CXX_COMPILER___CuraEngine_unscanned_Release C$:\Users\wsd07\vscode\Cura-Dev\CuraEngine\src\skin.cpp || cmake_object_order_depends_target__CuraEngine
  CONFIG = Release
  DEFINES = -DAGRPC_BOOST_ASIO -DARCUS -DBOOST_ALL_NO_LIB -DBOOST_STACKTRACE_USE_NOOP -DBOOST_STACKTRACE_USE_WINDBG -DBOOST_STACKTRACE_USE_WINDBG_CACHED -DCARES_STATICLIB -DCURA_ENGINE_HASH=\"unknown\" -DCURA_ENGINE_VERSION=\"5.11.0-alpha.0\" -DENABLE_PLUGINS -DNOMINMAX -DSENTRY_ENVIRONMENT=\"development\" -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSTB_TEXTEDIT_KEYTYPE=unsigned
  FLAGS = /DWIN32 /D_WINDOWS /EHsc /O2 /Ob2 /DNDEBUG -std:c++20 -MD /utf-8 /permissive-
  INCLUDES = -IC:\Users\<USER>\vscode\Cura-Dev\CuraEngine\include -IC:\Users\<USER>\vscode\Cura-Dev\CuraEngine\build\Release -external:IC:\Users\<USER>\.conan2\p\b\arcusee7666f716199\p\include -external:IC:\Users\<USER>\.conan2\p\proto47a676cb9257b\p\include -external:IC:\Users\<USER>\.conan2\p\zlibcfb9789dc2a53\p\include -external:IC:\Users\<USER>\.conan2\p\spdlo2d4b540ab0f22\p\include -external:IC:\Users\<USER>\.conan2\p\fmtdb20aad6e1bd4\p\include -external:IC:\Users\<USER>\.conan2\p\range0301bf3d76d5d\p\include -external:IC:\Users\<USER>\.conan2\p\b\clippf8ddcd4a5d961\p\include -external:IC:\Users\<USER>\.conan2\p\mapbo34ee9aeb574f9\p\include -external:IC:\Users\<USER>\.conan2\p\mapbofe72fb50cf7c4\p\include -external:IC:\Users\<USER>\.conan2\p\mapbo1b62b48748b82\p\include -external:IC:\Users\<USER>\.conan2\p\rapidf7a3355ba53c4\p\include -external:IC:\Users\<USER>\.conan2\p\stb6342cecb318f5\p\include -external:IC:\Users\<USER>\.conan2\p\boosta79d738920bc3\p\include -external:IC:\Users\<USER>\.conan2\p\scrip4cfd014ad4660\p\include -external:IC:\Users\<USER>\.conan2\p\nearg388d58da7a54c\p\include -external:IC:\Users\<USER>\.conan2\p\b\curaebb245e64b5ac7\p\include -external:IC:\Users\<USER>\.conan2\p\asio-c31dc2a380bf0\p\include -external:IC:\Users\<USER>\.conan2\p\grpc8b6c73b4e5756\p\include -external:IC:\Users\<USER>\.conan2\p\absei6a15444d60d47\p\include -external:W0
  OBJECT_DIR = CMakeFiles\_CuraEngine.dir
  OBJECT_FILE_DIR = CMakeFiles\_CuraEngine.dir\src
  TARGET_COMPILE_PDB = CMakeFiles\_CuraEngine.dir\_CuraEngine.pdb
  TARGET_PDB = _CuraEngine.pdb

build CMakeFiles\_CuraEngine.dir\src\SkirtBrim.cpp.obj: CXX_COMPILER___CuraEngine_unscanned_Release C$:\Users\wsd07\vscode\Cura-Dev\CuraEngine\src\SkirtBrim.cpp || cmake_object_order_depends_target__CuraEngine
  CONFIG = Release
  DEFINES = -DAGRPC_BOOST_ASIO -DARCUS -DBOOST_ALL_NO_LIB -DBOOST_STACKTRACE_USE_NOOP -DBOOST_STACKTRACE_USE_WINDBG -DBOOST_STACKTRACE_USE_WINDBG_CACHED -DCARES_STATICLIB -DCURA_ENGINE_HASH=\"unknown\" -DCURA_ENGINE_VERSION=\"5.11.0-alpha.0\" -DENABLE_PLUGINS -DNOMINMAX -DSENTRY_ENVIRONMENT=\"development\" -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSTB_TEXTEDIT_KEYTYPE=unsigned
  FLAGS = /DWIN32 /D_WINDOWS /EHsc /O2 /Ob2 /DNDEBUG -std:c++20 -MD /utf-8 /permissive-
  INCLUDES = -IC:\Users\<USER>\vscode\Cura-Dev\CuraEngine\include -IC:\Users\<USER>\vscode\Cura-Dev\CuraEngine\build\Release -external:IC:\Users\<USER>\.conan2\p\b\arcusee7666f716199\p\include -external:IC:\Users\<USER>\.conan2\p\proto47a676cb9257b\p\include -external:IC:\Users\<USER>\.conan2\p\zlibcfb9789dc2a53\p\include -external:IC:\Users\<USER>\.conan2\p\spdlo2d4b540ab0f22\p\include -external:IC:\Users\<USER>\.conan2\p\fmtdb20aad6e1bd4\p\include -external:IC:\Users\<USER>\.conan2\p\range0301bf3d76d5d\p\include -external:IC:\Users\<USER>\.conan2\p\b\clippf8ddcd4a5d961\p\include -external:IC:\Users\<USER>\.conan2\p\mapbo34ee9aeb574f9\p\include -external:IC:\Users\<USER>\.conan2\p\mapbofe72fb50cf7c4\p\include -external:IC:\Users\<USER>\.conan2\p\mapbo1b62b48748b82\p\include -external:IC:\Users\<USER>\.conan2\p\rapidf7a3355ba53c4\p\include -external:IC:\Users\<USER>\.conan2\p\stb6342cecb318f5\p\include -external:IC:\Users\<USER>\.conan2\p\boosta79d738920bc3\p\include -external:IC:\Users\<USER>\.conan2\p\scrip4cfd014ad4660\p\include -external:IC:\Users\<USER>\.conan2\p\nearg388d58da7a54c\p\include -external:IC:\Users\<USER>\.conan2\p\b\curaebb245e64b5ac7\p\include -external:IC:\Users\<USER>\.conan2\p\asio-c31dc2a380bf0\p\include -external:IC:\Users\<USER>\.conan2\p\grpc8b6c73b4e5756\p\include -external:IC:\Users\<USER>\.conan2\p\absei6a15444d60d47\p\include -external:W0
  OBJECT_DIR = CMakeFiles\_CuraEngine.dir
  OBJECT_FILE_DIR = CMakeFiles\_CuraEngine.dir\src
  TARGET_COMPILE_PDB = CMakeFiles\_CuraEngine.dir\_CuraEngine.pdb
  TARGET_PDB = _CuraEngine.pdb

build CMakeFiles\_CuraEngine.dir\src\SupportInfillPart.cpp.obj: CXX_COMPILER___CuraEngine_unscanned_Release C$:\Users\wsd07\vscode\Cura-Dev\CuraEngine\src\SupportInfillPart.cpp || cmake_object_order_depends_target__CuraEngine
  CONFIG = Release
  DEFINES = -DAGRPC_BOOST_ASIO -DARCUS -DBOOST_ALL_NO_LIB -DBOOST_STACKTRACE_USE_NOOP -DBOOST_STACKTRACE_USE_WINDBG -DBOOST_STACKTRACE_USE_WINDBG_CACHED -DCARES_STATICLIB -DCURA_ENGINE_HASH=\"unknown\" -DCURA_ENGINE_VERSION=\"5.11.0-alpha.0\" -DENABLE_PLUGINS -DNOMINMAX -DSENTRY_ENVIRONMENT=\"development\" -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSTB_TEXTEDIT_KEYTYPE=unsigned
  FLAGS = /DWIN32 /D_WINDOWS /EHsc /O2 /Ob2 /DNDEBUG -std:c++20 -MD /utf-8 /permissive-
  INCLUDES = -IC:\Users\<USER>\vscode\Cura-Dev\CuraEngine\include -IC:\Users\<USER>\vscode\Cura-Dev\CuraEngine\build\Release -external:IC:\Users\<USER>\.conan2\p\b\arcusee7666f716199\p\include -external:IC:\Users\<USER>\.conan2\p\proto47a676cb9257b\p\include -external:IC:\Users\<USER>\.conan2\p\zlibcfb9789dc2a53\p\include -external:IC:\Users\<USER>\.conan2\p\spdlo2d4b540ab0f22\p\include -external:IC:\Users\<USER>\.conan2\p\fmtdb20aad6e1bd4\p\include -external:IC:\Users\<USER>\.conan2\p\range0301bf3d76d5d\p\include -external:IC:\Users\<USER>\.conan2\p\b\clippf8ddcd4a5d961\p\include -external:IC:\Users\<USER>\.conan2\p\mapbo34ee9aeb574f9\p\include -external:IC:\Users\<USER>\.conan2\p\mapbofe72fb50cf7c4\p\include -external:IC:\Users\<USER>\.conan2\p\mapbo1b62b48748b82\p\include -external:IC:\Users\<USER>\.conan2\p\rapidf7a3355ba53c4\p\include -external:IC:\Users\<USER>\.conan2\p\stb6342cecb318f5\p\include -external:IC:\Users\<USER>\.conan2\p\boosta79d738920bc3\p\include -external:IC:\Users\<USER>\.conan2\p\scrip4cfd014ad4660\p\include -external:IC:\Users\<USER>\.conan2\p\nearg388d58da7a54c\p\include -external:IC:\Users\<USER>\.conan2\p\b\curaebb245e64b5ac7\p\include -external:IC:\Users\<USER>\.conan2\p\asio-c31dc2a380bf0\p\include -external:IC:\Users\<USER>\.conan2\p\grpc8b6c73b4e5756\p\include -external:IC:\Users\<USER>\.conan2\p\absei6a15444d60d47\p\include -external:W0
  OBJECT_DIR = CMakeFiles\_CuraEngine.dir
  OBJECT_FILE_DIR = CMakeFiles\_CuraEngine.dir\src
  TARGET_COMPILE_PDB = CMakeFiles\_CuraEngine.dir\_CuraEngine.pdb
  TARGET_PDB = _CuraEngine.pdb

build CMakeFiles\_CuraEngine.dir\src\Slice.cpp.obj: CXX_COMPILER___CuraEngine_unscanned_Release C$:\Users\wsd07\vscode\Cura-Dev\CuraEngine\src\Slice.cpp || cmake_object_order_depends_target__CuraEngine
  CONFIG = Release
  DEFINES = -DAGRPC_BOOST_ASIO -DARCUS -DBOOST_ALL_NO_LIB -DBOOST_STACKTRACE_USE_NOOP -DBOOST_STACKTRACE_USE_WINDBG -DBOOST_STACKTRACE_USE_WINDBG_CACHED -DCARES_STATICLIB -DCURA_ENGINE_HASH=\"unknown\" -DCURA_ENGINE_VERSION=\"5.11.0-alpha.0\" -DENABLE_PLUGINS -DNOMINMAX -DSENTRY_ENVIRONMENT=\"development\" -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSTB_TEXTEDIT_KEYTYPE=unsigned
  FLAGS = /DWIN32 /D_WINDOWS /EHsc /O2 /Ob2 /DNDEBUG -std:c++20 -MD /utf-8 /permissive-
  INCLUDES = -IC:\Users\<USER>\vscode\Cura-Dev\CuraEngine\include -IC:\Users\<USER>\vscode\Cura-Dev\CuraEngine\build\Release -external:IC:\Users\<USER>\.conan2\p\b\arcusee7666f716199\p\include -external:IC:\Users\<USER>\.conan2\p\proto47a676cb9257b\p\include -external:IC:\Users\<USER>\.conan2\p\zlibcfb9789dc2a53\p\include -external:IC:\Users\<USER>\.conan2\p\spdlo2d4b540ab0f22\p\include -external:IC:\Users\<USER>\.conan2\p\fmtdb20aad6e1bd4\p\include -external:IC:\Users\<USER>\.conan2\p\range0301bf3d76d5d\p\include -external:IC:\Users\<USER>\.conan2\p\b\clippf8ddcd4a5d961\p\include -external:IC:\Users\<USER>\.conan2\p\mapbo34ee9aeb574f9\p\include -external:IC:\Users\<USER>\.conan2\p\mapbofe72fb50cf7c4\p\include -external:IC:\Users\<USER>\.conan2\p\mapbo1b62b48748b82\p\include -external:IC:\Users\<USER>\.conan2\p\rapidf7a3355ba53c4\p\include -external:IC:\Users\<USER>\.conan2\p\stb6342cecb318f5\p\include -external:IC:\Users\<USER>\.conan2\p\boosta79d738920bc3\p\include -external:IC:\Users\<USER>\.conan2\p\scrip4cfd014ad4660\p\include -external:IC:\Users\<USER>\.conan2\p\nearg388d58da7a54c\p\include -external:IC:\Users\<USER>\.conan2\p\b\curaebb245e64b5ac7\p\include -external:IC:\Users\<USER>\.conan2\p\asio-c31dc2a380bf0\p\include -external:IC:\Users\<USER>\.conan2\p\grpc8b6c73b4e5756\p\include -external:IC:\Users\<USER>\.conan2\p\absei6a15444d60d47\p\include -external:W0
  OBJECT_DIR = CMakeFiles\_CuraEngine.dir
  OBJECT_FILE_DIR = CMakeFiles\_CuraEngine.dir\src
  TARGET_COMPILE_PDB = CMakeFiles\_CuraEngine.dir\_CuraEngine.pdb
  TARGET_PDB = _CuraEngine.pdb

build CMakeFiles\_CuraEngine.dir\src\sliceDataStorage.cpp.obj: CXX_COMPILER___CuraEngine_unscanned_Release C$:\Users\wsd07\vscode\Cura-Dev\CuraEngine\src\sliceDataStorage.cpp || cmake_object_order_depends_target__CuraEngine
  CONFIG = Release
  DEFINES = -DAGRPC_BOOST_ASIO -DARCUS -DBOOST_ALL_NO_LIB -DBOOST_STACKTRACE_USE_NOOP -DBOOST_STACKTRACE_USE_WINDBG -DBOOST_STACKTRACE_USE_WINDBG_CACHED -DCARES_STATICLIB -DCURA_ENGINE_HASH=\"unknown\" -DCURA_ENGINE_VERSION=\"5.11.0-alpha.0\" -DENABLE_PLUGINS -DNOMINMAX -DSENTRY_ENVIRONMENT=\"development\" -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSTB_TEXTEDIT_KEYTYPE=unsigned
  FLAGS = /DWIN32 /D_WINDOWS /EHsc /O2 /Ob2 /DNDEBUG -std:c++20 -MD /utf-8 /permissive-
  INCLUDES = -IC:\Users\<USER>\vscode\Cura-Dev\CuraEngine\include -IC:\Users\<USER>\vscode\Cura-Dev\CuraEngine\build\Release -external:IC:\Users\<USER>\.conan2\p\b\arcusee7666f716199\p\include -external:IC:\Users\<USER>\.conan2\p\proto47a676cb9257b\p\include -external:IC:\Users\<USER>\.conan2\p\zlibcfb9789dc2a53\p\include -external:IC:\Users\<USER>\.conan2\p\spdlo2d4b540ab0f22\p\include -external:IC:\Users\<USER>\.conan2\p\fmtdb20aad6e1bd4\p\include -external:IC:\Users\<USER>\.conan2\p\range0301bf3d76d5d\p\include -external:IC:\Users\<USER>\.conan2\p\b\clippf8ddcd4a5d961\p\include -external:IC:\Users\<USER>\.conan2\p\mapbo34ee9aeb574f9\p\include -external:IC:\Users\<USER>\.conan2\p\mapbofe72fb50cf7c4\p\include -external:IC:\Users\<USER>\.conan2\p\mapbo1b62b48748b82\p\include -external:IC:\Users\<USER>\.conan2\p\rapidf7a3355ba53c4\p\include -external:IC:\Users\<USER>\.conan2\p\stb6342cecb318f5\p\include -external:IC:\Users\<USER>\.conan2\p\boosta79d738920bc3\p\include -external:IC:\Users\<USER>\.conan2\p\scrip4cfd014ad4660\p\include -external:IC:\Users\<USER>\.conan2\p\nearg388d58da7a54c\p\include -external:IC:\Users\<USER>\.conan2\p\b\curaebb245e64b5ac7\p\include -external:IC:\Users\<USER>\.conan2\p\asio-c31dc2a380bf0\p\include -external:IC:\Users\<USER>\.conan2\p\grpc8b6c73b4e5756\p\include -external:IC:\Users\<USER>\.conan2\p\absei6a15444d60d47\p\include -external:W0
  OBJECT_DIR = CMakeFiles\_CuraEngine.dir
  OBJECT_FILE_DIR = CMakeFiles\_CuraEngine.dir\src
  TARGET_COMPILE_PDB = CMakeFiles\_CuraEngine.dir\_CuraEngine.pdb
  TARGET_PDB = _CuraEngine.pdb

build CMakeFiles\_CuraEngine.dir\src\slicer.cpp.obj: CXX_COMPILER___CuraEngine_unscanned_Release C$:\Users\wsd07\vscode\Cura-Dev\CuraEngine\src\slicer.cpp || cmake_object_order_depends_target__CuraEngine
  CONFIG = Release
  DEFINES = -DAGRPC_BOOST_ASIO -DARCUS -DBOOST_ALL_NO_LIB -DBOOST_STACKTRACE_USE_NOOP -DBOOST_STACKTRACE_USE_WINDBG -DBOOST_STACKTRACE_USE_WINDBG_CACHED -DCARES_STATICLIB -DCURA_ENGINE_HASH=\"unknown\" -DCURA_ENGINE_VERSION=\"5.11.0-alpha.0\" -DENABLE_PLUGINS -DNOMINMAX -DSENTRY_ENVIRONMENT=\"development\" -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSTB_TEXTEDIT_KEYTYPE=unsigned
  FLAGS = /DWIN32 /D_WINDOWS /EHsc /O2 /Ob2 /DNDEBUG -std:c++20 -MD /utf-8 /permissive-
  INCLUDES = -IC:\Users\<USER>\vscode\Cura-Dev\CuraEngine\include -IC:\Users\<USER>\vscode\Cura-Dev\CuraEngine\build\Release -external:IC:\Users\<USER>\.conan2\p\b\arcusee7666f716199\p\include -external:IC:\Users\<USER>\.conan2\p\proto47a676cb9257b\p\include -external:IC:\Users\<USER>\.conan2\p\zlibcfb9789dc2a53\p\include -external:IC:\Users\<USER>\.conan2\p\spdlo2d4b540ab0f22\p\include -external:IC:\Users\<USER>\.conan2\p\fmtdb20aad6e1bd4\p\include -external:IC:\Users\<USER>\.conan2\p\range0301bf3d76d5d\p\include -external:IC:\Users\<USER>\.conan2\p\b\clippf8ddcd4a5d961\p\include -external:IC:\Users\<USER>\.conan2\p\mapbo34ee9aeb574f9\p\include -external:IC:\Users\<USER>\.conan2\p\mapbofe72fb50cf7c4\p\include -external:IC:\Users\<USER>\.conan2\p\mapbo1b62b48748b82\p\include -external:IC:\Users\<USER>\.conan2\p\rapidf7a3355ba53c4\p\include -external:IC:\Users\<USER>\.conan2\p\stb6342cecb318f5\p\include -external:IC:\Users\<USER>\.conan2\p\boosta79d738920bc3\p\include -external:IC:\Users\<USER>\.conan2\p\scrip4cfd014ad4660\p\include -external:IC:\Users\<USER>\.conan2\p\nearg388d58da7a54c\p\include -external:IC:\Users\<USER>\.conan2\p\b\curaebb245e64b5ac7\p\include -external:IC:\Users\<USER>\.conan2\p\asio-c31dc2a380bf0\p\include -external:IC:\Users\<USER>\.conan2\p\grpc8b6c73b4e5756\p\include -external:IC:\Users\<USER>\.conan2\p\absei6a15444d60d47\p\include -external:W0
  OBJECT_DIR = CMakeFiles\_CuraEngine.dir
  OBJECT_FILE_DIR = CMakeFiles\_CuraEngine.dir\src
  TARGET_COMPILE_PDB = CMakeFiles\_CuraEngine.dir\_CuraEngine.pdb
  TARGET_PDB = _CuraEngine.pdb

build CMakeFiles\_CuraEngine.dir\src\support.cpp.obj: CXX_COMPILER___CuraEngine_unscanned_Release C$:\Users\wsd07\vscode\Cura-Dev\CuraEngine\src\support.cpp || cmake_object_order_depends_target__CuraEngine
  CONFIG = Release
  DEFINES = -DAGRPC_BOOST_ASIO -DARCUS -DBOOST_ALL_NO_LIB -DBOOST_STACKTRACE_USE_NOOP -DBOOST_STACKTRACE_USE_WINDBG -DBOOST_STACKTRACE_USE_WINDBG_CACHED -DCARES_STATICLIB -DCURA_ENGINE_HASH=\"unknown\" -DCURA_ENGINE_VERSION=\"5.11.0-alpha.0\" -DENABLE_PLUGINS -DNOMINMAX -DSENTRY_ENVIRONMENT=\"development\" -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSTB_TEXTEDIT_KEYTYPE=unsigned
  FLAGS = /DWIN32 /D_WINDOWS /EHsc /O2 /Ob2 /DNDEBUG -std:c++20 -MD /utf-8 /permissive-
  INCLUDES = -IC:\Users\<USER>\vscode\Cura-Dev\CuraEngine\include -IC:\Users\<USER>\vscode\Cura-Dev\CuraEngine\build\Release -external:IC:\Users\<USER>\.conan2\p\b\arcusee7666f716199\p\include -external:IC:\Users\<USER>\.conan2\p\proto47a676cb9257b\p\include -external:IC:\Users\<USER>\.conan2\p\zlibcfb9789dc2a53\p\include -external:IC:\Users\<USER>\.conan2\p\spdlo2d4b540ab0f22\p\include -external:IC:\Users\<USER>\.conan2\p\fmtdb20aad6e1bd4\p\include -external:IC:\Users\<USER>\.conan2\p\range0301bf3d76d5d\p\include -external:IC:\Users\<USER>\.conan2\p\b\clippf8ddcd4a5d961\p\include -external:IC:\Users\<USER>\.conan2\p\mapbo34ee9aeb574f9\p\include -external:IC:\Users\<USER>\.conan2\p\mapbofe72fb50cf7c4\p\include -external:IC:\Users\<USER>\.conan2\p\mapbo1b62b48748b82\p\include -external:IC:\Users\<USER>\.conan2\p\rapidf7a3355ba53c4\p\include -external:IC:\Users\<USER>\.conan2\p\stb6342cecb318f5\p\include -external:IC:\Users\<USER>\.conan2\p\boosta79d738920bc3\p\include -external:IC:\Users\<USER>\.conan2\p\scrip4cfd014ad4660\p\include -external:IC:\Users\<USER>\.conan2\p\nearg388d58da7a54c\p\include -external:IC:\Users\<USER>\.conan2\p\b\curaebb245e64b5ac7\p\include -external:IC:\Users\<USER>\.conan2\p\asio-c31dc2a380bf0\p\include -external:IC:\Users\<USER>\.conan2\p\grpc8b6c73b4e5756\p\include -external:IC:\Users\<USER>\.conan2\p\absei6a15444d60d47\p\include -external:W0
  OBJECT_DIR = CMakeFiles\_CuraEngine.dir
  OBJECT_FILE_DIR = CMakeFiles\_CuraEngine.dir\src
  TARGET_COMPILE_PDB = CMakeFiles\_CuraEngine.dir\_CuraEngine.pdb
  TARGET_PDB = _CuraEngine.pdb

build CMakeFiles\_CuraEngine.dir\src\timeEstimate.cpp.obj: CXX_COMPILER___CuraEngine_unscanned_Release C$:\Users\wsd07\vscode\Cura-Dev\CuraEngine\src\timeEstimate.cpp || cmake_object_order_depends_target__CuraEngine
  CONFIG = Release
  DEFINES = -DAGRPC_BOOST_ASIO -DARCUS -DBOOST_ALL_NO_LIB -DBOOST_STACKTRACE_USE_NOOP -DBOOST_STACKTRACE_USE_WINDBG -DBOOST_STACKTRACE_USE_WINDBG_CACHED -DCARES_STATICLIB -DCURA_ENGINE_HASH=\"unknown\" -DCURA_ENGINE_VERSION=\"5.11.0-alpha.0\" -DENABLE_PLUGINS -DNOMINMAX -DSENTRY_ENVIRONMENT=\"development\" -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSTB_TEXTEDIT_KEYTYPE=unsigned
  FLAGS = /DWIN32 /D_WINDOWS /EHsc /O2 /Ob2 /DNDEBUG -std:c++20 -MD /utf-8 /permissive-
  INCLUDES = -IC:\Users\<USER>\vscode\Cura-Dev\CuraEngine\include -IC:\Users\<USER>\vscode\Cura-Dev\CuraEngine\build\Release -external:IC:\Users\<USER>\.conan2\p\b\arcusee7666f716199\p\include -external:IC:\Users\<USER>\.conan2\p\proto47a676cb9257b\p\include -external:IC:\Users\<USER>\.conan2\p\zlibcfb9789dc2a53\p\include -external:IC:\Users\<USER>\.conan2\p\spdlo2d4b540ab0f22\p\include -external:IC:\Users\<USER>\.conan2\p\fmtdb20aad6e1bd4\p\include -external:IC:\Users\<USER>\.conan2\p\range0301bf3d76d5d\p\include -external:IC:\Users\<USER>\.conan2\p\b\clippf8ddcd4a5d961\p\include -external:IC:\Users\<USER>\.conan2\p\mapbo34ee9aeb574f9\p\include -external:IC:\Users\<USER>\.conan2\p\mapbofe72fb50cf7c4\p\include -external:IC:\Users\<USER>\.conan2\p\mapbo1b62b48748b82\p\include -external:IC:\Users\<USER>\.conan2\p\rapidf7a3355ba53c4\p\include -external:IC:\Users\<USER>\.conan2\p\stb6342cecb318f5\p\include -external:IC:\Users\<USER>\.conan2\p\boosta79d738920bc3\p\include -external:IC:\Users\<USER>\.conan2\p\scrip4cfd014ad4660\p\include -external:IC:\Users\<USER>\.conan2\p\nearg388d58da7a54c\p\include -external:IC:\Users\<USER>\.conan2\p\b\curaebb245e64b5ac7\p\include -external:IC:\Users\<USER>\.conan2\p\asio-c31dc2a380bf0\p\include -external:IC:\Users\<USER>\.conan2\p\grpc8b6c73b4e5756\p\include -external:IC:\Users\<USER>\.conan2\p\absei6a15444d60d47\p\include -external:W0
  OBJECT_DIR = CMakeFiles\_CuraEngine.dir
  OBJECT_FILE_DIR = CMakeFiles\_CuraEngine.dir\src
  TARGET_COMPILE_PDB = CMakeFiles\_CuraEngine.dir\_CuraEngine.pdb
  TARGET_PDB = _CuraEngine.pdb

build CMakeFiles\_CuraEngine.dir\src\TopSurface.cpp.obj: CXX_COMPILER___CuraEngine_unscanned_Release C$:\Users\wsd07\vscode\Cura-Dev\CuraEngine\src\TopSurface.cpp || cmake_object_order_depends_target__CuraEngine
  CONFIG = Release
  DEFINES = -DAGRPC_BOOST_ASIO -DARCUS -DBOOST_ALL_NO_LIB -DBOOST_STACKTRACE_USE_NOOP -DBOOST_STACKTRACE_USE_WINDBG -DBOOST_STACKTRACE_USE_WINDBG_CACHED -DCARES_STATICLIB -DCURA_ENGINE_HASH=\"unknown\" -DCURA_ENGINE_VERSION=\"5.11.0-alpha.0\" -DENABLE_PLUGINS -DNOMINMAX -DSENTRY_ENVIRONMENT=\"development\" -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSTB_TEXTEDIT_KEYTYPE=unsigned
  FLAGS = /DWIN32 /D_WINDOWS /EHsc /O2 /Ob2 /DNDEBUG -std:c++20 -MD /utf-8 /permissive-
  INCLUDES = -IC:\Users\<USER>\vscode\Cura-Dev\CuraEngine\include -IC:\Users\<USER>\vscode\Cura-Dev\CuraEngine\build\Release -external:IC:\Users\<USER>\.conan2\p\b\arcusee7666f716199\p\include -external:IC:\Users\<USER>\.conan2\p\proto47a676cb9257b\p\include -external:IC:\Users\<USER>\.conan2\p\zlibcfb9789dc2a53\p\include -external:IC:\Users\<USER>\.conan2\p\spdlo2d4b540ab0f22\p\include -external:IC:\Users\<USER>\.conan2\p\fmtdb20aad6e1bd4\p\include -external:IC:\Users\<USER>\.conan2\p\range0301bf3d76d5d\p\include -external:IC:\Users\<USER>\.conan2\p\b\clippf8ddcd4a5d961\p\include -external:IC:\Users\<USER>\.conan2\p\mapbo34ee9aeb574f9\p\include -external:IC:\Users\<USER>\.conan2\p\mapbofe72fb50cf7c4\p\include -external:IC:\Users\<USER>\.conan2\p\mapbo1b62b48748b82\p\include -external:IC:\Users\<USER>\.conan2\p\rapidf7a3355ba53c4\p\include -external:IC:\Users\<USER>\.conan2\p\stb6342cecb318f5\p\include -external:IC:\Users\<USER>\.conan2\p\boosta79d738920bc3\p\include -external:IC:\Users\<USER>\.conan2\p\scrip4cfd014ad4660\p\include -external:IC:\Users\<USER>\.conan2\p\nearg388d58da7a54c\p\include -external:IC:\Users\<USER>\.conan2\p\b\curaebb245e64b5ac7\p\include -external:IC:\Users\<USER>\.conan2\p\asio-c31dc2a380bf0\p\include -external:IC:\Users\<USER>\.conan2\p\grpc8b6c73b4e5756\p\include -external:IC:\Users\<USER>\.conan2\p\absei6a15444d60d47\p\include -external:W0
  OBJECT_DIR = CMakeFiles\_CuraEngine.dir
  OBJECT_FILE_DIR = CMakeFiles\_CuraEngine.dir\src
  TARGET_COMPILE_PDB = CMakeFiles\_CuraEngine.dir\_CuraEngine.pdb
  TARGET_PDB = _CuraEngine.pdb

build CMakeFiles\_CuraEngine.dir\src\TreeSupportTipGenerator.cpp.obj: CXX_COMPILER___CuraEngine_unscanned_Release C$:\Users\wsd07\vscode\Cura-Dev\CuraEngine\src\TreeSupportTipGenerator.cpp || cmake_object_order_depends_target__CuraEngine
  CONFIG = Release
  DEFINES = -DAGRPC_BOOST_ASIO -DARCUS -DBOOST_ALL_NO_LIB -DBOOST_STACKTRACE_USE_NOOP -DBOOST_STACKTRACE_USE_WINDBG -DBOOST_STACKTRACE_USE_WINDBG_CACHED -DCARES_STATICLIB -DCURA_ENGINE_HASH=\"unknown\" -DCURA_ENGINE_VERSION=\"5.11.0-alpha.0\" -DENABLE_PLUGINS -DNOMINMAX -DSENTRY_ENVIRONMENT=\"development\" -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSTB_TEXTEDIT_KEYTYPE=unsigned
  FLAGS = /DWIN32 /D_WINDOWS /EHsc /O2 /Ob2 /DNDEBUG -std:c++20 -MD /utf-8 /permissive-
  INCLUDES = -IC:\Users\<USER>\vscode\Cura-Dev\CuraEngine\include -IC:\Users\<USER>\vscode\Cura-Dev\CuraEngine\build\Release -external:IC:\Users\<USER>\.conan2\p\b\arcusee7666f716199\p\include -external:IC:\Users\<USER>\.conan2\p\proto47a676cb9257b\p\include -external:IC:\Users\<USER>\.conan2\p\zlibcfb9789dc2a53\p\include -external:IC:\Users\<USER>\.conan2\p\spdlo2d4b540ab0f22\p\include -external:IC:\Users\<USER>\.conan2\p\fmtdb20aad6e1bd4\p\include -external:IC:\Users\<USER>\.conan2\p\range0301bf3d76d5d\p\include -external:IC:\Users\<USER>\.conan2\p\b\clippf8ddcd4a5d961\p\include -external:IC:\Users\<USER>\.conan2\p\mapbo34ee9aeb574f9\p\include -external:IC:\Users\<USER>\.conan2\p\mapbofe72fb50cf7c4\p\include -external:IC:\Users\<USER>\.conan2\p\mapbo1b62b48748b82\p\include -external:IC:\Users\<USER>\.conan2\p\rapidf7a3355ba53c4\p\include -external:IC:\Users\<USER>\.conan2\p\stb6342cecb318f5\p\include -external:IC:\Users\<USER>\.conan2\p\boosta79d738920bc3\p\include -external:IC:\Users\<USER>\.conan2\p\scrip4cfd014ad4660\p\include -external:IC:\Users\<USER>\.conan2\p\nearg388d58da7a54c\p\include -external:IC:\Users\<USER>\.conan2\p\b\curaebb245e64b5ac7\p\include -external:IC:\Users\<USER>\.conan2\p\asio-c31dc2a380bf0\p\include -external:IC:\Users\<USER>\.conan2\p\grpc8b6c73b4e5756\p\include -external:IC:\Users\<USER>\.conan2\p\absei6a15444d60d47\p\include -external:W0
  OBJECT_DIR = CMakeFiles\_CuraEngine.dir
  OBJECT_FILE_DIR = CMakeFiles\_CuraEngine.dir\src
  TARGET_COMPILE_PDB = CMakeFiles\_CuraEngine.dir\_CuraEngine.pdb
  TARGET_PDB = _CuraEngine.pdb

build CMakeFiles\_CuraEngine.dir\src\TreeModelVolumes.cpp.obj: CXX_COMPILER___CuraEngine_unscanned_Release C$:\Users\wsd07\vscode\Cura-Dev\CuraEngine\src\TreeModelVolumes.cpp || cmake_object_order_depends_target__CuraEngine
  CONFIG = Release
  DEFINES = -DAGRPC_BOOST_ASIO -DARCUS -DBOOST_ALL_NO_LIB -DBOOST_STACKTRACE_USE_NOOP -DBOOST_STACKTRACE_USE_WINDBG -DBOOST_STACKTRACE_USE_WINDBG_CACHED -DCARES_STATICLIB -DCURA_ENGINE_HASH=\"unknown\" -DCURA_ENGINE_VERSION=\"5.11.0-alpha.0\" -DENABLE_PLUGINS -DNOMINMAX -DSENTRY_ENVIRONMENT=\"development\" -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSTB_TEXTEDIT_KEYTYPE=unsigned
  FLAGS = /DWIN32 /D_WINDOWS /EHsc /O2 /Ob2 /DNDEBUG -std:c++20 -MD /utf-8 /permissive-
  INCLUDES = -IC:\Users\<USER>\vscode\Cura-Dev\CuraEngine\include -IC:\Users\<USER>\vscode\Cura-Dev\CuraEngine\build\Release -external:IC:\Users\<USER>\.conan2\p\b\arcusee7666f716199\p\include -external:IC:\Users\<USER>\.conan2\p\proto47a676cb9257b\p\include -external:IC:\Users\<USER>\.conan2\p\zlibcfb9789dc2a53\p\include -external:IC:\Users\<USER>\.conan2\p\spdlo2d4b540ab0f22\p\include -external:IC:\Users\<USER>\.conan2\p\fmtdb20aad6e1bd4\p\include -external:IC:\Users\<USER>\.conan2\p\range0301bf3d76d5d\p\include -external:IC:\Users\<USER>\.conan2\p\b\clippf8ddcd4a5d961\p\include -external:IC:\Users\<USER>\.conan2\p\mapbo34ee9aeb574f9\p\include -external:IC:\Users\<USER>\.conan2\p\mapbofe72fb50cf7c4\p\include -external:IC:\Users\<USER>\.conan2\p\mapbo1b62b48748b82\p\include -external:IC:\Users\<USER>\.conan2\p\rapidf7a3355ba53c4\p\include -external:IC:\Users\<USER>\.conan2\p\stb6342cecb318f5\p\include -external:IC:\Users\<USER>\.conan2\p\boosta79d738920bc3\p\include -external:IC:\Users\<USER>\.conan2\p\scrip4cfd014ad4660\p\include -external:IC:\Users\<USER>\.conan2\p\nearg388d58da7a54c\p\include -external:IC:\Users\<USER>\.conan2\p\b\curaebb245e64b5ac7\p\include -external:IC:\Users\<USER>\.conan2\p\asio-c31dc2a380bf0\p\include -external:IC:\Users\<USER>\.conan2\p\grpc8b6c73b4e5756\p\include -external:IC:\Users\<USER>\.conan2\p\absei6a15444d60d47\p\include -external:W0
  OBJECT_DIR = CMakeFiles\_CuraEngine.dir
  OBJECT_FILE_DIR = CMakeFiles\_CuraEngine.dir\src
  TARGET_COMPILE_PDB = CMakeFiles\_CuraEngine.dir\_CuraEngine.pdb
  TARGET_PDB = _CuraEngine.pdb

build CMakeFiles\_CuraEngine.dir\src\TreeSupport.cpp.obj: CXX_COMPILER___CuraEngine_unscanned_Release C$:\Users\wsd07\vscode\Cura-Dev\CuraEngine\src\TreeSupport.cpp || cmake_object_order_depends_target__CuraEngine
  CONFIG = Release
  DEFINES = -DAGRPC_BOOST_ASIO -DARCUS -DBOOST_ALL_NO_LIB -DBOOST_STACKTRACE_USE_NOOP -DBOOST_STACKTRACE_USE_WINDBG -DBOOST_STACKTRACE_USE_WINDBG_CACHED -DCARES_STATICLIB -DCURA_ENGINE_HASH=\"unknown\" -DCURA_ENGINE_VERSION=\"5.11.0-alpha.0\" -DENABLE_PLUGINS -DNOMINMAX -DSENTRY_ENVIRONMENT=\"development\" -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSTB_TEXTEDIT_KEYTYPE=unsigned
  FLAGS = /DWIN32 /D_WINDOWS /EHsc /O2 /Ob2 /DNDEBUG -std:c++20 -MD /utf-8 /permissive-
  INCLUDES = -IC:\Users\<USER>\vscode\Cura-Dev\CuraEngine\include -IC:\Users\<USER>\vscode\Cura-Dev\CuraEngine\build\Release -external:IC:\Users\<USER>\.conan2\p\b\arcusee7666f716199\p\include -external:IC:\Users\<USER>\.conan2\p\proto47a676cb9257b\p\include -external:IC:\Users\<USER>\.conan2\p\zlibcfb9789dc2a53\p\include -external:IC:\Users\<USER>\.conan2\p\spdlo2d4b540ab0f22\p\include -external:IC:\Users\<USER>\.conan2\p\fmtdb20aad6e1bd4\p\include -external:IC:\Users\<USER>\.conan2\p\range0301bf3d76d5d\p\include -external:IC:\Users\<USER>\.conan2\p\b\clippf8ddcd4a5d961\p\include -external:IC:\Users\<USER>\.conan2\p\mapbo34ee9aeb574f9\p\include -external:IC:\Users\<USER>\.conan2\p\mapbofe72fb50cf7c4\p\include -external:IC:\Users\<USER>\.conan2\p\mapbo1b62b48748b82\p\include -external:IC:\Users\<USER>\.conan2\p\rapidf7a3355ba53c4\p\include -external:IC:\Users\<USER>\.conan2\p\stb6342cecb318f5\p\include -external:IC:\Users\<USER>\.conan2\p\boosta79d738920bc3\p\include -external:IC:\Users\<USER>\.conan2\p\scrip4cfd014ad4660\p\include -external:IC:\Users\<USER>\.conan2\p\nearg388d58da7a54c\p\include -external:IC:\Users\<USER>\.conan2\p\b\curaebb245e64b5ac7\p\include -external:IC:\Users\<USER>\.conan2\p\asio-c31dc2a380bf0\p\include -external:IC:\Users\<USER>\.conan2\p\grpc8b6c73b4e5756\p\include -external:IC:\Users\<USER>\.conan2\p\absei6a15444d60d47\p\include -external:W0
  OBJECT_DIR = CMakeFiles\_CuraEngine.dir
  OBJECT_FILE_DIR = CMakeFiles\_CuraEngine.dir\src
  TARGET_COMPILE_PDB = CMakeFiles\_CuraEngine.dir\_CuraEngine.pdb
  TARGET_PDB = _CuraEngine.pdb

build CMakeFiles\_CuraEngine.dir\src\WallsComputation.cpp.obj: CXX_COMPILER___CuraEngine_unscanned_Release C$:\Users\wsd07\vscode\Cura-Dev\CuraEngine\src\WallsComputation.cpp || cmake_object_order_depends_target__CuraEngine
  CONFIG = Release
  DEFINES = -DAGRPC_BOOST_ASIO -DARCUS -DBOOST_ALL_NO_LIB -DBOOST_STACKTRACE_USE_NOOP -DBOOST_STACKTRACE_USE_WINDBG -DBOOST_STACKTRACE_USE_WINDBG_CACHED -DCARES_STATICLIB -DCURA_ENGINE_HASH=\"unknown\" -DCURA_ENGINE_VERSION=\"5.11.0-alpha.0\" -DENABLE_PLUGINS -DNOMINMAX -DSENTRY_ENVIRONMENT=\"development\" -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSTB_TEXTEDIT_KEYTYPE=unsigned
  FLAGS = /DWIN32 /D_WINDOWS /EHsc /O2 /Ob2 /DNDEBUG -std:c++20 -MD /utf-8 /permissive-
  INCLUDES = -IC:\Users\<USER>\vscode\Cura-Dev\CuraEngine\include -IC:\Users\<USER>\vscode\Cura-Dev\CuraEngine\build\Release -external:IC:\Users\<USER>\.conan2\p\b\arcusee7666f716199\p\include -external:IC:\Users\<USER>\.conan2\p\proto47a676cb9257b\p\include -external:IC:\Users\<USER>\.conan2\p\zlibcfb9789dc2a53\p\include -external:IC:\Users\<USER>\.conan2\p\spdlo2d4b540ab0f22\p\include -external:IC:\Users\<USER>\.conan2\p\fmtdb20aad6e1bd4\p\include -external:IC:\Users\<USER>\.conan2\p\range0301bf3d76d5d\p\include -external:IC:\Users\<USER>\.conan2\p\b\clippf8ddcd4a5d961\p\include -external:IC:\Users\<USER>\.conan2\p\mapbo34ee9aeb574f9\p\include -external:IC:\Users\<USER>\.conan2\p\mapbofe72fb50cf7c4\p\include -external:IC:\Users\<USER>\.conan2\p\mapbo1b62b48748b82\p\include -external:IC:\Users\<USER>\.conan2\p\rapidf7a3355ba53c4\p\include -external:IC:\Users\<USER>\.conan2\p\stb6342cecb318f5\p\include -external:IC:\Users\<USER>\.conan2\p\boosta79d738920bc3\p\include -external:IC:\Users\<USER>\.conan2\p\scrip4cfd014ad4660\p\include -external:IC:\Users\<USER>\.conan2\p\nearg388d58da7a54c\p\include -external:IC:\Users\<USER>\.conan2\p\b\curaebb245e64b5ac7\p\include -external:IC:\Users\<USER>\.conan2\p\asio-c31dc2a380bf0\p\include -external:IC:\Users\<USER>\.conan2\p\grpc8b6c73b4e5756\p\include -external:IC:\Users\<USER>\.conan2\p\absei6a15444d60d47\p\include -external:W0
  OBJECT_DIR = CMakeFiles\_CuraEngine.dir
  OBJECT_FILE_DIR = CMakeFiles\_CuraEngine.dir\src
  TARGET_COMPILE_PDB = CMakeFiles\_CuraEngine.dir\_CuraEngine.pdb
  TARGET_PDB = _CuraEngine.pdb

build CMakeFiles\_CuraEngine.dir\src\WallToolPaths.cpp.obj: CXX_COMPILER___CuraEngine_unscanned_Release C$:\Users\wsd07\vscode\Cura-Dev\CuraEngine\src\WallToolPaths.cpp || cmake_object_order_depends_target__CuraEngine
  CONFIG = Release
  DEFINES = -DAGRPC_BOOST_ASIO -DARCUS -DBOOST_ALL_NO_LIB -DBOOST_STACKTRACE_USE_NOOP -DBOOST_STACKTRACE_USE_WINDBG -DBOOST_STACKTRACE_USE_WINDBG_CACHED -DCARES_STATICLIB -DCURA_ENGINE_HASH=\"unknown\" -DCURA_ENGINE_VERSION=\"5.11.0-alpha.0\" -DENABLE_PLUGINS -DNOMINMAX -DSENTRY_ENVIRONMENT=\"development\" -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSTB_TEXTEDIT_KEYTYPE=unsigned
  FLAGS = /DWIN32 /D_WINDOWS /EHsc /O2 /Ob2 /DNDEBUG -std:c++20 -MD /utf-8 /permissive-
  INCLUDES = -IC:\Users\<USER>\vscode\Cura-Dev\CuraEngine\include -IC:\Users\<USER>\vscode\Cura-Dev\CuraEngine\build\Release -external:IC:\Users\<USER>\.conan2\p\b\arcusee7666f716199\p\include -external:IC:\Users\<USER>\.conan2\p\proto47a676cb9257b\p\include -external:IC:\Users\<USER>\.conan2\p\zlibcfb9789dc2a53\p\include -external:IC:\Users\<USER>\.conan2\p\spdlo2d4b540ab0f22\p\include -external:IC:\Users\<USER>\.conan2\p\fmtdb20aad6e1bd4\p\include -external:IC:\Users\<USER>\.conan2\p\range0301bf3d76d5d\p\include -external:IC:\Users\<USER>\.conan2\p\b\clippf8ddcd4a5d961\p\include -external:IC:\Users\<USER>\.conan2\p\mapbo34ee9aeb574f9\p\include -external:IC:\Users\<USER>\.conan2\p\mapbofe72fb50cf7c4\p\include -external:IC:\Users\<USER>\.conan2\p\mapbo1b62b48748b82\p\include -external:IC:\Users\<USER>\.conan2\p\rapidf7a3355ba53c4\p\include -external:IC:\Users\<USER>\.conan2\p\stb6342cecb318f5\p\include -external:IC:\Users\<USER>\.conan2\p\boosta79d738920bc3\p\include -external:IC:\Users\<USER>\.conan2\p\scrip4cfd014ad4660\p\include -external:IC:\Users\<USER>\.conan2\p\nearg388d58da7a54c\p\include -external:IC:\Users\<USER>\.conan2\p\b\curaebb245e64b5ac7\p\include -external:IC:\Users\<USER>\.conan2\p\asio-c31dc2a380bf0\p\include -external:IC:\Users\<USER>\.conan2\p\grpc8b6c73b4e5756\p\include -external:IC:\Users\<USER>\.conan2\p\absei6a15444d60d47\p\include -external:W0
  OBJECT_DIR = CMakeFiles\_CuraEngine.dir
  OBJECT_FILE_DIR = CMakeFiles\_CuraEngine.dir\src
  TARGET_COMPILE_PDB = CMakeFiles\_CuraEngine.dir\_CuraEngine.pdb
  TARGET_PDB = _CuraEngine.pdb

build CMakeFiles\_CuraEngine.dir\src\BeadingStrategy\BeadingStrategy.cpp.obj: CXX_COMPILER___CuraEngine_unscanned_Release C$:\Users\wsd07\vscode\Cura-Dev\CuraEngine\src\BeadingStrategy\BeadingStrategy.cpp || cmake_object_order_depends_target__CuraEngine
  CONFIG = Release
  DEFINES = -DAGRPC_BOOST_ASIO -DARCUS -DBOOST_ALL_NO_LIB -DBOOST_STACKTRACE_USE_NOOP -DBOOST_STACKTRACE_USE_WINDBG -DBOOST_STACKTRACE_USE_WINDBG_CACHED -DCARES_STATICLIB -DCURA_ENGINE_HASH=\"unknown\" -DCURA_ENGINE_VERSION=\"5.11.0-alpha.0\" -DENABLE_PLUGINS -DNOMINMAX -DSENTRY_ENVIRONMENT=\"development\" -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSTB_TEXTEDIT_KEYTYPE=unsigned
  FLAGS = /DWIN32 /D_WINDOWS /EHsc /O2 /Ob2 /DNDEBUG -std:c++20 -MD /utf-8 /permissive-
  INCLUDES = -IC:\Users\<USER>\vscode\Cura-Dev\CuraEngine\include -IC:\Users\<USER>\vscode\Cura-Dev\CuraEngine\build\Release -external:IC:\Users\<USER>\.conan2\p\b\arcusee7666f716199\p\include -external:IC:\Users\<USER>\.conan2\p\proto47a676cb9257b\p\include -external:IC:\Users\<USER>\.conan2\p\zlibcfb9789dc2a53\p\include -external:IC:\Users\<USER>\.conan2\p\spdlo2d4b540ab0f22\p\include -external:IC:\Users\<USER>\.conan2\p\fmtdb20aad6e1bd4\p\include -external:IC:\Users\<USER>\.conan2\p\range0301bf3d76d5d\p\include -external:IC:\Users\<USER>\.conan2\p\b\clippf8ddcd4a5d961\p\include -external:IC:\Users\<USER>\.conan2\p\mapbo34ee9aeb574f9\p\include -external:IC:\Users\<USER>\.conan2\p\mapbofe72fb50cf7c4\p\include -external:IC:\Users\<USER>\.conan2\p\mapbo1b62b48748b82\p\include -external:IC:\Users\<USER>\.conan2\p\rapidf7a3355ba53c4\p\include -external:IC:\Users\<USER>\.conan2\p\stb6342cecb318f5\p\include -external:IC:\Users\<USER>\.conan2\p\boosta79d738920bc3\p\include -external:IC:\Users\<USER>\.conan2\p\scrip4cfd014ad4660\p\include -external:IC:\Users\<USER>\.conan2\p\nearg388d58da7a54c\p\include -external:IC:\Users\<USER>\.conan2\p\b\curaebb245e64b5ac7\p\include -external:IC:\Users\<USER>\.conan2\p\asio-c31dc2a380bf0\p\include -external:IC:\Users\<USER>\.conan2\p\grpc8b6c73b4e5756\p\include -external:IC:\Users\<USER>\.conan2\p\absei6a15444d60d47\p\include -external:W0
  OBJECT_DIR = CMakeFiles\_CuraEngine.dir
  OBJECT_FILE_DIR = CMakeFiles\_CuraEngine.dir\src\BeadingStrategy
  TARGET_COMPILE_PDB = CMakeFiles\_CuraEngine.dir\_CuraEngine.pdb
  TARGET_PDB = _CuraEngine.pdb

build CMakeFiles\_CuraEngine.dir\src\BeadingStrategy\BeadingStrategyFactory.cpp.obj: CXX_COMPILER___CuraEngine_unscanned_Release C$:\Users\wsd07\vscode\Cura-Dev\CuraEngine\src\BeadingStrategy\BeadingStrategyFactory.cpp || cmake_object_order_depends_target__CuraEngine
  CONFIG = Release
  DEFINES = -DAGRPC_BOOST_ASIO -DARCUS -DBOOST_ALL_NO_LIB -DBOOST_STACKTRACE_USE_NOOP -DBOOST_STACKTRACE_USE_WINDBG -DBOOST_STACKTRACE_USE_WINDBG_CACHED -DCARES_STATICLIB -DCURA_ENGINE_HASH=\"unknown\" -DCURA_ENGINE_VERSION=\"5.11.0-alpha.0\" -DENABLE_PLUGINS -DNOMINMAX -DSENTRY_ENVIRONMENT=\"development\" -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSTB_TEXTEDIT_KEYTYPE=unsigned
  FLAGS = /DWIN32 /D_WINDOWS /EHsc /O2 /Ob2 /DNDEBUG -std:c++20 -MD /utf-8 /permissive-
  INCLUDES = -IC:\Users\<USER>\vscode\Cura-Dev\CuraEngine\include -IC:\Users\<USER>\vscode\Cura-Dev\CuraEngine\build\Release -external:IC:\Users\<USER>\.conan2\p\b\arcusee7666f716199\p\include -external:IC:\Users\<USER>\.conan2\p\proto47a676cb9257b\p\include -external:IC:\Users\<USER>\.conan2\p\zlibcfb9789dc2a53\p\include -external:IC:\Users\<USER>\.conan2\p\spdlo2d4b540ab0f22\p\include -external:IC:\Users\<USER>\.conan2\p\fmtdb20aad6e1bd4\p\include -external:IC:\Users\<USER>\.conan2\p\range0301bf3d76d5d\p\include -external:IC:\Users\<USER>\.conan2\p\b\clippf8ddcd4a5d961\p\include -external:IC:\Users\<USER>\.conan2\p\mapbo34ee9aeb574f9\p\include -external:IC:\Users\<USER>\.conan2\p\mapbofe72fb50cf7c4\p\include -external:IC:\Users\<USER>\.conan2\p\mapbo1b62b48748b82\p\include -external:IC:\Users\<USER>\.conan2\p\rapidf7a3355ba53c4\p\include -external:IC:\Users\<USER>\.conan2\p\stb6342cecb318f5\p\include -external:IC:\Users\<USER>\.conan2\p\boosta79d738920bc3\p\include -external:IC:\Users\<USER>\.conan2\p\scrip4cfd014ad4660\p\include -external:IC:\Users\<USER>\.conan2\p\nearg388d58da7a54c\p\include -external:IC:\Users\<USER>\.conan2\p\b\curaebb245e64b5ac7\p\include -external:IC:\Users\<USER>\.conan2\p\asio-c31dc2a380bf0\p\include -external:IC:\Users\<USER>\.conan2\p\grpc8b6c73b4e5756\p\include -external:IC:\Users\<USER>\.conan2\p\absei6a15444d60d47\p\include -external:W0
  OBJECT_DIR = CMakeFiles\_CuraEngine.dir
  OBJECT_FILE_DIR = CMakeFiles\_CuraEngine.dir\src\BeadingStrategy
  TARGET_COMPILE_PDB = CMakeFiles\_CuraEngine.dir\_CuraEngine.pdb
  TARGET_PDB = _CuraEngine.pdb

build CMakeFiles\_CuraEngine.dir\src\BeadingStrategy\DistributedBeadingStrategy.cpp.obj: CXX_COMPILER___CuraEngine_unscanned_Release C$:\Users\wsd07\vscode\Cura-Dev\CuraEngine\src\BeadingStrategy\DistributedBeadingStrategy.cpp || cmake_object_order_depends_target__CuraEngine
  CONFIG = Release
  DEFINES = -DAGRPC_BOOST_ASIO -DARCUS -DBOOST_ALL_NO_LIB -DBOOST_STACKTRACE_USE_NOOP -DBOOST_STACKTRACE_USE_WINDBG -DBOOST_STACKTRACE_USE_WINDBG_CACHED -DCARES_STATICLIB -DCURA_ENGINE_HASH=\"unknown\" -DCURA_ENGINE_VERSION=\"5.11.0-alpha.0\" -DENABLE_PLUGINS -DNOMINMAX -DSENTRY_ENVIRONMENT=\"development\" -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSTB_TEXTEDIT_KEYTYPE=unsigned
  FLAGS = /DWIN32 /D_WINDOWS /EHsc /O2 /Ob2 /DNDEBUG -std:c++20 -MD /utf-8 /permissive-
  INCLUDES = -IC:\Users\<USER>\vscode\Cura-Dev\CuraEngine\include -IC:\Users\<USER>\vscode\Cura-Dev\CuraEngine\build\Release -external:IC:\Users\<USER>\.conan2\p\b\arcusee7666f716199\p\include -external:IC:\Users\<USER>\.conan2\p\proto47a676cb9257b\p\include -external:IC:\Users\<USER>\.conan2\p\zlibcfb9789dc2a53\p\include -external:IC:\Users\<USER>\.conan2\p\spdlo2d4b540ab0f22\p\include -external:IC:\Users\<USER>\.conan2\p\fmtdb20aad6e1bd4\p\include -external:IC:\Users\<USER>\.conan2\p\range0301bf3d76d5d\p\include -external:IC:\Users\<USER>\.conan2\p\b\clippf8ddcd4a5d961\p\include -external:IC:\Users\<USER>\.conan2\p\mapbo34ee9aeb574f9\p\include -external:IC:\Users\<USER>\.conan2\p\mapbofe72fb50cf7c4\p\include -external:IC:\Users\<USER>\.conan2\p\mapbo1b62b48748b82\p\include -external:IC:\Users\<USER>\.conan2\p\rapidf7a3355ba53c4\p\include -external:IC:\Users\<USER>\.conan2\p\stb6342cecb318f5\p\include -external:IC:\Users\<USER>\.conan2\p\boosta79d738920bc3\p\include -external:IC:\Users\<USER>\.conan2\p\scrip4cfd014ad4660\p\include -external:IC:\Users\<USER>\.conan2\p\nearg388d58da7a54c\p\include -external:IC:\Users\<USER>\.conan2\p\b\curaebb245e64b5ac7\p\include -external:IC:\Users\<USER>\.conan2\p\asio-c31dc2a380bf0\p\include -external:IC:\Users\<USER>\.conan2\p\grpc8b6c73b4e5756\p\include -external:IC:\Users\<USER>\.conan2\p\absei6a15444d60d47\p\include -external:W0
  OBJECT_DIR = CMakeFiles\_CuraEngine.dir
  OBJECT_FILE_DIR = CMakeFiles\_CuraEngine.dir\src\BeadingStrategy
  TARGET_COMPILE_PDB = CMakeFiles\_CuraEngine.dir\_CuraEngine.pdb
  TARGET_PDB = _CuraEngine.pdb

build CMakeFiles\_CuraEngine.dir\src\BeadingStrategy\LimitedBeadingStrategy.cpp.obj: CXX_COMPILER___CuraEngine_unscanned_Release C$:\Users\wsd07\vscode\Cura-Dev\CuraEngine\src\BeadingStrategy\LimitedBeadingStrategy.cpp || cmake_object_order_depends_target__CuraEngine
  CONFIG = Release
  DEFINES = -DAGRPC_BOOST_ASIO -DARCUS -DBOOST_ALL_NO_LIB -DBOOST_STACKTRACE_USE_NOOP -DBOOST_STACKTRACE_USE_WINDBG -DBOOST_STACKTRACE_USE_WINDBG_CACHED -DCARES_STATICLIB -DCURA_ENGINE_HASH=\"unknown\" -DCURA_ENGINE_VERSION=\"5.11.0-alpha.0\" -DENABLE_PLUGINS -DNOMINMAX -DSENTRY_ENVIRONMENT=\"development\" -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSTB_TEXTEDIT_KEYTYPE=unsigned
  FLAGS = /DWIN32 /D_WINDOWS /EHsc /O2 /Ob2 /DNDEBUG -std:c++20 -MD /utf-8 /permissive-
  INCLUDES = -IC:\Users\<USER>\vscode\Cura-Dev\CuraEngine\include -IC:\Users\<USER>\vscode\Cura-Dev\CuraEngine\build\Release -external:IC:\Users\<USER>\.conan2\p\b\arcusee7666f716199\p\include -external:IC:\Users\<USER>\.conan2\p\proto47a676cb9257b\p\include -external:IC:\Users\<USER>\.conan2\p\zlibcfb9789dc2a53\p\include -external:IC:\Users\<USER>\.conan2\p\spdlo2d4b540ab0f22\p\include -external:IC:\Users\<USER>\.conan2\p\fmtdb20aad6e1bd4\p\include -external:IC:\Users\<USER>\.conan2\p\range0301bf3d76d5d\p\include -external:IC:\Users\<USER>\.conan2\p\b\clippf8ddcd4a5d961\p\include -external:IC:\Users\<USER>\.conan2\p\mapbo34ee9aeb574f9\p\include -external:IC:\Users\<USER>\.conan2\p\mapbofe72fb50cf7c4\p\include -external:IC:\Users\<USER>\.conan2\p\mapbo1b62b48748b82\p\include -external:IC:\Users\<USER>\.conan2\p\rapidf7a3355ba53c4\p\include -external:IC:\Users\<USER>\.conan2\p\stb6342cecb318f5\p\include -external:IC:\Users\<USER>\.conan2\p\boosta79d738920bc3\p\include -external:IC:\Users\<USER>\.conan2\p\scrip4cfd014ad4660\p\include -external:IC:\Users\<USER>\.conan2\p\nearg388d58da7a54c\p\include -external:IC:\Users\<USER>\.conan2\p\b\curaebb245e64b5ac7\p\include -external:IC:\Users\<USER>\.conan2\p\asio-c31dc2a380bf0\p\include -external:IC:\Users\<USER>\.conan2\p\grpc8b6c73b4e5756\p\include -external:IC:\Users\<USER>\.conan2\p\absei6a15444d60d47\p\include -external:W0
  OBJECT_DIR = CMakeFiles\_CuraEngine.dir
  OBJECT_FILE_DIR = CMakeFiles\_CuraEngine.dir\src\BeadingStrategy
  TARGET_COMPILE_PDB = CMakeFiles\_CuraEngine.dir\_CuraEngine.pdb
  TARGET_PDB = _CuraEngine.pdb

build CMakeFiles\_CuraEngine.dir\src\BeadingStrategy\RedistributeBeadingStrategy.cpp.obj: CXX_COMPILER___CuraEngine_unscanned_Release C$:\Users\wsd07\vscode\Cura-Dev\CuraEngine\src\BeadingStrategy\RedistributeBeadingStrategy.cpp || cmake_object_order_depends_target__CuraEngine
  CONFIG = Release
  DEFINES = -DAGRPC_BOOST_ASIO -DARCUS -DBOOST_ALL_NO_LIB -DBOOST_STACKTRACE_USE_NOOP -DBOOST_STACKTRACE_USE_WINDBG -DBOOST_STACKTRACE_USE_WINDBG_CACHED -DCARES_STATICLIB -DCURA_ENGINE_HASH=\"unknown\" -DCURA_ENGINE_VERSION=\"5.11.0-alpha.0\" -DENABLE_PLUGINS -DNOMINMAX -DSENTRY_ENVIRONMENT=\"development\" -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSTB_TEXTEDIT_KEYTYPE=unsigned
  FLAGS = /DWIN32 /D_WINDOWS /EHsc /O2 /Ob2 /DNDEBUG -std:c++20 -MD /utf-8 /permissive-
  INCLUDES = -IC:\Users\<USER>\vscode\Cura-Dev\CuraEngine\include -IC:\Users\<USER>\vscode\Cura-Dev\CuraEngine\build\Release -external:IC:\Users\<USER>\.conan2\p\b\arcusee7666f716199\p\include -external:IC:\Users\<USER>\.conan2\p\proto47a676cb9257b\p\include -external:IC:\Users\<USER>\.conan2\p\zlibcfb9789dc2a53\p\include -external:IC:\Users\<USER>\.conan2\p\spdlo2d4b540ab0f22\p\include -external:IC:\Users\<USER>\.conan2\p\fmtdb20aad6e1bd4\p\include -external:IC:\Users\<USER>\.conan2\p\range0301bf3d76d5d\p\include -external:IC:\Users\<USER>\.conan2\p\b\clippf8ddcd4a5d961\p\include -external:IC:\Users\<USER>\.conan2\p\mapbo34ee9aeb574f9\p\include -external:IC:\Users\<USER>\.conan2\p\mapbofe72fb50cf7c4\p\include -external:IC:\Users\<USER>\.conan2\p\mapbo1b62b48748b82\p\include -external:IC:\Users\<USER>\.conan2\p\rapidf7a3355ba53c4\p\include -external:IC:\Users\<USER>\.conan2\p\stb6342cecb318f5\p\include -external:IC:\Users\<USER>\.conan2\p\boosta79d738920bc3\p\include -external:IC:\Users\<USER>\.conan2\p\scrip4cfd014ad4660\p\include -external:IC:\Users\<USER>\.conan2\p\nearg388d58da7a54c\p\include -external:IC:\Users\<USER>\.conan2\p\b\curaebb245e64b5ac7\p\include -external:IC:\Users\<USER>\.conan2\p\asio-c31dc2a380bf0\p\include -external:IC:\Users\<USER>\.conan2\p\grpc8b6c73b4e5756\p\include -external:IC:\Users\<USER>\.conan2\p\absei6a15444d60d47\p\include -external:W0
  OBJECT_DIR = CMakeFiles\_CuraEngine.dir
  OBJECT_FILE_DIR = CMakeFiles\_CuraEngine.dir\src\BeadingStrategy
  TARGET_COMPILE_PDB = CMakeFiles\_CuraEngine.dir\_CuraEngine.pdb
  TARGET_PDB = _CuraEngine.pdb

build CMakeFiles\_CuraEngine.dir\src\BeadingStrategy\WideningBeadingStrategy.cpp.obj: CXX_COMPILER___CuraEngine_unscanned_Release C$:\Users\wsd07\vscode\Cura-Dev\CuraEngine\src\BeadingStrategy\WideningBeadingStrategy.cpp || cmake_object_order_depends_target__CuraEngine
  CONFIG = Release
  DEFINES = -DAGRPC_BOOST_ASIO -DARCUS -DBOOST_ALL_NO_LIB -DBOOST_STACKTRACE_USE_NOOP -DBOOST_STACKTRACE_USE_WINDBG -DBOOST_STACKTRACE_USE_WINDBG_CACHED -DCARES_STATICLIB -DCURA_ENGINE_HASH=\"unknown\" -DCURA_ENGINE_VERSION=\"5.11.0-alpha.0\" -DENABLE_PLUGINS -DNOMINMAX -DSENTRY_ENVIRONMENT=\"development\" -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSTB_TEXTEDIT_KEYTYPE=unsigned
  FLAGS = /DWIN32 /D_WINDOWS /EHsc /O2 /Ob2 /DNDEBUG -std:c++20 -MD /utf-8 /permissive-
  INCLUDES = -IC:\Users\<USER>\vscode\Cura-Dev\CuraEngine\include -IC:\Users\<USER>\vscode\Cura-Dev\CuraEngine\build\Release -external:IC:\Users\<USER>\.conan2\p\b\arcusee7666f716199\p\include -external:IC:\Users\<USER>\.conan2\p\proto47a676cb9257b\p\include -external:IC:\Users\<USER>\.conan2\p\zlibcfb9789dc2a53\p\include -external:IC:\Users\<USER>\.conan2\p\spdlo2d4b540ab0f22\p\include -external:IC:\Users\<USER>\.conan2\p\fmtdb20aad6e1bd4\p\include -external:IC:\Users\<USER>\.conan2\p\range0301bf3d76d5d\p\include -external:IC:\Users\<USER>\.conan2\p\b\clippf8ddcd4a5d961\p\include -external:IC:\Users\<USER>\.conan2\p\mapbo34ee9aeb574f9\p\include -external:IC:\Users\<USER>\.conan2\p\mapbofe72fb50cf7c4\p\include -external:IC:\Users\<USER>\.conan2\p\mapbo1b62b48748b82\p\include -external:IC:\Users\<USER>\.conan2\p\rapidf7a3355ba53c4\p\include -external:IC:\Users\<USER>\.conan2\p\stb6342cecb318f5\p\include -external:IC:\Users\<USER>\.conan2\p\boosta79d738920bc3\p\include -external:IC:\Users\<USER>\.conan2\p\scrip4cfd014ad4660\p\include -external:IC:\Users\<USER>\.conan2\p\nearg388d58da7a54c\p\include -external:IC:\Users\<USER>\.conan2\p\b\curaebb245e64b5ac7\p\include -external:IC:\Users\<USER>\.conan2\p\asio-c31dc2a380bf0\p\include -external:IC:\Users\<USER>\.conan2\p\grpc8b6c73b4e5756\p\include -external:IC:\Users\<USER>\.conan2\p\absei6a15444d60d47\p\include -external:W0
  OBJECT_DIR = CMakeFiles\_CuraEngine.dir
  OBJECT_FILE_DIR = CMakeFiles\_CuraEngine.dir\src\BeadingStrategy
  TARGET_COMPILE_PDB = CMakeFiles\_CuraEngine.dir\_CuraEngine.pdb
  TARGET_PDB = _CuraEngine.pdb

build CMakeFiles\_CuraEngine.dir\src\BeadingStrategy\OuterWallInsetBeadingStrategy.cpp.obj: CXX_COMPILER___CuraEngine_unscanned_Release C$:\Users\wsd07\vscode\Cura-Dev\CuraEngine\src\BeadingStrategy\OuterWallInsetBeadingStrategy.cpp || cmake_object_order_depends_target__CuraEngine
  CONFIG = Release
  DEFINES = -DAGRPC_BOOST_ASIO -DARCUS -DBOOST_ALL_NO_LIB -DBOOST_STACKTRACE_USE_NOOP -DBOOST_STACKTRACE_USE_WINDBG -DBOOST_STACKTRACE_USE_WINDBG_CACHED -DCARES_STATICLIB -DCURA_ENGINE_HASH=\"unknown\" -DCURA_ENGINE_VERSION=\"5.11.0-alpha.0\" -DENABLE_PLUGINS -DNOMINMAX -DSENTRY_ENVIRONMENT=\"development\" -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSTB_TEXTEDIT_KEYTYPE=unsigned
  FLAGS = /DWIN32 /D_WINDOWS /EHsc /O2 /Ob2 /DNDEBUG -std:c++20 -MD /utf-8 /permissive-
  INCLUDES = -IC:\Users\<USER>\vscode\Cura-Dev\CuraEngine\include -IC:\Users\<USER>\vscode\Cura-Dev\CuraEngine\build\Release -external:IC:\Users\<USER>\.conan2\p\b\arcusee7666f716199\p\include -external:IC:\Users\<USER>\.conan2\p\proto47a676cb9257b\p\include -external:IC:\Users\<USER>\.conan2\p\zlibcfb9789dc2a53\p\include -external:IC:\Users\<USER>\.conan2\p\spdlo2d4b540ab0f22\p\include -external:IC:\Users\<USER>\.conan2\p\fmtdb20aad6e1bd4\p\include -external:IC:\Users\<USER>\.conan2\p\range0301bf3d76d5d\p\include -external:IC:\Users\<USER>\.conan2\p\b\clippf8ddcd4a5d961\p\include -external:IC:\Users\<USER>\.conan2\p\mapbo34ee9aeb574f9\p\include -external:IC:\Users\<USER>\.conan2\p\mapbofe72fb50cf7c4\p\include -external:IC:\Users\<USER>\.conan2\p\mapbo1b62b48748b82\p\include -external:IC:\Users\<USER>\.conan2\p\rapidf7a3355ba53c4\p\include -external:IC:\Users\<USER>\.conan2\p\stb6342cecb318f5\p\include -external:IC:\Users\<USER>\.conan2\p\boosta79d738920bc3\p\include -external:IC:\Users\<USER>\.conan2\p\scrip4cfd014ad4660\p\include -external:IC:\Users\<USER>\.conan2\p\nearg388d58da7a54c\p\include -external:IC:\Users\<USER>\.conan2\p\b\curaebb245e64b5ac7\p\include -external:IC:\Users\<USER>\.conan2\p\asio-c31dc2a380bf0\p\include -external:IC:\Users\<USER>\.conan2\p\grpc8b6c73b4e5756\p\include -external:IC:\Users\<USER>\.conan2\p\absei6a15444d60d47\p\include -external:W0
  OBJECT_DIR = CMakeFiles\_CuraEngine.dir
  OBJECT_FILE_DIR = CMakeFiles\_CuraEngine.dir\src\BeadingStrategy
  TARGET_COMPILE_PDB = CMakeFiles\_CuraEngine.dir\_CuraEngine.pdb
  TARGET_PDB = _CuraEngine.pdb

build CMakeFiles\_CuraEngine.dir\src\communication\ArcusCommunication.cpp.obj: CXX_COMPILER___CuraEngine_unscanned_Release C$:\Users\wsd07\vscode\Cura-Dev\CuraEngine\src\communication\ArcusCommunication.cpp || cmake_object_order_depends_target__CuraEngine
  CONFIG = Release
  DEFINES = -DAGRPC_BOOST_ASIO -DARCUS -DBOOST_ALL_NO_LIB -DBOOST_STACKTRACE_USE_NOOP -DBOOST_STACKTRACE_USE_WINDBG -DBOOST_STACKTRACE_USE_WINDBG_CACHED -DCARES_STATICLIB -DCURA_ENGINE_HASH=\"unknown\" -DCURA_ENGINE_VERSION=\"5.11.0-alpha.0\" -DENABLE_PLUGINS -DNOMINMAX -DSENTRY_ENVIRONMENT=\"development\" -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSTB_TEXTEDIT_KEYTYPE=unsigned
  FLAGS = /DWIN32 /D_WINDOWS /EHsc /O2 /Ob2 /DNDEBUG -std:c++20 -MD /utf-8 /permissive-
  INCLUDES = -IC:\Users\<USER>\vscode\Cura-Dev\CuraEngine\include -IC:\Users\<USER>\vscode\Cura-Dev\CuraEngine\build\Release -external:IC:\Users\<USER>\.conan2\p\b\arcusee7666f716199\p\include -external:IC:\Users\<USER>\.conan2\p\proto47a676cb9257b\p\include -external:IC:\Users\<USER>\.conan2\p\zlibcfb9789dc2a53\p\include -external:IC:\Users\<USER>\.conan2\p\spdlo2d4b540ab0f22\p\include -external:IC:\Users\<USER>\.conan2\p\fmtdb20aad6e1bd4\p\include -external:IC:\Users\<USER>\.conan2\p\range0301bf3d76d5d\p\include -external:IC:\Users\<USER>\.conan2\p\b\clippf8ddcd4a5d961\p\include -external:IC:\Users\<USER>\.conan2\p\mapbo34ee9aeb574f9\p\include -external:IC:\Users\<USER>\.conan2\p\mapbofe72fb50cf7c4\p\include -external:IC:\Users\<USER>\.conan2\p\mapbo1b62b48748b82\p\include -external:IC:\Users\<USER>\.conan2\p\rapidf7a3355ba53c4\p\include -external:IC:\Users\<USER>\.conan2\p\stb6342cecb318f5\p\include -external:IC:\Users\<USER>\.conan2\p\boosta79d738920bc3\p\include -external:IC:\Users\<USER>\.conan2\p\scrip4cfd014ad4660\p\include -external:IC:\Users\<USER>\.conan2\p\nearg388d58da7a54c\p\include -external:IC:\Users\<USER>\.conan2\p\b\curaebb245e64b5ac7\p\include -external:IC:\Users\<USER>\.conan2\p\asio-c31dc2a380bf0\p\include -external:IC:\Users\<USER>\.conan2\p\grpc8b6c73b4e5756\p\include -external:IC:\Users\<USER>\.conan2\p\absei6a15444d60d47\p\include -external:W0
  OBJECT_DIR = CMakeFiles\_CuraEngine.dir
  OBJECT_FILE_DIR = CMakeFiles\_CuraEngine.dir\src\communication
  TARGET_COMPILE_PDB = CMakeFiles\_CuraEngine.dir\_CuraEngine.pdb
  TARGET_PDB = _CuraEngine.pdb

build CMakeFiles\_CuraEngine.dir\src\communication\ArcusCommunicationPrivate.cpp.obj: CXX_COMPILER___CuraEngine_unscanned_Release C$:\Users\wsd07\vscode\Cura-Dev\CuraEngine\src\communication\ArcusCommunicationPrivate.cpp || cmake_object_order_depends_target__CuraEngine
  CONFIG = Release
  DEFINES = -DAGRPC_BOOST_ASIO -DARCUS -DBOOST_ALL_NO_LIB -DBOOST_STACKTRACE_USE_NOOP -DBOOST_STACKTRACE_USE_WINDBG -DBOOST_STACKTRACE_USE_WINDBG_CACHED -DCARES_STATICLIB -DCURA_ENGINE_HASH=\"unknown\" -DCURA_ENGINE_VERSION=\"5.11.0-alpha.0\" -DENABLE_PLUGINS -DNOMINMAX -DSENTRY_ENVIRONMENT=\"development\" -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSTB_TEXTEDIT_KEYTYPE=unsigned
  FLAGS = /DWIN32 /D_WINDOWS /EHsc /O2 /Ob2 /DNDEBUG -std:c++20 -MD /utf-8 /permissive-
  INCLUDES = -IC:\Users\<USER>\vscode\Cura-Dev\CuraEngine\include -IC:\Users\<USER>\vscode\Cura-Dev\CuraEngine\build\Release -external:IC:\Users\<USER>\.conan2\p\b\arcusee7666f716199\p\include -external:IC:\Users\<USER>\.conan2\p\proto47a676cb9257b\p\include -external:IC:\Users\<USER>\.conan2\p\zlibcfb9789dc2a53\p\include -external:IC:\Users\<USER>\.conan2\p\spdlo2d4b540ab0f22\p\include -external:IC:\Users\<USER>\.conan2\p\fmtdb20aad6e1bd4\p\include -external:IC:\Users\<USER>\.conan2\p\range0301bf3d76d5d\p\include -external:IC:\Users\<USER>\.conan2\p\b\clippf8ddcd4a5d961\p\include -external:IC:\Users\<USER>\.conan2\p\mapbo34ee9aeb574f9\p\include -external:IC:\Users\<USER>\.conan2\p\mapbofe72fb50cf7c4\p\include -external:IC:\Users\<USER>\.conan2\p\mapbo1b62b48748b82\p\include -external:IC:\Users\<USER>\.conan2\p\rapidf7a3355ba53c4\p\include -external:IC:\Users\<USER>\.conan2\p\stb6342cecb318f5\p\include -external:IC:\Users\<USER>\.conan2\p\boosta79d738920bc3\p\include -external:IC:\Users\<USER>\.conan2\p\scrip4cfd014ad4660\p\include -external:IC:\Users\<USER>\.conan2\p\nearg388d58da7a54c\p\include -external:IC:\Users\<USER>\.conan2\p\b\curaebb245e64b5ac7\p\include -external:IC:\Users\<USER>\.conan2\p\asio-c31dc2a380bf0\p\include -external:IC:\Users\<USER>\.conan2\p\grpc8b6c73b4e5756\p\include -external:IC:\Users\<USER>\.conan2\p\absei6a15444d60d47\p\include -external:W0
  OBJECT_DIR = CMakeFiles\_CuraEngine.dir
  OBJECT_FILE_DIR = CMakeFiles\_CuraEngine.dir\src\communication
  TARGET_COMPILE_PDB = CMakeFiles\_CuraEngine.dir\_CuraEngine.pdb
  TARGET_PDB = _CuraEngine.pdb

build CMakeFiles\_CuraEngine.dir\src\communication\CommandLine.cpp.obj: CXX_COMPILER___CuraEngine_unscanned_Release C$:\Users\wsd07\vscode\Cura-Dev\CuraEngine\src\communication\CommandLine.cpp || cmake_object_order_depends_target__CuraEngine
  CONFIG = Release
  DEFINES = -DAGRPC_BOOST_ASIO -DARCUS -DBOOST_ALL_NO_LIB -DBOOST_STACKTRACE_USE_NOOP -DBOOST_STACKTRACE_USE_WINDBG -DBOOST_STACKTRACE_USE_WINDBG_CACHED -DCARES_STATICLIB -DCURA_ENGINE_HASH=\"unknown\" -DCURA_ENGINE_VERSION=\"5.11.0-alpha.0\" -DENABLE_PLUGINS -DNOMINMAX -DSENTRY_ENVIRONMENT=\"development\" -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSTB_TEXTEDIT_KEYTYPE=unsigned
  FLAGS = /DWIN32 /D_WINDOWS /EHsc /O2 /Ob2 /DNDEBUG -std:c++20 -MD /utf-8 /permissive-
  INCLUDES = -IC:\Users\<USER>\vscode\Cura-Dev\CuraEngine\include -IC:\Users\<USER>\vscode\Cura-Dev\CuraEngine\build\Release -external:IC:\Users\<USER>\.conan2\p\b\arcusee7666f716199\p\include -external:IC:\Users\<USER>\.conan2\p\proto47a676cb9257b\p\include -external:IC:\Users\<USER>\.conan2\p\zlibcfb9789dc2a53\p\include -external:IC:\Users\<USER>\.conan2\p\spdlo2d4b540ab0f22\p\include -external:IC:\Users\<USER>\.conan2\p\fmtdb20aad6e1bd4\p\include -external:IC:\Users\<USER>\.conan2\p\range0301bf3d76d5d\p\include -external:IC:\Users\<USER>\.conan2\p\b\clippf8ddcd4a5d961\p\include -external:IC:\Users\<USER>\.conan2\p\mapbo34ee9aeb574f9\p\include -external:IC:\Users\<USER>\.conan2\p\mapbofe72fb50cf7c4\p\include -external:IC:\Users\<USER>\.conan2\p\mapbo1b62b48748b82\p\include -external:IC:\Users\<USER>\.conan2\p\rapidf7a3355ba53c4\p\include -external:IC:\Users\<USER>\.conan2\p\stb6342cecb318f5\p\include -external:IC:\Users\<USER>\.conan2\p\boosta79d738920bc3\p\include -external:IC:\Users\<USER>\.conan2\p\scrip4cfd014ad4660\p\include -external:IC:\Users\<USER>\.conan2\p\nearg388d58da7a54c\p\include -external:IC:\Users\<USER>\.conan2\p\b\curaebb245e64b5ac7\p\include -external:IC:\Users\<USER>\.conan2\p\asio-c31dc2a380bf0\p\include -external:IC:\Users\<USER>\.conan2\p\grpc8b6c73b4e5756\p\include -external:IC:\Users\<USER>\.conan2\p\absei6a15444d60d47\p\include -external:W0
  OBJECT_DIR = CMakeFiles\_CuraEngine.dir
  OBJECT_FILE_DIR = CMakeFiles\_CuraEngine.dir\src\communication
  TARGET_COMPILE_PDB = CMakeFiles\_CuraEngine.dir\_CuraEngine.pdb
  TARGET_PDB = _CuraEngine.pdb

build CMakeFiles\_CuraEngine.dir\src\communication\EmscriptenCommunication.cpp.obj: CXX_COMPILER___CuraEngine_unscanned_Release C$:\Users\wsd07\vscode\Cura-Dev\CuraEngine\src\communication\EmscriptenCommunication.cpp || cmake_object_order_depends_target__CuraEngine
  CONFIG = Release
  DEFINES = -DAGRPC_BOOST_ASIO -DARCUS -DBOOST_ALL_NO_LIB -DBOOST_STACKTRACE_USE_NOOP -DBOOST_STACKTRACE_USE_WINDBG -DBOOST_STACKTRACE_USE_WINDBG_CACHED -DCARES_STATICLIB -DCURA_ENGINE_HASH=\"unknown\" -DCURA_ENGINE_VERSION=\"5.11.0-alpha.0\" -DENABLE_PLUGINS -DNOMINMAX -DSENTRY_ENVIRONMENT=\"development\" -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSTB_TEXTEDIT_KEYTYPE=unsigned
  FLAGS = /DWIN32 /D_WINDOWS /EHsc /O2 /Ob2 /DNDEBUG -std:c++20 -MD /utf-8 /permissive-
  INCLUDES = -IC:\Users\<USER>\vscode\Cura-Dev\CuraEngine\include -IC:\Users\<USER>\vscode\Cura-Dev\CuraEngine\build\Release -external:IC:\Users\<USER>\.conan2\p\b\arcusee7666f716199\p\include -external:IC:\Users\<USER>\.conan2\p\proto47a676cb9257b\p\include -external:IC:\Users\<USER>\.conan2\p\zlibcfb9789dc2a53\p\include -external:IC:\Users\<USER>\.conan2\p\spdlo2d4b540ab0f22\p\include -external:IC:\Users\<USER>\.conan2\p\fmtdb20aad6e1bd4\p\include -external:IC:\Users\<USER>\.conan2\p\range0301bf3d76d5d\p\include -external:IC:\Users\<USER>\.conan2\p\b\clippf8ddcd4a5d961\p\include -external:IC:\Users\<USER>\.conan2\p\mapbo34ee9aeb574f9\p\include -external:IC:\Users\<USER>\.conan2\p\mapbofe72fb50cf7c4\p\include -external:IC:\Users\<USER>\.conan2\p\mapbo1b62b48748b82\p\include -external:IC:\Users\<USER>\.conan2\p\rapidf7a3355ba53c4\p\include -external:IC:\Users\<USER>\.conan2\p\stb6342cecb318f5\p\include -external:IC:\Users\<USER>\.conan2\p\boosta79d738920bc3\p\include -external:IC:\Users\<USER>\.conan2\p\scrip4cfd014ad4660\p\include -external:IC:\Users\<USER>\.conan2\p\nearg388d58da7a54c\p\include -external:IC:\Users\<USER>\.conan2\p\b\curaebb245e64b5ac7\p\include -external:IC:\Users\<USER>\.conan2\p\asio-c31dc2a380bf0\p\include -external:IC:\Users\<USER>\.conan2\p\grpc8b6c73b4e5756\p\include -external:IC:\Users\<USER>\.conan2\p\absei6a15444d60d47\p\include -external:W0
  OBJECT_DIR = CMakeFiles\_CuraEngine.dir
  OBJECT_FILE_DIR = CMakeFiles\_CuraEngine.dir\src\communication
  TARGET_COMPILE_PDB = CMakeFiles\_CuraEngine.dir\_CuraEngine.pdb
  TARGET_PDB = _CuraEngine.pdb

build CMakeFiles\_CuraEngine.dir\src\communication\Listener.cpp.obj: CXX_COMPILER___CuraEngine_unscanned_Release C$:\Users\wsd07\vscode\Cura-Dev\CuraEngine\src\communication\Listener.cpp || cmake_object_order_depends_target__CuraEngine
  CONFIG = Release
  DEFINES = -DAGRPC_BOOST_ASIO -DARCUS -DBOOST_ALL_NO_LIB -DBOOST_STACKTRACE_USE_NOOP -DBOOST_STACKTRACE_USE_WINDBG -DBOOST_STACKTRACE_USE_WINDBG_CACHED -DCARES_STATICLIB -DCURA_ENGINE_HASH=\"unknown\" -DCURA_ENGINE_VERSION=\"5.11.0-alpha.0\" -DENABLE_PLUGINS -DNOMINMAX -DSENTRY_ENVIRONMENT=\"development\" -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSTB_TEXTEDIT_KEYTYPE=unsigned
  FLAGS = /DWIN32 /D_WINDOWS /EHsc /O2 /Ob2 /DNDEBUG -std:c++20 -MD /utf-8 /permissive-
  INCLUDES = -IC:\Users\<USER>\vscode\Cura-Dev\CuraEngine\include -IC:\Users\<USER>\vscode\Cura-Dev\CuraEngine\build\Release -external:IC:\Users\<USER>\.conan2\p\b\arcusee7666f716199\p\include -external:IC:\Users\<USER>\.conan2\p\proto47a676cb9257b\p\include -external:IC:\Users\<USER>\.conan2\p\zlibcfb9789dc2a53\p\include -external:IC:\Users\<USER>\.conan2\p\spdlo2d4b540ab0f22\p\include -external:IC:\Users\<USER>\.conan2\p\fmtdb20aad6e1bd4\p\include -external:IC:\Users\<USER>\.conan2\p\range0301bf3d76d5d\p\include -external:IC:\Users\<USER>\.conan2\p\b\clippf8ddcd4a5d961\p\include -external:IC:\Users\<USER>\.conan2\p\mapbo34ee9aeb574f9\p\include -external:IC:\Users\<USER>\.conan2\p\mapbofe72fb50cf7c4\p\include -external:IC:\Users\<USER>\.conan2\p\mapbo1b62b48748b82\p\include -external:IC:\Users\<USER>\.conan2\p\rapidf7a3355ba53c4\p\include -external:IC:\Users\<USER>\.conan2\p\stb6342cecb318f5\p\include -external:IC:\Users\<USER>\.conan2\p\boosta79d738920bc3\p\include -external:IC:\Users\<USER>\.conan2\p\scrip4cfd014ad4660\p\include -external:IC:\Users\<USER>\.conan2\p\nearg388d58da7a54c\p\include -external:IC:\Users\<USER>\.conan2\p\b\curaebb245e64b5ac7\p\include -external:IC:\Users\<USER>\.conan2\p\asio-c31dc2a380bf0\p\include -external:IC:\Users\<USER>\.conan2\p\grpc8b6c73b4e5756\p\include -external:IC:\Users\<USER>\.conan2\p\absei6a15444d60d47\p\include -external:W0
  OBJECT_DIR = CMakeFiles\_CuraEngine.dir
  OBJECT_FILE_DIR = CMakeFiles\_CuraEngine.dir\src\communication
  TARGET_COMPILE_PDB = CMakeFiles\_CuraEngine.dir\_CuraEngine.pdb
  TARGET_PDB = _CuraEngine.pdb

build CMakeFiles\_CuraEngine.dir\src\infill\ImageBasedDensityProvider.cpp.obj: CXX_COMPILER___CuraEngine_unscanned_Release C$:\Users\wsd07\vscode\Cura-Dev\CuraEngine\src\infill\ImageBasedDensityProvider.cpp || cmake_object_order_depends_target__CuraEngine
  CONFIG = Release
  DEFINES = -DAGRPC_BOOST_ASIO -DARCUS -DBOOST_ALL_NO_LIB -DBOOST_STACKTRACE_USE_NOOP -DBOOST_STACKTRACE_USE_WINDBG -DBOOST_STACKTRACE_USE_WINDBG_CACHED -DCARES_STATICLIB -DCURA_ENGINE_HASH=\"unknown\" -DCURA_ENGINE_VERSION=\"5.11.0-alpha.0\" -DENABLE_PLUGINS -DNOMINMAX -DSENTRY_ENVIRONMENT=\"development\" -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSTB_TEXTEDIT_KEYTYPE=unsigned
  FLAGS = /DWIN32 /D_WINDOWS /EHsc /O2 /Ob2 /DNDEBUG -std:c++20 -MD /utf-8 /permissive-
  INCLUDES = -IC:\Users\<USER>\vscode\Cura-Dev\CuraEngine\include -IC:\Users\<USER>\vscode\Cura-Dev\CuraEngine\build\Release -external:IC:\Users\<USER>\.conan2\p\b\arcusee7666f716199\p\include -external:IC:\Users\<USER>\.conan2\p\proto47a676cb9257b\p\include -external:IC:\Users\<USER>\.conan2\p\zlibcfb9789dc2a53\p\include -external:IC:\Users\<USER>\.conan2\p\spdlo2d4b540ab0f22\p\include -external:IC:\Users\<USER>\.conan2\p\fmtdb20aad6e1bd4\p\include -external:IC:\Users\<USER>\.conan2\p\range0301bf3d76d5d\p\include -external:IC:\Users\<USER>\.conan2\p\b\clippf8ddcd4a5d961\p\include -external:IC:\Users\<USER>\.conan2\p\mapbo34ee9aeb574f9\p\include -external:IC:\Users\<USER>\.conan2\p\mapbofe72fb50cf7c4\p\include -external:IC:\Users\<USER>\.conan2\p\mapbo1b62b48748b82\p\include -external:IC:\Users\<USER>\.conan2\p\rapidf7a3355ba53c4\p\include -external:IC:\Users\<USER>\.conan2\p\stb6342cecb318f5\p\include -external:IC:\Users\<USER>\.conan2\p\boosta79d738920bc3\p\include -external:IC:\Users\<USER>\.conan2\p\scrip4cfd014ad4660\p\include -external:IC:\Users\<USER>\.conan2\p\nearg388d58da7a54c\p\include -external:IC:\Users\<USER>\.conan2\p\b\curaebb245e64b5ac7\p\include -external:IC:\Users\<USER>\.conan2\p\asio-c31dc2a380bf0\p\include -external:IC:\Users\<USER>\.conan2\p\grpc8b6c73b4e5756\p\include -external:IC:\Users\<USER>\.conan2\p\absei6a15444d60d47\p\include -external:W0
  OBJECT_DIR = CMakeFiles\_CuraEngine.dir
  OBJECT_FILE_DIR = CMakeFiles\_CuraEngine.dir\src\infill
  TARGET_COMPILE_PDB = CMakeFiles\_CuraEngine.dir\_CuraEngine.pdb
  TARGET_PDB = _CuraEngine.pdb

build CMakeFiles\_CuraEngine.dir\src\infill\NoZigZagConnectorProcessor.cpp.obj: CXX_COMPILER___CuraEngine_unscanned_Release C$:\Users\wsd07\vscode\Cura-Dev\CuraEngine\src\infill\NoZigZagConnectorProcessor.cpp || cmake_object_order_depends_target__CuraEngine
  CONFIG = Release
  DEFINES = -DAGRPC_BOOST_ASIO -DARCUS -DBOOST_ALL_NO_LIB -DBOOST_STACKTRACE_USE_NOOP -DBOOST_STACKTRACE_USE_WINDBG -DBOOST_STACKTRACE_USE_WINDBG_CACHED -DCARES_STATICLIB -DCURA_ENGINE_HASH=\"unknown\" -DCURA_ENGINE_VERSION=\"5.11.0-alpha.0\" -DENABLE_PLUGINS -DNOMINMAX -DSENTRY_ENVIRONMENT=\"development\" -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSTB_TEXTEDIT_KEYTYPE=unsigned
  FLAGS = /DWIN32 /D_WINDOWS /EHsc /O2 /Ob2 /DNDEBUG -std:c++20 -MD /utf-8 /permissive-
  INCLUDES = -IC:\Users\<USER>\vscode\Cura-Dev\CuraEngine\include -IC:\Users\<USER>\vscode\Cura-Dev\CuraEngine\build\Release -external:IC:\Users\<USER>\.conan2\p\b\arcusee7666f716199\p\include -external:IC:\Users\<USER>\.conan2\p\proto47a676cb9257b\p\include -external:IC:\Users\<USER>\.conan2\p\zlibcfb9789dc2a53\p\include -external:IC:\Users\<USER>\.conan2\p\spdlo2d4b540ab0f22\p\include -external:IC:\Users\<USER>\.conan2\p\fmtdb20aad6e1bd4\p\include -external:IC:\Users\<USER>\.conan2\p\range0301bf3d76d5d\p\include -external:IC:\Users\<USER>\.conan2\p\b\clippf8ddcd4a5d961\p\include -external:IC:\Users\<USER>\.conan2\p\mapbo34ee9aeb574f9\p\include -external:IC:\Users\<USER>\.conan2\p\mapbofe72fb50cf7c4\p\include -external:IC:\Users\<USER>\.conan2\p\mapbo1b62b48748b82\p\include -external:IC:\Users\<USER>\.conan2\p\rapidf7a3355ba53c4\p\include -external:IC:\Users\<USER>\.conan2\p\stb6342cecb318f5\p\include -external:IC:\Users\<USER>\.conan2\p\boosta79d738920bc3\p\include -external:IC:\Users\<USER>\.conan2\p\scrip4cfd014ad4660\p\include -external:IC:\Users\<USER>\.conan2\p\nearg388d58da7a54c\p\include -external:IC:\Users\<USER>\.conan2\p\b\curaebb245e64b5ac7\p\include -external:IC:\Users\<USER>\.conan2\p\asio-c31dc2a380bf0\p\include -external:IC:\Users\<USER>\.conan2\p\grpc8b6c73b4e5756\p\include -external:IC:\Users\<USER>\.conan2\p\absei6a15444d60d47\p\include -external:W0
  OBJECT_DIR = CMakeFiles\_CuraEngine.dir
  OBJECT_FILE_DIR = CMakeFiles\_CuraEngine.dir\src\infill
  TARGET_COMPILE_PDB = CMakeFiles\_CuraEngine.dir\_CuraEngine.pdb
  TARGET_PDB = _CuraEngine.pdb

build CMakeFiles\_CuraEngine.dir\src\infill\ZigzagConnectorProcessor.cpp.obj: CXX_COMPILER___CuraEngine_unscanned_Release C$:\Users\wsd07\vscode\Cura-Dev\CuraEngine\src\infill\ZigzagConnectorProcessor.cpp || cmake_object_order_depends_target__CuraEngine
  CONFIG = Release
  DEFINES = -DAGRPC_BOOST_ASIO -DARCUS -DBOOST_ALL_NO_LIB -DBOOST_STACKTRACE_USE_NOOP -DBOOST_STACKTRACE_USE_WINDBG -DBOOST_STACKTRACE_USE_WINDBG_CACHED -DCARES_STATICLIB -DCURA_ENGINE_HASH=\"unknown\" -DCURA_ENGINE_VERSION=\"5.11.0-alpha.0\" -DENABLE_PLUGINS -DNOMINMAX -DSENTRY_ENVIRONMENT=\"development\" -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSTB_TEXTEDIT_KEYTYPE=unsigned
  FLAGS = /DWIN32 /D_WINDOWS /EHsc /O2 /Ob2 /DNDEBUG -std:c++20 -MD /utf-8 /permissive-
  INCLUDES = -IC:\Users\<USER>\vscode\Cura-Dev\CuraEngine\include -IC:\Users\<USER>\vscode\Cura-Dev\CuraEngine\build\Release -external:IC:\Users\<USER>\.conan2\p\b\arcusee7666f716199\p\include -external:IC:\Users\<USER>\.conan2\p\proto47a676cb9257b\p\include -external:IC:\Users\<USER>\.conan2\p\zlibcfb9789dc2a53\p\include -external:IC:\Users\<USER>\.conan2\p\spdlo2d4b540ab0f22\p\include -external:IC:\Users\<USER>\.conan2\p\fmtdb20aad6e1bd4\p\include -external:IC:\Users\<USER>\.conan2\p\range0301bf3d76d5d\p\include -external:IC:\Users\<USER>\.conan2\p\b\clippf8ddcd4a5d961\p\include -external:IC:\Users\<USER>\.conan2\p\mapbo34ee9aeb574f9\p\include -external:IC:\Users\<USER>\.conan2\p\mapbofe72fb50cf7c4\p\include -external:IC:\Users\<USER>\.conan2\p\mapbo1b62b48748b82\p\include -external:IC:\Users\<USER>\.conan2\p\rapidf7a3355ba53c4\p\include -external:IC:\Users\<USER>\.conan2\p\stb6342cecb318f5\p\include -external:IC:\Users\<USER>\.conan2\p\boosta79d738920bc3\p\include -external:IC:\Users\<USER>\.conan2\p\scrip4cfd014ad4660\p\include -external:IC:\Users\<USER>\.conan2\p\nearg388d58da7a54c\p\include -external:IC:\Users\<USER>\.conan2\p\b\curaebb245e64b5ac7\p\include -external:IC:\Users\<USER>\.conan2\p\asio-c31dc2a380bf0\p\include -external:IC:\Users\<USER>\.conan2\p\grpc8b6c73b4e5756\p\include -external:IC:\Users\<USER>\.conan2\p\absei6a15444d60d47\p\include -external:W0
  OBJECT_DIR = CMakeFiles\_CuraEngine.dir
  OBJECT_FILE_DIR = CMakeFiles\_CuraEngine.dir\src\infill
  TARGET_COMPILE_PDB = CMakeFiles\_CuraEngine.dir\_CuraEngine.pdb
  TARGET_PDB = _CuraEngine.pdb

build CMakeFiles\_CuraEngine.dir\src\infill\LightningDistanceField.cpp.obj: CXX_COMPILER___CuraEngine_unscanned_Release C$:\Users\wsd07\vscode\Cura-Dev\CuraEngine\src\infill\LightningDistanceField.cpp || cmake_object_order_depends_target__CuraEngine
  CONFIG = Release
  DEFINES = -DAGRPC_BOOST_ASIO -DARCUS -DBOOST_ALL_NO_LIB -DBOOST_STACKTRACE_USE_NOOP -DBOOST_STACKTRACE_USE_WINDBG -DBOOST_STACKTRACE_USE_WINDBG_CACHED -DCARES_STATICLIB -DCURA_ENGINE_HASH=\"unknown\" -DCURA_ENGINE_VERSION=\"5.11.0-alpha.0\" -DENABLE_PLUGINS -DNOMINMAX -DSENTRY_ENVIRONMENT=\"development\" -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSTB_TEXTEDIT_KEYTYPE=unsigned
  FLAGS = /DWIN32 /D_WINDOWS /EHsc /O2 /Ob2 /DNDEBUG -std:c++20 -MD /utf-8 /permissive-
  INCLUDES = -IC:\Users\<USER>\vscode\Cura-Dev\CuraEngine\include -IC:\Users\<USER>\vscode\Cura-Dev\CuraEngine\build\Release -external:IC:\Users\<USER>\.conan2\p\b\arcusee7666f716199\p\include -external:IC:\Users\<USER>\.conan2\p\proto47a676cb9257b\p\include -external:IC:\Users\<USER>\.conan2\p\zlibcfb9789dc2a53\p\include -external:IC:\Users\<USER>\.conan2\p\spdlo2d4b540ab0f22\p\include -external:IC:\Users\<USER>\.conan2\p\fmtdb20aad6e1bd4\p\include -external:IC:\Users\<USER>\.conan2\p\range0301bf3d76d5d\p\include -external:IC:\Users\<USER>\.conan2\p\b\clippf8ddcd4a5d961\p\include -external:IC:\Users\<USER>\.conan2\p\mapbo34ee9aeb574f9\p\include -external:IC:\Users\<USER>\.conan2\p\mapbofe72fb50cf7c4\p\include -external:IC:\Users\<USER>\.conan2\p\mapbo1b62b48748b82\p\include -external:IC:\Users\<USER>\.conan2\p\rapidf7a3355ba53c4\p\include -external:IC:\Users\<USER>\.conan2\p\stb6342cecb318f5\p\include -external:IC:\Users\<USER>\.conan2\p\boosta79d738920bc3\p\include -external:IC:\Users\<USER>\.conan2\p\scrip4cfd014ad4660\p\include -external:IC:\Users\<USER>\.conan2\p\nearg388d58da7a54c\p\include -external:IC:\Users\<USER>\.conan2\p\b\curaebb245e64b5ac7\p\include -external:IC:\Users\<USER>\.conan2\p\asio-c31dc2a380bf0\p\include -external:IC:\Users\<USER>\.conan2\p\grpc8b6c73b4e5756\p\include -external:IC:\Users\<USER>\.conan2\p\absei6a15444d60d47\p\include -external:W0
  OBJECT_DIR = CMakeFiles\_CuraEngine.dir
  OBJECT_FILE_DIR = CMakeFiles\_CuraEngine.dir\src\infill
  TARGET_COMPILE_PDB = CMakeFiles\_CuraEngine.dir\_CuraEngine.pdb
  TARGET_PDB = _CuraEngine.pdb

build CMakeFiles\_CuraEngine.dir\src\infill\LightningGenerator.cpp.obj: CXX_COMPILER___CuraEngine_unscanned_Release C$:\Users\wsd07\vscode\Cura-Dev\CuraEngine\src\infill\LightningGenerator.cpp || cmake_object_order_depends_target__CuraEngine
  CONFIG = Release
  DEFINES = -DAGRPC_BOOST_ASIO -DARCUS -DBOOST_ALL_NO_LIB -DBOOST_STACKTRACE_USE_NOOP -DBOOST_STACKTRACE_USE_WINDBG -DBOOST_STACKTRACE_USE_WINDBG_CACHED -DCARES_STATICLIB -DCURA_ENGINE_HASH=\"unknown\" -DCURA_ENGINE_VERSION=\"5.11.0-alpha.0\" -DENABLE_PLUGINS -DNOMINMAX -DSENTRY_ENVIRONMENT=\"development\" -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSTB_TEXTEDIT_KEYTYPE=unsigned
  FLAGS = /DWIN32 /D_WINDOWS /EHsc /O2 /Ob2 /DNDEBUG -std:c++20 -MD /utf-8 /permissive-
  INCLUDES = -IC:\Users\<USER>\vscode\Cura-Dev\CuraEngine\include -IC:\Users\<USER>\vscode\Cura-Dev\CuraEngine\build\Release -external:IC:\Users\<USER>\.conan2\p\b\arcusee7666f716199\p\include -external:IC:\Users\<USER>\.conan2\p\proto47a676cb9257b\p\include -external:IC:\Users\<USER>\.conan2\p\zlibcfb9789dc2a53\p\include -external:IC:\Users\<USER>\.conan2\p\spdlo2d4b540ab0f22\p\include -external:IC:\Users\<USER>\.conan2\p\fmtdb20aad6e1bd4\p\include -external:IC:\Users\<USER>\.conan2\p\range0301bf3d76d5d\p\include -external:IC:\Users\<USER>\.conan2\p\b\clippf8ddcd4a5d961\p\include -external:IC:\Users\<USER>\.conan2\p\mapbo34ee9aeb574f9\p\include -external:IC:\Users\<USER>\.conan2\p\mapbofe72fb50cf7c4\p\include -external:IC:\Users\<USER>\.conan2\p\mapbo1b62b48748b82\p\include -external:IC:\Users\<USER>\.conan2\p\rapidf7a3355ba53c4\p\include -external:IC:\Users\<USER>\.conan2\p\stb6342cecb318f5\p\include -external:IC:\Users\<USER>\.conan2\p\boosta79d738920bc3\p\include -external:IC:\Users\<USER>\.conan2\p\scrip4cfd014ad4660\p\include -external:IC:\Users\<USER>\.conan2\p\nearg388d58da7a54c\p\include -external:IC:\Users\<USER>\.conan2\p\b\curaebb245e64b5ac7\p\include -external:IC:\Users\<USER>\.conan2\p\asio-c31dc2a380bf0\p\include -external:IC:\Users\<USER>\.conan2\p\grpc8b6c73b4e5756\p\include -external:IC:\Users\<USER>\.conan2\p\absei6a15444d60d47\p\include -external:W0
  OBJECT_DIR = CMakeFiles\_CuraEngine.dir
  OBJECT_FILE_DIR = CMakeFiles\_CuraEngine.dir\src\infill
  TARGET_COMPILE_PDB = CMakeFiles\_CuraEngine.dir\_CuraEngine.pdb
  TARGET_PDB = _CuraEngine.pdb

build CMakeFiles\_CuraEngine.dir\src\infill\LightningLayer.cpp.obj: CXX_COMPILER___CuraEngine_unscanned_Release C$:\Users\wsd07\vscode\Cura-Dev\CuraEngine\src\infill\LightningLayer.cpp || cmake_object_order_depends_target__CuraEngine
  CONFIG = Release
  DEFINES = -DAGRPC_BOOST_ASIO -DARCUS -DBOOST_ALL_NO_LIB -DBOOST_STACKTRACE_USE_NOOP -DBOOST_STACKTRACE_USE_WINDBG -DBOOST_STACKTRACE_USE_WINDBG_CACHED -DCARES_STATICLIB -DCURA_ENGINE_HASH=\"unknown\" -DCURA_ENGINE_VERSION=\"5.11.0-alpha.0\" -DENABLE_PLUGINS -DNOMINMAX -DSENTRY_ENVIRONMENT=\"development\" -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSTB_TEXTEDIT_KEYTYPE=unsigned
  FLAGS = /DWIN32 /D_WINDOWS /EHsc /O2 /Ob2 /DNDEBUG -std:c++20 -MD /utf-8 /permissive-
  INCLUDES = -IC:\Users\<USER>\vscode\Cura-Dev\CuraEngine\include -IC:\Users\<USER>\vscode\Cura-Dev\CuraEngine\build\Release -external:IC:\Users\<USER>\.conan2\p\b\arcusee7666f716199\p\include -external:IC:\Users\<USER>\.conan2\p\proto47a676cb9257b\p\include -external:IC:\Users\<USER>\.conan2\p\zlibcfb9789dc2a53\p\include -external:IC:\Users\<USER>\.conan2\p\spdlo2d4b540ab0f22\p\include -external:IC:\Users\<USER>\.conan2\p\fmtdb20aad6e1bd4\p\include -external:IC:\Users\<USER>\.conan2\p\range0301bf3d76d5d\p\include -external:IC:\Users\<USER>\.conan2\p\b\clippf8ddcd4a5d961\p\include -external:IC:\Users\<USER>\.conan2\p\mapbo34ee9aeb574f9\p\include -external:IC:\Users\<USER>\.conan2\p\mapbofe72fb50cf7c4\p\include -external:IC:\Users\<USER>\.conan2\p\mapbo1b62b48748b82\p\include -external:IC:\Users\<USER>\.conan2\p\rapidf7a3355ba53c4\p\include -external:IC:\Users\<USER>\.conan2\p\stb6342cecb318f5\p\include -external:IC:\Users\<USER>\.conan2\p\boosta79d738920bc3\p\include -external:IC:\Users\<USER>\.conan2\p\scrip4cfd014ad4660\p\include -external:IC:\Users\<USER>\.conan2\p\nearg388d58da7a54c\p\include -external:IC:\Users\<USER>\.conan2\p\b\curaebb245e64b5ac7\p\include -external:IC:\Users\<USER>\.conan2\p\asio-c31dc2a380bf0\p\include -external:IC:\Users\<USER>\.conan2\p\grpc8b6c73b4e5756\p\include -external:IC:\Users\<USER>\.conan2\p\absei6a15444d60d47\p\include -external:W0
  OBJECT_DIR = CMakeFiles\_CuraEngine.dir
  OBJECT_FILE_DIR = CMakeFiles\_CuraEngine.dir\src\infill
  TARGET_COMPILE_PDB = CMakeFiles\_CuraEngine.dir\_CuraEngine.pdb
  TARGET_PDB = _CuraEngine.pdb

build CMakeFiles\_CuraEngine.dir\src\infill\LightningTreeNode.cpp.obj: CXX_COMPILER___CuraEngine_unscanned_Release C$:\Users\wsd07\vscode\Cura-Dev\CuraEngine\src\infill\LightningTreeNode.cpp || cmake_object_order_depends_target__CuraEngine
  CONFIG = Release
  DEFINES = -DAGRPC_BOOST_ASIO -DARCUS -DBOOST_ALL_NO_LIB -DBOOST_STACKTRACE_USE_NOOP -DBOOST_STACKTRACE_USE_WINDBG -DBOOST_STACKTRACE_USE_WINDBG_CACHED -DCARES_STATICLIB -DCURA_ENGINE_HASH=\"unknown\" -DCURA_ENGINE_VERSION=\"5.11.0-alpha.0\" -DENABLE_PLUGINS -DNOMINMAX -DSENTRY_ENVIRONMENT=\"development\" -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSTB_TEXTEDIT_KEYTYPE=unsigned
  FLAGS = /DWIN32 /D_WINDOWS /EHsc /O2 /Ob2 /DNDEBUG -std:c++20 -MD /utf-8 /permissive-
  INCLUDES = -IC:\Users\<USER>\vscode\Cura-Dev\CuraEngine\include -IC:\Users\<USER>\vscode\Cura-Dev\CuraEngine\build\Release -external:IC:\Users\<USER>\.conan2\p\b\arcusee7666f716199\p\include -external:IC:\Users\<USER>\.conan2\p\proto47a676cb9257b\p\include -external:IC:\Users\<USER>\.conan2\p\zlibcfb9789dc2a53\p\include -external:IC:\Users\<USER>\.conan2\p\spdlo2d4b540ab0f22\p\include -external:IC:\Users\<USER>\.conan2\p\fmtdb20aad6e1bd4\p\include -external:IC:\Users\<USER>\.conan2\p\range0301bf3d76d5d\p\include -external:IC:\Users\<USER>\.conan2\p\b\clippf8ddcd4a5d961\p\include -external:IC:\Users\<USER>\.conan2\p\mapbo34ee9aeb574f9\p\include -external:IC:\Users\<USER>\.conan2\p\mapbofe72fb50cf7c4\p\include -external:IC:\Users\<USER>\.conan2\p\mapbo1b62b48748b82\p\include -external:IC:\Users\<USER>\.conan2\p\rapidf7a3355ba53c4\p\include -external:IC:\Users\<USER>\.conan2\p\stb6342cecb318f5\p\include -external:IC:\Users\<USER>\.conan2\p\boosta79d738920bc3\p\include -external:IC:\Users\<USER>\.conan2\p\scrip4cfd014ad4660\p\include -external:IC:\Users\<USER>\.conan2\p\nearg388d58da7a54c\p\include -external:IC:\Users\<USER>\.conan2\p\b\curaebb245e64b5ac7\p\include -external:IC:\Users\<USER>\.conan2\p\asio-c31dc2a380bf0\p\include -external:IC:\Users\<USER>\.conan2\p\grpc8b6c73b4e5756\p\include -external:IC:\Users\<USER>\.conan2\p\absei6a15444d60d47\p\include -external:W0
  OBJECT_DIR = CMakeFiles\_CuraEngine.dir
  OBJECT_FILE_DIR = CMakeFiles\_CuraEngine.dir\src\infill
  TARGET_COMPILE_PDB = CMakeFiles\_CuraEngine.dir\_CuraEngine.pdb
  TARGET_PDB = _CuraEngine.pdb

build CMakeFiles\_CuraEngine.dir\src\infill\SierpinskiFill.cpp.obj: CXX_COMPILER___CuraEngine_unscanned_Release C$:\Users\wsd07\vscode\Cura-Dev\CuraEngine\src\infill\SierpinskiFill.cpp || cmake_object_order_depends_target__CuraEngine
  CONFIG = Release
  DEFINES = -DAGRPC_BOOST_ASIO -DARCUS -DBOOST_ALL_NO_LIB -DBOOST_STACKTRACE_USE_NOOP -DBOOST_STACKTRACE_USE_WINDBG -DBOOST_STACKTRACE_USE_WINDBG_CACHED -DCARES_STATICLIB -DCURA_ENGINE_HASH=\"unknown\" -DCURA_ENGINE_VERSION=\"5.11.0-alpha.0\" -DENABLE_PLUGINS -DNOMINMAX -DSENTRY_ENVIRONMENT=\"development\" -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSTB_TEXTEDIT_KEYTYPE=unsigned
  FLAGS = /DWIN32 /D_WINDOWS /EHsc /O2 /Ob2 /DNDEBUG -std:c++20 -MD /utf-8 /permissive-
  INCLUDES = -IC:\Users\<USER>\vscode\Cura-Dev\CuraEngine\include -IC:\Users\<USER>\vscode\Cura-Dev\CuraEngine\build\Release -external:IC:\Users\<USER>\.conan2\p\b\arcusee7666f716199\p\include -external:IC:\Users\<USER>\.conan2\p\proto47a676cb9257b\p\include -external:IC:\Users\<USER>\.conan2\p\zlibcfb9789dc2a53\p\include -external:IC:\Users\<USER>\.conan2\p\spdlo2d4b540ab0f22\p\include -external:IC:\Users\<USER>\.conan2\p\fmtdb20aad6e1bd4\p\include -external:IC:\Users\<USER>\.conan2\p\range0301bf3d76d5d\p\include -external:IC:\Users\<USER>\.conan2\p\b\clippf8ddcd4a5d961\p\include -external:IC:\Users\<USER>\.conan2\p\mapbo34ee9aeb574f9\p\include -external:IC:\Users\<USER>\.conan2\p\mapbofe72fb50cf7c4\p\include -external:IC:\Users\<USER>\.conan2\p\mapbo1b62b48748b82\p\include -external:IC:\Users\<USER>\.conan2\p\rapidf7a3355ba53c4\p\include -external:IC:\Users\<USER>\.conan2\p\stb6342cecb318f5\p\include -external:IC:\Users\<USER>\.conan2\p\boosta79d738920bc3\p\include -external:IC:\Users\<USER>\.conan2\p\scrip4cfd014ad4660\p\include -external:IC:\Users\<USER>\.conan2\p\nearg388d58da7a54c\p\include -external:IC:\Users\<USER>\.conan2\p\b\curaebb245e64b5ac7\p\include -external:IC:\Users\<USER>\.conan2\p\asio-c31dc2a380bf0\p\include -external:IC:\Users\<USER>\.conan2\p\grpc8b6c73b4e5756\p\include -external:IC:\Users\<USER>\.conan2\p\absei6a15444d60d47\p\include -external:W0
  OBJECT_DIR = CMakeFiles\_CuraEngine.dir
  OBJECT_FILE_DIR = CMakeFiles\_CuraEngine.dir\src\infill
  TARGET_COMPILE_PDB = CMakeFiles\_CuraEngine.dir\_CuraEngine.pdb
  TARGET_PDB = _CuraEngine.pdb

build CMakeFiles\_CuraEngine.dir\src\infill\SierpinskiFillProvider.cpp.obj: CXX_COMPILER___CuraEngine_unscanned_Release C$:\Users\wsd07\vscode\Cura-Dev\CuraEngine\src\infill\SierpinskiFillProvider.cpp || cmake_object_order_depends_target__CuraEngine
  CONFIG = Release
  DEFINES = -DAGRPC_BOOST_ASIO -DARCUS -DBOOST_ALL_NO_LIB -DBOOST_STACKTRACE_USE_NOOP -DBOOST_STACKTRACE_USE_WINDBG -DBOOST_STACKTRACE_USE_WINDBG_CACHED -DCARES_STATICLIB -DCURA_ENGINE_HASH=\"unknown\" -DCURA_ENGINE_VERSION=\"5.11.0-alpha.0\" -DENABLE_PLUGINS -DNOMINMAX -DSENTRY_ENVIRONMENT=\"development\" -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSTB_TEXTEDIT_KEYTYPE=unsigned
  FLAGS = /DWIN32 /D_WINDOWS /EHsc /O2 /Ob2 /DNDEBUG -std:c++20 -MD /utf-8 /permissive-
  INCLUDES = -IC:\Users\<USER>\vscode\Cura-Dev\CuraEngine\include -IC:\Users\<USER>\vscode\Cura-Dev\CuraEngine\build\Release -external:IC:\Users\<USER>\.conan2\p\b\arcusee7666f716199\p\include -external:IC:\Users\<USER>\.conan2\p\proto47a676cb9257b\p\include -external:IC:\Users\<USER>\.conan2\p\zlibcfb9789dc2a53\p\include -external:IC:\Users\<USER>\.conan2\p\spdlo2d4b540ab0f22\p\include -external:IC:\Users\<USER>\.conan2\p\fmtdb20aad6e1bd4\p\include -external:IC:\Users\<USER>\.conan2\p\range0301bf3d76d5d\p\include -external:IC:\Users\<USER>\.conan2\p\b\clippf8ddcd4a5d961\p\include -external:IC:\Users\<USER>\.conan2\p\mapbo34ee9aeb574f9\p\include -external:IC:\Users\<USER>\.conan2\p\mapbofe72fb50cf7c4\p\include -external:IC:\Users\<USER>\.conan2\p\mapbo1b62b48748b82\p\include -external:IC:\Users\<USER>\.conan2\p\rapidf7a3355ba53c4\p\include -external:IC:\Users\<USER>\.conan2\p\stb6342cecb318f5\p\include -external:IC:\Users\<USER>\.conan2\p\boosta79d738920bc3\p\include -external:IC:\Users\<USER>\.conan2\p\scrip4cfd014ad4660\p\include -external:IC:\Users\<USER>\.conan2\p\nearg388d58da7a54c\p\include -external:IC:\Users\<USER>\.conan2\p\b\curaebb245e64b5ac7\p\include -external:IC:\Users\<USER>\.conan2\p\asio-c31dc2a380bf0\p\include -external:IC:\Users\<USER>\.conan2\p\grpc8b6c73b4e5756\p\include -external:IC:\Users\<USER>\.conan2\p\absei6a15444d60d47\p\include -external:W0
  OBJECT_DIR = CMakeFiles\_CuraEngine.dir
  OBJECT_FILE_DIR = CMakeFiles\_CuraEngine.dir\src\infill
  TARGET_COMPILE_PDB = CMakeFiles\_CuraEngine.dir\_CuraEngine.pdb
  TARGET_PDB = _CuraEngine.pdb

build CMakeFiles\_CuraEngine.dir\src\infill\SubDivCube.cpp.obj: CXX_COMPILER___CuraEngine_unscanned_Release C$:\Users\wsd07\vscode\Cura-Dev\CuraEngine\src\infill\SubDivCube.cpp || cmake_object_order_depends_target__CuraEngine
  CONFIG = Release
  DEFINES = -DAGRPC_BOOST_ASIO -DARCUS -DBOOST_ALL_NO_LIB -DBOOST_STACKTRACE_USE_NOOP -DBOOST_STACKTRACE_USE_WINDBG -DBOOST_STACKTRACE_USE_WINDBG_CACHED -DCARES_STATICLIB -DCURA_ENGINE_HASH=\"unknown\" -DCURA_ENGINE_VERSION=\"5.11.0-alpha.0\" -DENABLE_PLUGINS -DNOMINMAX -DSENTRY_ENVIRONMENT=\"development\" -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSTB_TEXTEDIT_KEYTYPE=unsigned
  FLAGS = /DWIN32 /D_WINDOWS /EHsc /O2 /Ob2 /DNDEBUG -std:c++20 -MD /utf-8 /permissive-
  INCLUDES = -IC:\Users\<USER>\vscode\Cura-Dev\CuraEngine\include -IC:\Users\<USER>\vscode\Cura-Dev\CuraEngine\build\Release -external:IC:\Users\<USER>\.conan2\p\b\arcusee7666f716199\p\include -external:IC:\Users\<USER>\.conan2\p\proto47a676cb9257b\p\include -external:IC:\Users\<USER>\.conan2\p\zlibcfb9789dc2a53\p\include -external:IC:\Users\<USER>\.conan2\p\spdlo2d4b540ab0f22\p\include -external:IC:\Users\<USER>\.conan2\p\fmtdb20aad6e1bd4\p\include -external:IC:\Users\<USER>\.conan2\p\range0301bf3d76d5d\p\include -external:IC:\Users\<USER>\.conan2\p\b\clippf8ddcd4a5d961\p\include -external:IC:\Users\<USER>\.conan2\p\mapbo34ee9aeb574f9\p\include -external:IC:\Users\<USER>\.conan2\p\mapbofe72fb50cf7c4\p\include -external:IC:\Users\<USER>\.conan2\p\mapbo1b62b48748b82\p\include -external:IC:\Users\<USER>\.conan2\p\rapidf7a3355ba53c4\p\include -external:IC:\Users\<USER>\.conan2\p\stb6342cecb318f5\p\include -external:IC:\Users\<USER>\.conan2\p\boosta79d738920bc3\p\include -external:IC:\Users\<USER>\.conan2\p\scrip4cfd014ad4660\p\include -external:IC:\Users\<USER>\.conan2\p\nearg388d58da7a54c\p\include -external:IC:\Users\<USER>\.conan2\p\b\curaebb245e64b5ac7\p\include -external:IC:\Users\<USER>\.conan2\p\asio-c31dc2a380bf0\p\include -external:IC:\Users\<USER>\.conan2\p\grpc8b6c73b4e5756\p\include -external:IC:\Users\<USER>\.conan2\p\absei6a15444d60d47\p\include -external:W0
  OBJECT_DIR = CMakeFiles\_CuraEngine.dir
  OBJECT_FILE_DIR = CMakeFiles\_CuraEngine.dir\src\infill
  TARGET_COMPILE_PDB = CMakeFiles\_CuraEngine.dir\_CuraEngine.pdb
  TARGET_PDB = _CuraEngine.pdb

build CMakeFiles\_CuraEngine.dir\src\infill\GyroidInfill.cpp.obj: CXX_COMPILER___CuraEngine_unscanned_Release C$:\Users\wsd07\vscode\Cura-Dev\CuraEngine\src\infill\GyroidInfill.cpp || cmake_object_order_depends_target__CuraEngine
  CONFIG = Release
  DEFINES = -DAGRPC_BOOST_ASIO -DARCUS -DBOOST_ALL_NO_LIB -DBOOST_STACKTRACE_USE_NOOP -DBOOST_STACKTRACE_USE_WINDBG -DBOOST_STACKTRACE_USE_WINDBG_CACHED -DCARES_STATICLIB -DCURA_ENGINE_HASH=\"unknown\" -DCURA_ENGINE_VERSION=\"5.11.0-alpha.0\" -DENABLE_PLUGINS -DNOMINMAX -DSENTRY_ENVIRONMENT=\"development\" -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSTB_TEXTEDIT_KEYTYPE=unsigned
  FLAGS = /DWIN32 /D_WINDOWS /EHsc /O2 /Ob2 /DNDEBUG -std:c++20 -MD /utf-8 /permissive-
  INCLUDES = -IC:\Users\<USER>\vscode\Cura-Dev\CuraEngine\include -IC:\Users\<USER>\vscode\Cura-Dev\CuraEngine\build\Release -external:IC:\Users\<USER>\.conan2\p\b\arcusee7666f716199\p\include -external:IC:\Users\<USER>\.conan2\p\proto47a676cb9257b\p\include -external:IC:\Users\<USER>\.conan2\p\zlibcfb9789dc2a53\p\include -external:IC:\Users\<USER>\.conan2\p\spdlo2d4b540ab0f22\p\include -external:IC:\Users\<USER>\.conan2\p\fmtdb20aad6e1bd4\p\include -external:IC:\Users\<USER>\.conan2\p\range0301bf3d76d5d\p\include -external:IC:\Users\<USER>\.conan2\p\b\clippf8ddcd4a5d961\p\include -external:IC:\Users\<USER>\.conan2\p\mapbo34ee9aeb574f9\p\include -external:IC:\Users\<USER>\.conan2\p\mapbofe72fb50cf7c4\p\include -external:IC:\Users\<USER>\.conan2\p\mapbo1b62b48748b82\p\include -external:IC:\Users\<USER>\.conan2\p\rapidf7a3355ba53c4\p\include -external:IC:\Users\<USER>\.conan2\p\stb6342cecb318f5\p\include -external:IC:\Users\<USER>\.conan2\p\boosta79d738920bc3\p\include -external:IC:\Users\<USER>\.conan2\p\scrip4cfd014ad4660\p\include -external:IC:\Users\<USER>\.conan2\p\nearg388d58da7a54c\p\include -external:IC:\Users\<USER>\.conan2\p\b\curaebb245e64b5ac7\p\include -external:IC:\Users\<USER>\.conan2\p\asio-c31dc2a380bf0\p\include -external:IC:\Users\<USER>\.conan2\p\grpc8b6c73b4e5756\p\include -external:IC:\Users\<USER>\.conan2\p\absei6a15444d60d47\p\include -external:W0
  OBJECT_DIR = CMakeFiles\_CuraEngine.dir
  OBJECT_FILE_DIR = CMakeFiles\_CuraEngine.dir\src\infill
  TARGET_COMPILE_PDB = CMakeFiles\_CuraEngine.dir\_CuraEngine.pdb
  TARGET_PDB = _CuraEngine.pdb

build CMakeFiles\_CuraEngine.dir\src\pathPlanning\Comb.cpp.obj: CXX_COMPILER___CuraEngine_unscanned_Release C$:\Users\wsd07\vscode\Cura-Dev\CuraEngine\src\pathPlanning\Comb.cpp || cmake_object_order_depends_target__CuraEngine
  CONFIG = Release
  DEFINES = -DAGRPC_BOOST_ASIO -DARCUS -DBOOST_ALL_NO_LIB -DBOOST_STACKTRACE_USE_NOOP -DBOOST_STACKTRACE_USE_WINDBG -DBOOST_STACKTRACE_USE_WINDBG_CACHED -DCARES_STATICLIB -DCURA_ENGINE_HASH=\"unknown\" -DCURA_ENGINE_VERSION=\"5.11.0-alpha.0\" -DENABLE_PLUGINS -DNOMINMAX -DSENTRY_ENVIRONMENT=\"development\" -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSTB_TEXTEDIT_KEYTYPE=unsigned
  FLAGS = /DWIN32 /D_WINDOWS /EHsc /O2 /Ob2 /DNDEBUG -std:c++20 -MD /utf-8 /permissive-
  INCLUDES = -IC:\Users\<USER>\vscode\Cura-Dev\CuraEngine\include -IC:\Users\<USER>\vscode\Cura-Dev\CuraEngine\build\Release -external:IC:\Users\<USER>\.conan2\p\b\arcusee7666f716199\p\include -external:IC:\Users\<USER>\.conan2\p\proto47a676cb9257b\p\include -external:IC:\Users\<USER>\.conan2\p\zlibcfb9789dc2a53\p\include -external:IC:\Users\<USER>\.conan2\p\spdlo2d4b540ab0f22\p\include -external:IC:\Users\<USER>\.conan2\p\fmtdb20aad6e1bd4\p\include -external:IC:\Users\<USER>\.conan2\p\range0301bf3d76d5d\p\include -external:IC:\Users\<USER>\.conan2\p\b\clippf8ddcd4a5d961\p\include -external:IC:\Users\<USER>\.conan2\p\mapbo34ee9aeb574f9\p\include -external:IC:\Users\<USER>\.conan2\p\mapbofe72fb50cf7c4\p\include -external:IC:\Users\<USER>\.conan2\p\mapbo1b62b48748b82\p\include -external:IC:\Users\<USER>\.conan2\p\rapidf7a3355ba53c4\p\include -external:IC:\Users\<USER>\.conan2\p\stb6342cecb318f5\p\include -external:IC:\Users\<USER>\.conan2\p\boosta79d738920bc3\p\include -external:IC:\Users\<USER>\.conan2\p\scrip4cfd014ad4660\p\include -external:IC:\Users\<USER>\.conan2\p\nearg388d58da7a54c\p\include -external:IC:\Users\<USER>\.conan2\p\b\curaebb245e64b5ac7\p\include -external:IC:\Users\<USER>\.conan2\p\asio-c31dc2a380bf0\p\include -external:IC:\Users\<USER>\.conan2\p\grpc8b6c73b4e5756\p\include -external:IC:\Users\<USER>\.conan2\p\absei6a15444d60d47\p\include -external:W0
  OBJECT_DIR = CMakeFiles\_CuraEngine.dir
  OBJECT_FILE_DIR = CMakeFiles\_CuraEngine.dir\src\pathPlanning
  TARGET_COMPILE_PDB = CMakeFiles\_CuraEngine.dir\_CuraEngine.pdb
  TARGET_PDB = _CuraEngine.pdb

build CMakeFiles\_CuraEngine.dir\src\pathPlanning\GCodePath.cpp.obj: CXX_COMPILER___CuraEngine_unscanned_Release C$:\Users\wsd07\vscode\Cura-Dev\CuraEngine\src\pathPlanning\GCodePath.cpp || cmake_object_order_depends_target__CuraEngine
  CONFIG = Release
  DEFINES = -DAGRPC_BOOST_ASIO -DARCUS -DBOOST_ALL_NO_LIB -DBOOST_STACKTRACE_USE_NOOP -DBOOST_STACKTRACE_USE_WINDBG -DBOOST_STACKTRACE_USE_WINDBG_CACHED -DCARES_STATICLIB -DCURA_ENGINE_HASH=\"unknown\" -DCURA_ENGINE_VERSION=\"5.11.0-alpha.0\" -DENABLE_PLUGINS -DNOMINMAX -DSENTRY_ENVIRONMENT=\"development\" -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSTB_TEXTEDIT_KEYTYPE=unsigned
  FLAGS = /DWIN32 /D_WINDOWS /EHsc /O2 /Ob2 /DNDEBUG -std:c++20 -MD /utf-8 /permissive-
  INCLUDES = -IC:\Users\<USER>\vscode\Cura-Dev\CuraEngine\include -IC:\Users\<USER>\vscode\Cura-Dev\CuraEngine\build\Release -external:IC:\Users\<USER>\.conan2\p\b\arcusee7666f716199\p\include -external:IC:\Users\<USER>\.conan2\p\proto47a676cb9257b\p\include -external:IC:\Users\<USER>\.conan2\p\zlibcfb9789dc2a53\p\include -external:IC:\Users\<USER>\.conan2\p\spdlo2d4b540ab0f22\p\include -external:IC:\Users\<USER>\.conan2\p\fmtdb20aad6e1bd4\p\include -external:IC:\Users\<USER>\.conan2\p\range0301bf3d76d5d\p\include -external:IC:\Users\<USER>\.conan2\p\b\clippf8ddcd4a5d961\p\include -external:IC:\Users\<USER>\.conan2\p\mapbo34ee9aeb574f9\p\include -external:IC:\Users\<USER>\.conan2\p\mapbofe72fb50cf7c4\p\include -external:IC:\Users\<USER>\.conan2\p\mapbo1b62b48748b82\p\include -external:IC:\Users\<USER>\.conan2\p\rapidf7a3355ba53c4\p\include -external:IC:\Users\<USER>\.conan2\p\stb6342cecb318f5\p\include -external:IC:\Users\<USER>\.conan2\p\boosta79d738920bc3\p\include -external:IC:\Users\<USER>\.conan2\p\scrip4cfd014ad4660\p\include -external:IC:\Users\<USER>\.conan2\p\nearg388d58da7a54c\p\include -external:IC:\Users\<USER>\.conan2\p\b\curaebb245e64b5ac7\p\include -external:IC:\Users\<USER>\.conan2\p\asio-c31dc2a380bf0\p\include -external:IC:\Users\<USER>\.conan2\p\grpc8b6c73b4e5756\p\include -external:IC:\Users\<USER>\.conan2\p\absei6a15444d60d47\p\include -external:W0
  OBJECT_DIR = CMakeFiles\_CuraEngine.dir
  OBJECT_FILE_DIR = CMakeFiles\_CuraEngine.dir\src\pathPlanning
  TARGET_COMPILE_PDB = CMakeFiles\_CuraEngine.dir\_CuraEngine.pdb
  TARGET_PDB = _CuraEngine.pdb

build CMakeFiles\_CuraEngine.dir\src\pathPlanning\LinePolygonsCrossings.cpp.obj: CXX_COMPILER___CuraEngine_unscanned_Release C$:\Users\wsd07\vscode\Cura-Dev\CuraEngine\src\pathPlanning\LinePolygonsCrossings.cpp || cmake_object_order_depends_target__CuraEngine
  CONFIG = Release
  DEFINES = -DAGRPC_BOOST_ASIO -DARCUS -DBOOST_ALL_NO_LIB -DBOOST_STACKTRACE_USE_NOOP -DBOOST_STACKTRACE_USE_WINDBG -DBOOST_STACKTRACE_USE_WINDBG_CACHED -DCARES_STATICLIB -DCURA_ENGINE_HASH=\"unknown\" -DCURA_ENGINE_VERSION=\"5.11.0-alpha.0\" -DENABLE_PLUGINS -DNOMINMAX -DSENTRY_ENVIRONMENT=\"development\" -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSTB_TEXTEDIT_KEYTYPE=unsigned
  FLAGS = /DWIN32 /D_WINDOWS /EHsc /O2 /Ob2 /DNDEBUG -std:c++20 -MD /utf-8 /permissive-
  INCLUDES = -IC:\Users\<USER>\vscode\Cura-Dev\CuraEngine\include -IC:\Users\<USER>\vscode\Cura-Dev\CuraEngine\build\Release -external:IC:\Users\<USER>\.conan2\p\b\arcusee7666f716199\p\include -external:IC:\Users\<USER>\.conan2\p\proto47a676cb9257b\p\include -external:IC:\Users\<USER>\.conan2\p\zlibcfb9789dc2a53\p\include -external:IC:\Users\<USER>\.conan2\p\spdlo2d4b540ab0f22\p\include -external:IC:\Users\<USER>\.conan2\p\fmtdb20aad6e1bd4\p\include -external:IC:\Users\<USER>\.conan2\p\range0301bf3d76d5d\p\include -external:IC:\Users\<USER>\.conan2\p\b\clippf8ddcd4a5d961\p\include -external:IC:\Users\<USER>\.conan2\p\mapbo34ee9aeb574f9\p\include -external:IC:\Users\<USER>\.conan2\p\mapbofe72fb50cf7c4\p\include -external:IC:\Users\<USER>\.conan2\p\mapbo1b62b48748b82\p\include -external:IC:\Users\<USER>\.conan2\p\rapidf7a3355ba53c4\p\include -external:IC:\Users\<USER>\.conan2\p\stb6342cecb318f5\p\include -external:IC:\Users\<USER>\.conan2\p\boosta79d738920bc3\p\include -external:IC:\Users\<USER>\.conan2\p\scrip4cfd014ad4660\p\include -external:IC:\Users\<USER>\.conan2\p\nearg388d58da7a54c\p\include -external:IC:\Users\<USER>\.conan2\p\b\curaebb245e64b5ac7\p\include -external:IC:\Users\<USER>\.conan2\p\asio-c31dc2a380bf0\p\include -external:IC:\Users\<USER>\.conan2\p\grpc8b6c73b4e5756\p\include -external:IC:\Users\<USER>\.conan2\p\absei6a15444d60d47\p\include -external:W0
  OBJECT_DIR = CMakeFiles\_CuraEngine.dir
  OBJECT_FILE_DIR = CMakeFiles\_CuraEngine.dir\src\pathPlanning
  TARGET_COMPILE_PDB = CMakeFiles\_CuraEngine.dir\_CuraEngine.pdb
  TARGET_PDB = _CuraEngine.pdb

build CMakeFiles\_CuraEngine.dir\src\pathPlanning\NozzleTempInsert.cpp.obj: CXX_COMPILER___CuraEngine_unscanned_Release C$:\Users\wsd07\vscode\Cura-Dev\CuraEngine\src\pathPlanning\NozzleTempInsert.cpp || cmake_object_order_depends_target__CuraEngine
  CONFIG = Release
  DEFINES = -DAGRPC_BOOST_ASIO -DARCUS -DBOOST_ALL_NO_LIB -DBOOST_STACKTRACE_USE_NOOP -DBOOST_STACKTRACE_USE_WINDBG -DBOOST_STACKTRACE_USE_WINDBG_CACHED -DCARES_STATICLIB -DCURA_ENGINE_HASH=\"unknown\" -DCURA_ENGINE_VERSION=\"5.11.0-alpha.0\" -DENABLE_PLUGINS -DNOMINMAX -DSENTRY_ENVIRONMENT=\"development\" -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSTB_TEXTEDIT_KEYTYPE=unsigned
  FLAGS = /DWIN32 /D_WINDOWS /EHsc /O2 /Ob2 /DNDEBUG -std:c++20 -MD /utf-8 /permissive-
  INCLUDES = -IC:\Users\<USER>\vscode\Cura-Dev\CuraEngine\include -IC:\Users\<USER>\vscode\Cura-Dev\CuraEngine\build\Release -external:IC:\Users\<USER>\.conan2\p\b\arcusee7666f716199\p\include -external:IC:\Users\<USER>\.conan2\p\proto47a676cb9257b\p\include -external:IC:\Users\<USER>\.conan2\p\zlibcfb9789dc2a53\p\include -external:IC:\Users\<USER>\.conan2\p\spdlo2d4b540ab0f22\p\include -external:IC:\Users\<USER>\.conan2\p\fmtdb20aad6e1bd4\p\include -external:IC:\Users\<USER>\.conan2\p\range0301bf3d76d5d\p\include -external:IC:\Users\<USER>\.conan2\p\b\clippf8ddcd4a5d961\p\include -external:IC:\Users\<USER>\.conan2\p\mapbo34ee9aeb574f9\p\include -external:IC:\Users\<USER>\.conan2\p\mapbofe72fb50cf7c4\p\include -external:IC:\Users\<USER>\.conan2\p\mapbo1b62b48748b82\p\include -external:IC:\Users\<USER>\.conan2\p\rapidf7a3355ba53c4\p\include -external:IC:\Users\<USER>\.conan2\p\stb6342cecb318f5\p\include -external:IC:\Users\<USER>\.conan2\p\boosta79d738920bc3\p\include -external:IC:\Users\<USER>\.conan2\p\scrip4cfd014ad4660\p\include -external:IC:\Users\<USER>\.conan2\p\nearg388d58da7a54c\p\include -external:IC:\Users\<USER>\.conan2\p\b\curaebb245e64b5ac7\p\include -external:IC:\Users\<USER>\.conan2\p\asio-c31dc2a380bf0\p\include -external:IC:\Users\<USER>\.conan2\p\grpc8b6c73b4e5756\p\include -external:IC:\Users\<USER>\.conan2\p\absei6a15444d60d47\p\include -external:W0
  OBJECT_DIR = CMakeFiles\_CuraEngine.dir
  OBJECT_FILE_DIR = CMakeFiles\_CuraEngine.dir\src\pathPlanning
  TARGET_COMPILE_PDB = CMakeFiles\_CuraEngine.dir\_CuraEngine.pdb
  TARGET_PDB = _CuraEngine.pdb

build CMakeFiles\_CuraEngine.dir\src\pathPlanning\SpeedDerivatives.cpp.obj: CXX_COMPILER___CuraEngine_unscanned_Release C$:\Users\wsd07\vscode\Cura-Dev\CuraEngine\src\pathPlanning\SpeedDerivatives.cpp || cmake_object_order_depends_target__CuraEngine
  CONFIG = Release
  DEFINES = -DAGRPC_BOOST_ASIO -DARCUS -DBOOST_ALL_NO_LIB -DBOOST_STACKTRACE_USE_NOOP -DBOOST_STACKTRACE_USE_WINDBG -DBOOST_STACKTRACE_USE_WINDBG_CACHED -DCARES_STATICLIB -DCURA_ENGINE_HASH=\"unknown\" -DCURA_ENGINE_VERSION=\"5.11.0-alpha.0\" -DENABLE_PLUGINS -DNOMINMAX -DSENTRY_ENVIRONMENT=\"development\" -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSTB_TEXTEDIT_KEYTYPE=unsigned
  FLAGS = /DWIN32 /D_WINDOWS /EHsc /O2 /Ob2 /DNDEBUG -std:c++20 -MD /utf-8 /permissive-
  INCLUDES = -IC:\Users\<USER>\vscode\Cura-Dev\CuraEngine\include -IC:\Users\<USER>\vscode\Cura-Dev\CuraEngine\build\Release -external:IC:\Users\<USER>\.conan2\p\b\arcusee7666f716199\p\include -external:IC:\Users\<USER>\.conan2\p\proto47a676cb9257b\p\include -external:IC:\Users\<USER>\.conan2\p\zlibcfb9789dc2a53\p\include -external:IC:\Users\<USER>\.conan2\p\spdlo2d4b540ab0f22\p\include -external:IC:\Users\<USER>\.conan2\p\fmtdb20aad6e1bd4\p\include -external:IC:\Users\<USER>\.conan2\p\range0301bf3d76d5d\p\include -external:IC:\Users\<USER>\.conan2\p\b\clippf8ddcd4a5d961\p\include -external:IC:\Users\<USER>\.conan2\p\mapbo34ee9aeb574f9\p\include -external:IC:\Users\<USER>\.conan2\p\mapbofe72fb50cf7c4\p\include -external:IC:\Users\<USER>\.conan2\p\mapbo1b62b48748b82\p\include -external:IC:\Users\<USER>\.conan2\p\rapidf7a3355ba53c4\p\include -external:IC:\Users\<USER>\.conan2\p\stb6342cecb318f5\p\include -external:IC:\Users\<USER>\.conan2\p\boosta79d738920bc3\p\include -external:IC:\Users\<USER>\.conan2\p\scrip4cfd014ad4660\p\include -external:IC:\Users\<USER>\.conan2\p\nearg388d58da7a54c\p\include -external:IC:\Users\<USER>\.conan2\p\b\curaebb245e64b5ac7\p\include -external:IC:\Users\<USER>\.conan2\p\asio-c31dc2a380bf0\p\include -external:IC:\Users\<USER>\.conan2\p\grpc8b6c73b4e5756\p\include -external:IC:\Users\<USER>\.conan2\p\absei6a15444d60d47\p\include -external:W0
  OBJECT_DIR = CMakeFiles\_CuraEngine.dir
  OBJECT_FILE_DIR = CMakeFiles\_CuraEngine.dir\src\pathPlanning
  TARGET_COMPILE_PDB = CMakeFiles\_CuraEngine.dir\_CuraEngine.pdb
  TARGET_PDB = _CuraEngine.pdb

build CMakeFiles\_CuraEngine.dir\src\plugins\converters.cpp.obj: CXX_COMPILER___CuraEngine_unscanned_Release C$:\Users\wsd07\vscode\Cura-Dev\CuraEngine\src\plugins\converters.cpp || cmake_object_order_depends_target__CuraEngine
  CONFIG = Release
  DEFINES = -DAGRPC_BOOST_ASIO -DARCUS -DBOOST_ALL_NO_LIB -DBOOST_STACKTRACE_USE_NOOP -DBOOST_STACKTRACE_USE_WINDBG -DBOOST_STACKTRACE_USE_WINDBG_CACHED -DCARES_STATICLIB -DCURA_ENGINE_HASH=\"unknown\" -DCURA_ENGINE_VERSION=\"5.11.0-alpha.0\" -DENABLE_PLUGINS -DNOMINMAX -DSENTRY_ENVIRONMENT=\"development\" -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSTB_TEXTEDIT_KEYTYPE=unsigned
  FLAGS = /DWIN32 /D_WINDOWS /EHsc /O2 /Ob2 /DNDEBUG -std:c++20 -MD /utf-8 /permissive-
  INCLUDES = -IC:\Users\<USER>\vscode\Cura-Dev\CuraEngine\include -IC:\Users\<USER>\vscode\Cura-Dev\CuraEngine\build\Release -external:IC:\Users\<USER>\.conan2\p\b\arcusee7666f716199\p\include -external:IC:\Users\<USER>\.conan2\p\proto47a676cb9257b\p\include -external:IC:\Users\<USER>\.conan2\p\zlibcfb9789dc2a53\p\include -external:IC:\Users\<USER>\.conan2\p\spdlo2d4b540ab0f22\p\include -external:IC:\Users\<USER>\.conan2\p\fmtdb20aad6e1bd4\p\include -external:IC:\Users\<USER>\.conan2\p\range0301bf3d76d5d\p\include -external:IC:\Users\<USER>\.conan2\p\b\clippf8ddcd4a5d961\p\include -external:IC:\Users\<USER>\.conan2\p\mapbo34ee9aeb574f9\p\include -external:IC:\Users\<USER>\.conan2\p\mapbofe72fb50cf7c4\p\include -external:IC:\Users\<USER>\.conan2\p\mapbo1b62b48748b82\p\include -external:IC:\Users\<USER>\.conan2\p\rapidf7a3355ba53c4\p\include -external:IC:\Users\<USER>\.conan2\p\stb6342cecb318f5\p\include -external:IC:\Users\<USER>\.conan2\p\boosta79d738920bc3\p\include -external:IC:\Users\<USER>\.conan2\p\scrip4cfd014ad4660\p\include -external:IC:\Users\<USER>\.conan2\p\nearg388d58da7a54c\p\include -external:IC:\Users\<USER>\.conan2\p\b\curaebb245e64b5ac7\p\include -external:IC:\Users\<USER>\.conan2\p\asio-c31dc2a380bf0\p\include -external:IC:\Users\<USER>\.conan2\p\grpc8b6c73b4e5756\p\include -external:IC:\Users\<USER>\.conan2\p\absei6a15444d60d47\p\include -external:W0
  OBJECT_DIR = CMakeFiles\_CuraEngine.dir
  OBJECT_FILE_DIR = CMakeFiles\_CuraEngine.dir\src\plugins
  TARGET_COMPILE_PDB = CMakeFiles\_CuraEngine.dir\_CuraEngine.pdb
  TARGET_PDB = _CuraEngine.pdb

build CMakeFiles\_CuraEngine.dir\src\progress\Progress.cpp.obj: CXX_COMPILER___CuraEngine_unscanned_Release C$:\Users\wsd07\vscode\Cura-Dev\CuraEngine\src\progress\Progress.cpp || cmake_object_order_depends_target__CuraEngine
  CONFIG = Release
  DEFINES = -DAGRPC_BOOST_ASIO -DARCUS -DBOOST_ALL_NO_LIB -DBOOST_STACKTRACE_USE_NOOP -DBOOST_STACKTRACE_USE_WINDBG -DBOOST_STACKTRACE_USE_WINDBG_CACHED -DCARES_STATICLIB -DCURA_ENGINE_HASH=\"unknown\" -DCURA_ENGINE_VERSION=\"5.11.0-alpha.0\" -DENABLE_PLUGINS -DNOMINMAX -DSENTRY_ENVIRONMENT=\"development\" -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSTB_TEXTEDIT_KEYTYPE=unsigned
  FLAGS = /DWIN32 /D_WINDOWS /EHsc /O2 /Ob2 /DNDEBUG -std:c++20 -MD /utf-8 /permissive-
  INCLUDES = -IC:\Users\<USER>\vscode\Cura-Dev\CuraEngine\include -IC:\Users\<USER>\vscode\Cura-Dev\CuraEngine\build\Release -external:IC:\Users\<USER>\.conan2\p\b\arcusee7666f716199\p\include -external:IC:\Users\<USER>\.conan2\p\proto47a676cb9257b\p\include -external:IC:\Users\<USER>\.conan2\p\zlibcfb9789dc2a53\p\include -external:IC:\Users\<USER>\.conan2\p\spdlo2d4b540ab0f22\p\include -external:IC:\Users\<USER>\.conan2\p\fmtdb20aad6e1bd4\p\include -external:IC:\Users\<USER>\.conan2\p\range0301bf3d76d5d\p\include -external:IC:\Users\<USER>\.conan2\p\b\clippf8ddcd4a5d961\p\include -external:IC:\Users\<USER>\.conan2\p\mapbo34ee9aeb574f9\p\include -external:IC:\Users\<USER>\.conan2\p\mapbofe72fb50cf7c4\p\include -external:IC:\Users\<USER>\.conan2\p\mapbo1b62b48748b82\p\include -external:IC:\Users\<USER>\.conan2\p\rapidf7a3355ba53c4\p\include -external:IC:\Users\<USER>\.conan2\p\stb6342cecb318f5\p\include -external:IC:\Users\<USER>\.conan2\p\boosta79d738920bc3\p\include -external:IC:\Users\<USER>\.conan2\p\scrip4cfd014ad4660\p\include -external:IC:\Users\<USER>\.conan2\p\nearg388d58da7a54c\p\include -external:IC:\Users\<USER>\.conan2\p\b\curaebb245e64b5ac7\p\include -external:IC:\Users\<USER>\.conan2\p\asio-c31dc2a380bf0\p\include -external:IC:\Users\<USER>\.conan2\p\grpc8b6c73b4e5756\p\include -external:IC:\Users\<USER>\.conan2\p\absei6a15444d60d47\p\include -external:W0
  OBJECT_DIR = CMakeFiles\_CuraEngine.dir
  OBJECT_FILE_DIR = CMakeFiles\_CuraEngine.dir\src\progress
  TARGET_COMPILE_PDB = CMakeFiles\_CuraEngine.dir\_CuraEngine.pdb
  TARGET_PDB = _CuraEngine.pdb

build CMakeFiles\_CuraEngine.dir\src\progress\ProgressStageEstimator.cpp.obj: CXX_COMPILER___CuraEngine_unscanned_Release C$:\Users\wsd07\vscode\Cura-Dev\CuraEngine\src\progress\ProgressStageEstimator.cpp || cmake_object_order_depends_target__CuraEngine
  CONFIG = Release
  DEFINES = -DAGRPC_BOOST_ASIO -DARCUS -DBOOST_ALL_NO_LIB -DBOOST_STACKTRACE_USE_NOOP -DBOOST_STACKTRACE_USE_WINDBG -DBOOST_STACKTRACE_USE_WINDBG_CACHED -DCARES_STATICLIB -DCURA_ENGINE_HASH=\"unknown\" -DCURA_ENGINE_VERSION=\"5.11.0-alpha.0\" -DENABLE_PLUGINS -DNOMINMAX -DSENTRY_ENVIRONMENT=\"development\" -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSTB_TEXTEDIT_KEYTYPE=unsigned
  FLAGS = /DWIN32 /D_WINDOWS /EHsc /O2 /Ob2 /DNDEBUG -std:c++20 -MD /utf-8 /permissive-
  INCLUDES = -IC:\Users\<USER>\vscode\Cura-Dev\CuraEngine\include -IC:\Users\<USER>\vscode\Cura-Dev\CuraEngine\build\Release -external:IC:\Users\<USER>\.conan2\p\b\arcusee7666f716199\p\include -external:IC:\Users\<USER>\.conan2\p\proto47a676cb9257b\p\include -external:IC:\Users\<USER>\.conan2\p\zlibcfb9789dc2a53\p\include -external:IC:\Users\<USER>\.conan2\p\spdlo2d4b540ab0f22\p\include -external:IC:\Users\<USER>\.conan2\p\fmtdb20aad6e1bd4\p\include -external:IC:\Users\<USER>\.conan2\p\range0301bf3d76d5d\p\include -external:IC:\Users\<USER>\.conan2\p\b\clippf8ddcd4a5d961\p\include -external:IC:\Users\<USER>\.conan2\p\mapbo34ee9aeb574f9\p\include -external:IC:\Users\<USER>\.conan2\p\mapbofe72fb50cf7c4\p\include -external:IC:\Users\<USER>\.conan2\p\mapbo1b62b48748b82\p\include -external:IC:\Users\<USER>\.conan2\p\rapidf7a3355ba53c4\p\include -external:IC:\Users\<USER>\.conan2\p\stb6342cecb318f5\p\include -external:IC:\Users\<USER>\.conan2\p\boosta79d738920bc3\p\include -external:IC:\Users\<USER>\.conan2\p\scrip4cfd014ad4660\p\include -external:IC:\Users\<USER>\.conan2\p\nearg388d58da7a54c\p\include -external:IC:\Users\<USER>\.conan2\p\b\curaebb245e64b5ac7\p\include -external:IC:\Users\<USER>\.conan2\p\asio-c31dc2a380bf0\p\include -external:IC:\Users\<USER>\.conan2\p\grpc8b6c73b4e5756\p\include -external:IC:\Users\<USER>\.conan2\p\absei6a15444d60d47\p\include -external:W0
  OBJECT_DIR = CMakeFiles\_CuraEngine.dir
  OBJECT_FILE_DIR = CMakeFiles\_CuraEngine.dir\src\progress
  TARGET_COMPILE_PDB = CMakeFiles\_CuraEngine.dir\_CuraEngine.pdb
  TARGET_PDB = _CuraEngine.pdb

build CMakeFiles\_CuraEngine.dir\src\settings\AdaptiveLayerHeights.cpp.obj: CXX_COMPILER___CuraEngine_unscanned_Release C$:\Users\wsd07\vscode\Cura-Dev\CuraEngine\src\settings\AdaptiveLayerHeights.cpp || cmake_object_order_depends_target__CuraEngine
  CONFIG = Release
  DEFINES = -DAGRPC_BOOST_ASIO -DARCUS -DBOOST_ALL_NO_LIB -DBOOST_STACKTRACE_USE_NOOP -DBOOST_STACKTRACE_USE_WINDBG -DBOOST_STACKTRACE_USE_WINDBG_CACHED -DCARES_STATICLIB -DCURA_ENGINE_HASH=\"unknown\" -DCURA_ENGINE_VERSION=\"5.11.0-alpha.0\" -DENABLE_PLUGINS -DNOMINMAX -DSENTRY_ENVIRONMENT=\"development\" -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSTB_TEXTEDIT_KEYTYPE=unsigned
  FLAGS = /DWIN32 /D_WINDOWS /EHsc /O2 /Ob2 /DNDEBUG -std:c++20 -MD /utf-8 /permissive-
  INCLUDES = -IC:\Users\<USER>\vscode\Cura-Dev\CuraEngine\include -IC:\Users\<USER>\vscode\Cura-Dev\CuraEngine\build\Release -external:IC:\Users\<USER>\.conan2\p\b\arcusee7666f716199\p\include -external:IC:\Users\<USER>\.conan2\p\proto47a676cb9257b\p\include -external:IC:\Users\<USER>\.conan2\p\zlibcfb9789dc2a53\p\include -external:IC:\Users\<USER>\.conan2\p\spdlo2d4b540ab0f22\p\include -external:IC:\Users\<USER>\.conan2\p\fmtdb20aad6e1bd4\p\include -external:IC:\Users\<USER>\.conan2\p\range0301bf3d76d5d\p\include -external:IC:\Users\<USER>\.conan2\p\b\clippf8ddcd4a5d961\p\include -external:IC:\Users\<USER>\.conan2\p\mapbo34ee9aeb574f9\p\include -external:IC:\Users\<USER>\.conan2\p\mapbofe72fb50cf7c4\p\include -external:IC:\Users\<USER>\.conan2\p\mapbo1b62b48748b82\p\include -external:IC:\Users\<USER>\.conan2\p\rapidf7a3355ba53c4\p\include -external:IC:\Users\<USER>\.conan2\p\stb6342cecb318f5\p\include -external:IC:\Users\<USER>\.conan2\p\boosta79d738920bc3\p\include -external:IC:\Users\<USER>\.conan2\p\scrip4cfd014ad4660\p\include -external:IC:\Users\<USER>\.conan2\p\nearg388d58da7a54c\p\include -external:IC:\Users\<USER>\.conan2\p\b\curaebb245e64b5ac7\p\include -external:IC:\Users\<USER>\.conan2\p\asio-c31dc2a380bf0\p\include -external:IC:\Users\<USER>\.conan2\p\grpc8b6c73b4e5756\p\include -external:IC:\Users\<USER>\.conan2\p\absei6a15444d60d47\p\include -external:W0
  OBJECT_DIR = CMakeFiles\_CuraEngine.dir
  OBJECT_FILE_DIR = CMakeFiles\_CuraEngine.dir\src\settings
  TARGET_COMPILE_PDB = CMakeFiles\_CuraEngine.dir\_CuraEngine.pdb
  TARGET_PDB = _CuraEngine.pdb

build CMakeFiles\_CuraEngine.dir\src\settings\FlowTempGraph.cpp.obj: CXX_COMPILER___CuraEngine_unscanned_Release C$:\Users\wsd07\vscode\Cura-Dev\CuraEngine\src\settings\FlowTempGraph.cpp || cmake_object_order_depends_target__CuraEngine
  CONFIG = Release
  DEFINES = -DAGRPC_BOOST_ASIO -DARCUS -DBOOST_ALL_NO_LIB -DBOOST_STACKTRACE_USE_NOOP -DBOOST_STACKTRACE_USE_WINDBG -DBOOST_STACKTRACE_USE_WINDBG_CACHED -DCARES_STATICLIB -DCURA_ENGINE_HASH=\"unknown\" -DCURA_ENGINE_VERSION=\"5.11.0-alpha.0\" -DENABLE_PLUGINS -DNOMINMAX -DSENTRY_ENVIRONMENT=\"development\" -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSTB_TEXTEDIT_KEYTYPE=unsigned
  FLAGS = /DWIN32 /D_WINDOWS /EHsc /O2 /Ob2 /DNDEBUG -std:c++20 -MD /utf-8 /permissive-
  INCLUDES = -IC:\Users\<USER>\vscode\Cura-Dev\CuraEngine\include -IC:\Users\<USER>\vscode\Cura-Dev\CuraEngine\build\Release -external:IC:\Users\<USER>\.conan2\p\b\arcusee7666f716199\p\include -external:IC:\Users\<USER>\.conan2\p\proto47a676cb9257b\p\include -external:IC:\Users\<USER>\.conan2\p\zlibcfb9789dc2a53\p\include -external:IC:\Users\<USER>\.conan2\p\spdlo2d4b540ab0f22\p\include -external:IC:\Users\<USER>\.conan2\p\fmtdb20aad6e1bd4\p\include -external:IC:\Users\<USER>\.conan2\p\range0301bf3d76d5d\p\include -external:IC:\Users\<USER>\.conan2\p\b\clippf8ddcd4a5d961\p\include -external:IC:\Users\<USER>\.conan2\p\mapbo34ee9aeb574f9\p\include -external:IC:\Users\<USER>\.conan2\p\mapbofe72fb50cf7c4\p\include -external:IC:\Users\<USER>\.conan2\p\mapbo1b62b48748b82\p\include -external:IC:\Users\<USER>\.conan2\p\rapidf7a3355ba53c4\p\include -external:IC:\Users\<USER>\.conan2\p\stb6342cecb318f5\p\include -external:IC:\Users\<USER>\.conan2\p\boosta79d738920bc3\p\include -external:IC:\Users\<USER>\.conan2\p\scrip4cfd014ad4660\p\include -external:IC:\Users\<USER>\.conan2\p\nearg388d58da7a54c\p\include -external:IC:\Users\<USER>\.conan2\p\b\curaebb245e64b5ac7\p\include -external:IC:\Users\<USER>\.conan2\p\asio-c31dc2a380bf0\p\include -external:IC:\Users\<USER>\.conan2\p\grpc8b6c73b4e5756\p\include -external:IC:\Users\<USER>\.conan2\p\absei6a15444d60d47\p\include -external:W0
  OBJECT_DIR = CMakeFiles\_CuraEngine.dir
  OBJECT_FILE_DIR = CMakeFiles\_CuraEngine.dir\src\settings
  TARGET_COMPILE_PDB = CMakeFiles\_CuraEngine.dir\_CuraEngine.pdb
  TARGET_PDB = _CuraEngine.pdb

build CMakeFiles\_CuraEngine.dir\src\settings\MeshPathConfigs.cpp.obj: CXX_COMPILER___CuraEngine_unscanned_Release C$:\Users\wsd07\vscode\Cura-Dev\CuraEngine\src\settings\MeshPathConfigs.cpp || cmake_object_order_depends_target__CuraEngine
  CONFIG = Release
  DEFINES = -DAGRPC_BOOST_ASIO -DARCUS -DBOOST_ALL_NO_LIB -DBOOST_STACKTRACE_USE_NOOP -DBOOST_STACKTRACE_USE_WINDBG -DBOOST_STACKTRACE_USE_WINDBG_CACHED -DCARES_STATICLIB -DCURA_ENGINE_HASH=\"unknown\" -DCURA_ENGINE_VERSION=\"5.11.0-alpha.0\" -DENABLE_PLUGINS -DNOMINMAX -DSENTRY_ENVIRONMENT=\"development\" -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSTB_TEXTEDIT_KEYTYPE=unsigned
  FLAGS = /DWIN32 /D_WINDOWS /EHsc /O2 /Ob2 /DNDEBUG -std:c++20 -MD /utf-8 /permissive-
  INCLUDES = -IC:\Users\<USER>\vscode\Cura-Dev\CuraEngine\include -IC:\Users\<USER>\vscode\Cura-Dev\CuraEngine\build\Release -external:IC:\Users\<USER>\.conan2\p\b\arcusee7666f716199\p\include -external:IC:\Users\<USER>\.conan2\p\proto47a676cb9257b\p\include -external:IC:\Users\<USER>\.conan2\p\zlibcfb9789dc2a53\p\include -external:IC:\Users\<USER>\.conan2\p\spdlo2d4b540ab0f22\p\include -external:IC:\Users\<USER>\.conan2\p\fmtdb20aad6e1bd4\p\include -external:IC:\Users\<USER>\.conan2\p\range0301bf3d76d5d\p\include -external:IC:\Users\<USER>\.conan2\p\b\clippf8ddcd4a5d961\p\include -external:IC:\Users\<USER>\.conan2\p\mapbo34ee9aeb574f9\p\include -external:IC:\Users\<USER>\.conan2\p\mapbofe72fb50cf7c4\p\include -external:IC:\Users\<USER>\.conan2\p\mapbo1b62b48748b82\p\include -external:IC:\Users\<USER>\.conan2\p\rapidf7a3355ba53c4\p\include -external:IC:\Users\<USER>\.conan2\p\stb6342cecb318f5\p\include -external:IC:\Users\<USER>\.conan2\p\boosta79d738920bc3\p\include -external:IC:\Users\<USER>\.conan2\p\scrip4cfd014ad4660\p\include -external:IC:\Users\<USER>\.conan2\p\nearg388d58da7a54c\p\include -external:IC:\Users\<USER>\.conan2\p\b\curaebb245e64b5ac7\p\include -external:IC:\Users\<USER>\.conan2\p\asio-c31dc2a380bf0\p\include -external:IC:\Users\<USER>\.conan2\p\grpc8b6c73b4e5756\p\include -external:IC:\Users\<USER>\.conan2\p\absei6a15444d60d47\p\include -external:W0
  OBJECT_DIR = CMakeFiles\_CuraEngine.dir
  OBJECT_FILE_DIR = CMakeFiles\_CuraEngine.dir\src\settings
  TARGET_COMPILE_PDB = CMakeFiles\_CuraEngine.dir\_CuraEngine.pdb
  TARGET_PDB = _CuraEngine.pdb

build CMakeFiles\_CuraEngine.dir\src\settings\PathConfigStorage.cpp.obj: CXX_COMPILER___CuraEngine_unscanned_Release C$:\Users\wsd07\vscode\Cura-Dev\CuraEngine\src\settings\PathConfigStorage.cpp || cmake_object_order_depends_target__CuraEngine
  CONFIG = Release
  DEFINES = -DAGRPC_BOOST_ASIO -DARCUS -DBOOST_ALL_NO_LIB -DBOOST_STACKTRACE_USE_NOOP -DBOOST_STACKTRACE_USE_WINDBG -DBOOST_STACKTRACE_USE_WINDBG_CACHED -DCARES_STATICLIB -DCURA_ENGINE_HASH=\"unknown\" -DCURA_ENGINE_VERSION=\"5.11.0-alpha.0\" -DENABLE_PLUGINS -DNOMINMAX -DSENTRY_ENVIRONMENT=\"development\" -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSTB_TEXTEDIT_KEYTYPE=unsigned
  FLAGS = /DWIN32 /D_WINDOWS /EHsc /O2 /Ob2 /DNDEBUG -std:c++20 -MD /utf-8 /permissive-
  INCLUDES = -IC:\Users\<USER>\vscode\Cura-Dev\CuraEngine\include -IC:\Users\<USER>\vscode\Cura-Dev\CuraEngine\build\Release -external:IC:\Users\<USER>\.conan2\p\b\arcusee7666f716199\p\include -external:IC:\Users\<USER>\.conan2\p\proto47a676cb9257b\p\include -external:IC:\Users\<USER>\.conan2\p\zlibcfb9789dc2a53\p\include -external:IC:\Users\<USER>\.conan2\p\spdlo2d4b540ab0f22\p\include -external:IC:\Users\<USER>\.conan2\p\fmtdb20aad6e1bd4\p\include -external:IC:\Users\<USER>\.conan2\p\range0301bf3d76d5d\p\include -external:IC:\Users\<USER>\.conan2\p\b\clippf8ddcd4a5d961\p\include -external:IC:\Users\<USER>\.conan2\p\mapbo34ee9aeb574f9\p\include -external:IC:\Users\<USER>\.conan2\p\mapbofe72fb50cf7c4\p\include -external:IC:\Users\<USER>\.conan2\p\mapbo1b62b48748b82\p\include -external:IC:\Users\<USER>\.conan2\p\rapidf7a3355ba53c4\p\include -external:IC:\Users\<USER>\.conan2\p\stb6342cecb318f5\p\include -external:IC:\Users\<USER>\.conan2\p\boosta79d738920bc3\p\include -external:IC:\Users\<USER>\.conan2\p\scrip4cfd014ad4660\p\include -external:IC:\Users\<USER>\.conan2\p\nearg388d58da7a54c\p\include -external:IC:\Users\<USER>\.conan2\p\b\curaebb245e64b5ac7\p\include -external:IC:\Users\<USER>\.conan2\p\asio-c31dc2a380bf0\p\include -external:IC:\Users\<USER>\.conan2\p\grpc8b6c73b4e5756\p\include -external:IC:\Users\<USER>\.conan2\p\absei6a15444d60d47\p\include -external:W0
  OBJECT_DIR = CMakeFiles\_CuraEngine.dir
  OBJECT_FILE_DIR = CMakeFiles\_CuraEngine.dir\src\settings
  TARGET_COMPILE_PDB = CMakeFiles\_CuraEngine.dir\_CuraEngine.pdb
  TARGET_PDB = _CuraEngine.pdb

build CMakeFiles\_CuraEngine.dir\src\settings\Settings.cpp.obj: CXX_COMPILER___CuraEngine_unscanned_Release C$:\Users\wsd07\vscode\Cura-Dev\CuraEngine\src\settings\Settings.cpp || cmake_object_order_depends_target__CuraEngine
  CONFIG = Release
  DEFINES = -DAGRPC_BOOST_ASIO -DARCUS -DBOOST_ALL_NO_LIB -DBOOST_STACKTRACE_USE_NOOP -DBOOST_STACKTRACE_USE_WINDBG -DBOOST_STACKTRACE_USE_WINDBG_CACHED -DCARES_STATICLIB -DCURA_ENGINE_HASH=\"unknown\" -DCURA_ENGINE_VERSION=\"5.11.0-alpha.0\" -DENABLE_PLUGINS -DNOMINMAX -DSENTRY_ENVIRONMENT=\"development\" -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSTB_TEXTEDIT_KEYTYPE=unsigned
  FLAGS = /DWIN32 /D_WINDOWS /EHsc /O2 /Ob2 /DNDEBUG -std:c++20 -MD /utf-8 /permissive-
  INCLUDES = -IC:\Users\<USER>\vscode\Cura-Dev\CuraEngine\include -IC:\Users\<USER>\vscode\Cura-Dev\CuraEngine\build\Release -external:IC:\Users\<USER>\.conan2\p\b\arcusee7666f716199\p\include -external:IC:\Users\<USER>\.conan2\p\proto47a676cb9257b\p\include -external:IC:\Users\<USER>\.conan2\p\zlibcfb9789dc2a53\p\include -external:IC:\Users\<USER>\.conan2\p\spdlo2d4b540ab0f22\p\include -external:IC:\Users\<USER>\.conan2\p\fmtdb20aad6e1bd4\p\include -external:IC:\Users\<USER>\.conan2\p\range0301bf3d76d5d\p\include -external:IC:\Users\<USER>\.conan2\p\b\clippf8ddcd4a5d961\p\include -external:IC:\Users\<USER>\.conan2\p\mapbo34ee9aeb574f9\p\include -external:IC:\Users\<USER>\.conan2\p\mapbofe72fb50cf7c4\p\include -external:IC:\Users\<USER>\.conan2\p\mapbo1b62b48748b82\p\include -external:IC:\Users\<USER>\.conan2\p\rapidf7a3355ba53c4\p\include -external:IC:\Users\<USER>\.conan2\p\stb6342cecb318f5\p\include -external:IC:\Users\<USER>\.conan2\p\boosta79d738920bc3\p\include -external:IC:\Users\<USER>\.conan2\p\scrip4cfd014ad4660\p\include -external:IC:\Users\<USER>\.conan2\p\nearg388d58da7a54c\p\include -external:IC:\Users\<USER>\.conan2\p\b\curaebb245e64b5ac7\p\include -external:IC:\Users\<USER>\.conan2\p\asio-c31dc2a380bf0\p\include -external:IC:\Users\<USER>\.conan2\p\grpc8b6c73b4e5756\p\include -external:IC:\Users\<USER>\.conan2\p\absei6a15444d60d47\p\include -external:W0
  OBJECT_DIR = CMakeFiles\_CuraEngine.dir
  OBJECT_FILE_DIR = CMakeFiles\_CuraEngine.dir\src\settings
  TARGET_COMPILE_PDB = CMakeFiles\_CuraEngine.dir\_CuraEngine.pdb
  TARGET_PDB = _CuraEngine.pdb

build CMakeFiles\_CuraEngine.dir\src\settings\ZSeamConfig.cpp.obj: CXX_COMPILER___CuraEngine_unscanned_Release C$:\Users\wsd07\vscode\Cura-Dev\CuraEngine\src\settings\ZSeamConfig.cpp || cmake_object_order_depends_target__CuraEngine
  CONFIG = Release
  DEFINES = -DAGRPC_BOOST_ASIO -DARCUS -DBOOST_ALL_NO_LIB -DBOOST_STACKTRACE_USE_NOOP -DBOOST_STACKTRACE_USE_WINDBG -DBOOST_STACKTRACE_USE_WINDBG_CACHED -DCARES_STATICLIB -DCURA_ENGINE_HASH=\"unknown\" -DCURA_ENGINE_VERSION=\"5.11.0-alpha.0\" -DENABLE_PLUGINS -DNOMINMAX -DSENTRY_ENVIRONMENT=\"development\" -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSTB_TEXTEDIT_KEYTYPE=unsigned
  FLAGS = /DWIN32 /D_WINDOWS /EHsc /O2 /Ob2 /DNDEBUG -std:c++20 -MD /utf-8 /permissive-
  INCLUDES = -IC:\Users\<USER>\vscode\Cura-Dev\CuraEngine\include -IC:\Users\<USER>\vscode\Cura-Dev\CuraEngine\build\Release -external:IC:\Users\<USER>\.conan2\p\b\arcusee7666f716199\p\include -external:IC:\Users\<USER>\.conan2\p\proto47a676cb9257b\p\include -external:IC:\Users\<USER>\.conan2\p\zlibcfb9789dc2a53\p\include -external:IC:\Users\<USER>\.conan2\p\spdlo2d4b540ab0f22\p\include -external:IC:\Users\<USER>\.conan2\p\fmtdb20aad6e1bd4\p\include -external:IC:\Users\<USER>\.conan2\p\range0301bf3d76d5d\p\include -external:IC:\Users\<USER>\.conan2\p\b\clippf8ddcd4a5d961\p\include -external:IC:\Users\<USER>\.conan2\p\mapbo34ee9aeb574f9\p\include -external:IC:\Users\<USER>\.conan2\p\mapbofe72fb50cf7c4\p\include -external:IC:\Users\<USER>\.conan2\p\mapbo1b62b48748b82\p\include -external:IC:\Users\<USER>\.conan2\p\rapidf7a3355ba53c4\p\include -external:IC:\Users\<USER>\.conan2\p\stb6342cecb318f5\p\include -external:IC:\Users\<USER>\.conan2\p\boosta79d738920bc3\p\include -external:IC:\Users\<USER>\.conan2\p\scrip4cfd014ad4660\p\include -external:IC:\Users\<USER>\.conan2\p\nearg388d58da7a54c\p\include -external:IC:\Users\<USER>\.conan2\p\b\curaebb245e64b5ac7\p\include -external:IC:\Users\<USER>\.conan2\p\asio-c31dc2a380bf0\p\include -external:IC:\Users\<USER>\.conan2\p\grpc8b6c73b4e5756\p\include -external:IC:\Users\<USER>\.conan2\p\absei6a15444d60d47\p\include -external:W0
  OBJECT_DIR = CMakeFiles\_CuraEngine.dir
  OBJECT_FILE_DIR = CMakeFiles\_CuraEngine.dir\src\settings
  TARGET_COMPILE_PDB = CMakeFiles\_CuraEngine.dir\_CuraEngine.pdb
  TARGET_PDB = _CuraEngine.pdb

build CMakeFiles\_CuraEngine.dir\src\utils\AABB.cpp.obj: CXX_COMPILER___CuraEngine_unscanned_Release C$:\Users\wsd07\vscode\Cura-Dev\CuraEngine\src\utils\AABB.cpp || cmake_object_order_depends_target__CuraEngine
  CONFIG = Release
  DEFINES = -DAGRPC_BOOST_ASIO -DARCUS -DBOOST_ALL_NO_LIB -DBOOST_STACKTRACE_USE_NOOP -DBOOST_STACKTRACE_USE_WINDBG -DBOOST_STACKTRACE_USE_WINDBG_CACHED -DCARES_STATICLIB -DCURA_ENGINE_HASH=\"unknown\" -DCURA_ENGINE_VERSION=\"5.11.0-alpha.0\" -DENABLE_PLUGINS -DNOMINMAX -DSENTRY_ENVIRONMENT=\"development\" -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSTB_TEXTEDIT_KEYTYPE=unsigned
  FLAGS = /DWIN32 /D_WINDOWS /EHsc /O2 /Ob2 /DNDEBUG -std:c++20 -MD /utf-8 /permissive-
  INCLUDES = -IC:\Users\<USER>\vscode\Cura-Dev\CuraEngine\include -IC:\Users\<USER>\vscode\Cura-Dev\CuraEngine\build\Release -external:IC:\Users\<USER>\.conan2\p\b\arcusee7666f716199\p\include -external:IC:\Users\<USER>\.conan2\p\proto47a676cb9257b\p\include -external:IC:\Users\<USER>\.conan2\p\zlibcfb9789dc2a53\p\include -external:IC:\Users\<USER>\.conan2\p\spdlo2d4b540ab0f22\p\include -external:IC:\Users\<USER>\.conan2\p\fmtdb20aad6e1bd4\p\include -external:IC:\Users\<USER>\.conan2\p\range0301bf3d76d5d\p\include -external:IC:\Users\<USER>\.conan2\p\b\clippf8ddcd4a5d961\p\include -external:IC:\Users\<USER>\.conan2\p\mapbo34ee9aeb574f9\p\include -external:IC:\Users\<USER>\.conan2\p\mapbofe72fb50cf7c4\p\include -external:IC:\Users\<USER>\.conan2\p\mapbo1b62b48748b82\p\include -external:IC:\Users\<USER>\.conan2\p\rapidf7a3355ba53c4\p\include -external:IC:\Users\<USER>\.conan2\p\stb6342cecb318f5\p\include -external:IC:\Users\<USER>\.conan2\p\boosta79d738920bc3\p\include -external:IC:\Users\<USER>\.conan2\p\scrip4cfd014ad4660\p\include -external:IC:\Users\<USER>\.conan2\p\nearg388d58da7a54c\p\include -external:IC:\Users\<USER>\.conan2\p\b\curaebb245e64b5ac7\p\include -external:IC:\Users\<USER>\.conan2\p\asio-c31dc2a380bf0\p\include -external:IC:\Users\<USER>\.conan2\p\grpc8b6c73b4e5756\p\include -external:IC:\Users\<USER>\.conan2\p\absei6a15444d60d47\p\include -external:W0
  OBJECT_DIR = CMakeFiles\_CuraEngine.dir
  OBJECT_FILE_DIR = CMakeFiles\_CuraEngine.dir\src\utils
  TARGET_COMPILE_PDB = CMakeFiles\_CuraEngine.dir\_CuraEngine.pdb
  TARGET_PDB = _CuraEngine.pdb

build CMakeFiles\_CuraEngine.dir\src\utils\AABB3D.cpp.obj: CXX_COMPILER___CuraEngine_unscanned_Release C$:\Users\wsd07\vscode\Cura-Dev\CuraEngine\src\utils\AABB3D.cpp || cmake_object_order_depends_target__CuraEngine
  CONFIG = Release
  DEFINES = -DAGRPC_BOOST_ASIO -DARCUS -DBOOST_ALL_NO_LIB -DBOOST_STACKTRACE_USE_NOOP -DBOOST_STACKTRACE_USE_WINDBG -DBOOST_STACKTRACE_USE_WINDBG_CACHED -DCARES_STATICLIB -DCURA_ENGINE_HASH=\"unknown\" -DCURA_ENGINE_VERSION=\"5.11.0-alpha.0\" -DENABLE_PLUGINS -DNOMINMAX -DSENTRY_ENVIRONMENT=\"development\" -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSTB_TEXTEDIT_KEYTYPE=unsigned
  FLAGS = /DWIN32 /D_WINDOWS /EHsc /O2 /Ob2 /DNDEBUG -std:c++20 -MD /utf-8 /permissive-
  INCLUDES = -IC:\Users\<USER>\vscode\Cura-Dev\CuraEngine\include -IC:\Users\<USER>\vscode\Cura-Dev\CuraEngine\build\Release -external:IC:\Users\<USER>\.conan2\p\b\arcusee7666f716199\p\include -external:IC:\Users\<USER>\.conan2\p\proto47a676cb9257b\p\include -external:IC:\Users\<USER>\.conan2\p\zlibcfb9789dc2a53\p\include -external:IC:\Users\<USER>\.conan2\p\spdlo2d4b540ab0f22\p\include -external:IC:\Users\<USER>\.conan2\p\fmtdb20aad6e1bd4\p\include -external:IC:\Users\<USER>\.conan2\p\range0301bf3d76d5d\p\include -external:IC:\Users\<USER>\.conan2\p\b\clippf8ddcd4a5d961\p\include -external:IC:\Users\<USER>\.conan2\p\mapbo34ee9aeb574f9\p\include -external:IC:\Users\<USER>\.conan2\p\mapbofe72fb50cf7c4\p\include -external:IC:\Users\<USER>\.conan2\p\mapbo1b62b48748b82\p\include -external:IC:\Users\<USER>\.conan2\p\rapidf7a3355ba53c4\p\include -external:IC:\Users\<USER>\.conan2\p\stb6342cecb318f5\p\include -external:IC:\Users\<USER>\.conan2\p\boosta79d738920bc3\p\include -external:IC:\Users\<USER>\.conan2\p\scrip4cfd014ad4660\p\include -external:IC:\Users\<USER>\.conan2\p\nearg388d58da7a54c\p\include -external:IC:\Users\<USER>\.conan2\p\b\curaebb245e64b5ac7\p\include -external:IC:\Users\<USER>\.conan2\p\asio-c31dc2a380bf0\p\include -external:IC:\Users\<USER>\.conan2\p\grpc8b6c73b4e5756\p\include -external:IC:\Users\<USER>\.conan2\p\absei6a15444d60d47\p\include -external:W0
  OBJECT_DIR = CMakeFiles\_CuraEngine.dir
  OBJECT_FILE_DIR = CMakeFiles\_CuraEngine.dir\src\utils
  TARGET_COMPILE_PDB = CMakeFiles\_CuraEngine.dir\_CuraEngine.pdb
  TARGET_PDB = _CuraEngine.pdb

build CMakeFiles\_CuraEngine.dir\src\utils\channel.cpp.obj: CXX_COMPILER___CuraEngine_unscanned_Release C$:\Users\wsd07\vscode\Cura-Dev\CuraEngine\src\utils\channel.cpp || cmake_object_order_depends_target__CuraEngine
  CONFIG = Release
  DEFINES = -DAGRPC_BOOST_ASIO -DARCUS -DBOOST_ALL_NO_LIB -DBOOST_STACKTRACE_USE_NOOP -DBOOST_STACKTRACE_USE_WINDBG -DBOOST_STACKTRACE_USE_WINDBG_CACHED -DCARES_STATICLIB -DCURA_ENGINE_HASH=\"unknown\" -DCURA_ENGINE_VERSION=\"5.11.0-alpha.0\" -DENABLE_PLUGINS -DNOMINMAX -DSENTRY_ENVIRONMENT=\"development\" -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSTB_TEXTEDIT_KEYTYPE=unsigned
  FLAGS = /DWIN32 /D_WINDOWS /EHsc /O2 /Ob2 /DNDEBUG -std:c++20 -MD /utf-8 /permissive-
  INCLUDES = -IC:\Users\<USER>\vscode\Cura-Dev\CuraEngine\include -IC:\Users\<USER>\vscode\Cura-Dev\CuraEngine\build\Release -external:IC:\Users\<USER>\.conan2\p\b\arcusee7666f716199\p\include -external:IC:\Users\<USER>\.conan2\p\proto47a676cb9257b\p\include -external:IC:\Users\<USER>\.conan2\p\zlibcfb9789dc2a53\p\include -external:IC:\Users\<USER>\.conan2\p\spdlo2d4b540ab0f22\p\include -external:IC:\Users\<USER>\.conan2\p\fmtdb20aad6e1bd4\p\include -external:IC:\Users\<USER>\.conan2\p\range0301bf3d76d5d\p\include -external:IC:\Users\<USER>\.conan2\p\b\clippf8ddcd4a5d961\p\include -external:IC:\Users\<USER>\.conan2\p\mapbo34ee9aeb574f9\p\include -external:IC:\Users\<USER>\.conan2\p\mapbofe72fb50cf7c4\p\include -external:IC:\Users\<USER>\.conan2\p\mapbo1b62b48748b82\p\include -external:IC:\Users\<USER>\.conan2\p\rapidf7a3355ba53c4\p\include -external:IC:\Users\<USER>\.conan2\p\stb6342cecb318f5\p\include -external:IC:\Users\<USER>\.conan2\p\boosta79d738920bc3\p\include -external:IC:\Users\<USER>\.conan2\p\scrip4cfd014ad4660\p\include -external:IC:\Users\<USER>\.conan2\p\nearg388d58da7a54c\p\include -external:IC:\Users\<USER>\.conan2\p\b\curaebb245e64b5ac7\p\include -external:IC:\Users\<USER>\.conan2\p\asio-c31dc2a380bf0\p\include -external:IC:\Users\<USER>\.conan2\p\grpc8b6c73b4e5756\p\include -external:IC:\Users\<USER>\.conan2\p\absei6a15444d60d47\p\include -external:W0
  OBJECT_DIR = CMakeFiles\_CuraEngine.dir
  OBJECT_FILE_DIR = CMakeFiles\_CuraEngine.dir\src\utils
  TARGET_COMPILE_PDB = CMakeFiles\_CuraEngine.dir\_CuraEngine.pdb
  TARGET_PDB = _CuraEngine.pdb

build CMakeFiles\_CuraEngine.dir\src\utils\Date.cpp.obj: CXX_COMPILER___CuraEngine_unscanned_Release C$:\Users\wsd07\vscode\Cura-Dev\CuraEngine\src\utils\Date.cpp || cmake_object_order_depends_target__CuraEngine
  CONFIG = Release
  DEFINES = -DAGRPC_BOOST_ASIO -DARCUS -DBOOST_ALL_NO_LIB -DBOOST_STACKTRACE_USE_NOOP -DBOOST_STACKTRACE_USE_WINDBG -DBOOST_STACKTRACE_USE_WINDBG_CACHED -DCARES_STATICLIB -DCURA_ENGINE_HASH=\"unknown\" -DCURA_ENGINE_VERSION=\"5.11.0-alpha.0\" -DENABLE_PLUGINS -DNOMINMAX -DSENTRY_ENVIRONMENT=\"development\" -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSTB_TEXTEDIT_KEYTYPE=unsigned
  FLAGS = /DWIN32 /D_WINDOWS /EHsc /O2 /Ob2 /DNDEBUG -std:c++20 -MD /utf-8 /permissive-
  INCLUDES = -IC:\Users\<USER>\vscode\Cura-Dev\CuraEngine\include -IC:\Users\<USER>\vscode\Cura-Dev\CuraEngine\build\Release -external:IC:\Users\<USER>\.conan2\p\b\arcusee7666f716199\p\include -external:IC:\Users\<USER>\.conan2\p\proto47a676cb9257b\p\include -external:IC:\Users\<USER>\.conan2\p\zlibcfb9789dc2a53\p\include -external:IC:\Users\<USER>\.conan2\p\spdlo2d4b540ab0f22\p\include -external:IC:\Users\<USER>\.conan2\p\fmtdb20aad6e1bd4\p\include -external:IC:\Users\<USER>\.conan2\p\range0301bf3d76d5d\p\include -external:IC:\Users\<USER>\.conan2\p\b\clippf8ddcd4a5d961\p\include -external:IC:\Users\<USER>\.conan2\p\mapbo34ee9aeb574f9\p\include -external:IC:\Users\<USER>\.conan2\p\mapbofe72fb50cf7c4\p\include -external:IC:\Users\<USER>\.conan2\p\mapbo1b62b48748b82\p\include -external:IC:\Users\<USER>\.conan2\p\rapidf7a3355ba53c4\p\include -external:IC:\Users\<USER>\.conan2\p\stb6342cecb318f5\p\include -external:IC:\Users\<USER>\.conan2\p\boosta79d738920bc3\p\include -external:IC:\Users\<USER>\.conan2\p\scrip4cfd014ad4660\p\include -external:IC:\Users\<USER>\.conan2\p\nearg388d58da7a54c\p\include -external:IC:\Users\<USER>\.conan2\p\b\curaebb245e64b5ac7\p\include -external:IC:\Users\<USER>\.conan2\p\asio-c31dc2a380bf0\p\include -external:IC:\Users\<USER>\.conan2\p\grpc8b6c73b4e5756\p\include -external:IC:\Users\<USER>\.conan2\p\absei6a15444d60d47\p\include -external:W0
  OBJECT_DIR = CMakeFiles\_CuraEngine.dir
  OBJECT_FILE_DIR = CMakeFiles\_CuraEngine.dir\src\utils
  TARGET_COMPILE_PDB = CMakeFiles\_CuraEngine.dir\_CuraEngine.pdb
  TARGET_PDB = _CuraEngine.pdb

build CMakeFiles\_CuraEngine.dir\src\utils\ExtrusionJunction.cpp.obj: CXX_COMPILER___CuraEngine_unscanned_Release C$:\Users\wsd07\vscode\Cura-Dev\CuraEngine\src\utils\ExtrusionJunction.cpp || cmake_object_order_depends_target__CuraEngine
  CONFIG = Release
  DEFINES = -DAGRPC_BOOST_ASIO -DARCUS -DBOOST_ALL_NO_LIB -DBOOST_STACKTRACE_USE_NOOP -DBOOST_STACKTRACE_USE_WINDBG -DBOOST_STACKTRACE_USE_WINDBG_CACHED -DCARES_STATICLIB -DCURA_ENGINE_HASH=\"unknown\" -DCURA_ENGINE_VERSION=\"5.11.0-alpha.0\" -DENABLE_PLUGINS -DNOMINMAX -DSENTRY_ENVIRONMENT=\"development\" -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSTB_TEXTEDIT_KEYTYPE=unsigned
  FLAGS = /DWIN32 /D_WINDOWS /EHsc /O2 /Ob2 /DNDEBUG -std:c++20 -MD /utf-8 /permissive-
  INCLUDES = -IC:\Users\<USER>\vscode\Cura-Dev\CuraEngine\include -IC:\Users\<USER>\vscode\Cura-Dev\CuraEngine\build\Release -external:IC:\Users\<USER>\.conan2\p\b\arcusee7666f716199\p\include -external:IC:\Users\<USER>\.conan2\p\proto47a676cb9257b\p\include -external:IC:\Users\<USER>\.conan2\p\zlibcfb9789dc2a53\p\include -external:IC:\Users\<USER>\.conan2\p\spdlo2d4b540ab0f22\p\include -external:IC:\Users\<USER>\.conan2\p\fmtdb20aad6e1bd4\p\include -external:IC:\Users\<USER>\.conan2\p\range0301bf3d76d5d\p\include -external:IC:\Users\<USER>\.conan2\p\b\clippf8ddcd4a5d961\p\include -external:IC:\Users\<USER>\.conan2\p\mapbo34ee9aeb574f9\p\include -external:IC:\Users\<USER>\.conan2\p\mapbofe72fb50cf7c4\p\include -external:IC:\Users\<USER>\.conan2\p\mapbo1b62b48748b82\p\include -external:IC:\Users\<USER>\.conan2\p\rapidf7a3355ba53c4\p\include -external:IC:\Users\<USER>\.conan2\p\stb6342cecb318f5\p\include -external:IC:\Users\<USER>\.conan2\p\boosta79d738920bc3\p\include -external:IC:\Users\<USER>\.conan2\p\scrip4cfd014ad4660\p\include -external:IC:\Users\<USER>\.conan2\p\nearg388d58da7a54c\p\include -external:IC:\Users\<USER>\.conan2\p\b\curaebb245e64b5ac7\p\include -external:IC:\Users\<USER>\.conan2\p\asio-c31dc2a380bf0\p\include -external:IC:\Users\<USER>\.conan2\p\grpc8b6c73b4e5756\p\include -external:IC:\Users\<USER>\.conan2\p\absei6a15444d60d47\p\include -external:W0
  OBJECT_DIR = CMakeFiles\_CuraEngine.dir
  OBJECT_FILE_DIR = CMakeFiles\_CuraEngine.dir\src\utils
  TARGET_COMPILE_PDB = CMakeFiles\_CuraEngine.dir\_CuraEngine.pdb
  TARGET_PDB = _CuraEngine.pdb

build CMakeFiles\_CuraEngine.dir\src\utils\ExtrusionLine.cpp.obj: CXX_COMPILER___CuraEngine_unscanned_Release C$:\Users\wsd07\vscode\Cura-Dev\CuraEngine\src\utils\ExtrusionLine.cpp || cmake_object_order_depends_target__CuraEngine
  CONFIG = Release
  DEFINES = -DAGRPC_BOOST_ASIO -DARCUS -DBOOST_ALL_NO_LIB -DBOOST_STACKTRACE_USE_NOOP -DBOOST_STACKTRACE_USE_WINDBG -DBOOST_STACKTRACE_USE_WINDBG_CACHED -DCARES_STATICLIB -DCURA_ENGINE_HASH=\"unknown\" -DCURA_ENGINE_VERSION=\"5.11.0-alpha.0\" -DENABLE_PLUGINS -DNOMINMAX -DSENTRY_ENVIRONMENT=\"development\" -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSTB_TEXTEDIT_KEYTYPE=unsigned
  FLAGS = /DWIN32 /D_WINDOWS /EHsc /O2 /Ob2 /DNDEBUG -std:c++20 -MD /utf-8 /permissive-
  INCLUDES = -IC:\Users\<USER>\vscode\Cura-Dev\CuraEngine\include -IC:\Users\<USER>\vscode\Cura-Dev\CuraEngine\build\Release -external:IC:\Users\<USER>\.conan2\p\b\arcusee7666f716199\p\include -external:IC:\Users\<USER>\.conan2\p\proto47a676cb9257b\p\include -external:IC:\Users\<USER>\.conan2\p\zlibcfb9789dc2a53\p\include -external:IC:\Users\<USER>\.conan2\p\spdlo2d4b540ab0f22\p\include -external:IC:\Users\<USER>\.conan2\p\fmtdb20aad6e1bd4\p\include -external:IC:\Users\<USER>\.conan2\p\range0301bf3d76d5d\p\include -external:IC:\Users\<USER>\.conan2\p\b\clippf8ddcd4a5d961\p\include -external:IC:\Users\<USER>\.conan2\p\mapbo34ee9aeb574f9\p\include -external:IC:\Users\<USER>\.conan2\p\mapbofe72fb50cf7c4\p\include -external:IC:\Users\<USER>\.conan2\p\mapbo1b62b48748b82\p\include -external:IC:\Users\<USER>\.conan2\p\rapidf7a3355ba53c4\p\include -external:IC:\Users\<USER>\.conan2\p\stb6342cecb318f5\p\include -external:IC:\Users\<USER>\.conan2\p\boosta79d738920bc3\p\include -external:IC:\Users\<USER>\.conan2\p\scrip4cfd014ad4660\p\include -external:IC:\Users\<USER>\.conan2\p\nearg388d58da7a54c\p\include -external:IC:\Users\<USER>\.conan2\p\b\curaebb245e64b5ac7\p\include -external:IC:\Users\<USER>\.conan2\p\asio-c31dc2a380bf0\p\include -external:IC:\Users\<USER>\.conan2\p\grpc8b6c73b4e5756\p\include -external:IC:\Users\<USER>\.conan2\p\absei6a15444d60d47\p\include -external:W0
  OBJECT_DIR = CMakeFiles\_CuraEngine.dir
  OBJECT_FILE_DIR = CMakeFiles\_CuraEngine.dir\src\utils
  TARGET_COMPILE_PDB = CMakeFiles\_CuraEngine.dir\_CuraEngine.pdb
  TARGET_PDB = _CuraEngine.pdb

build CMakeFiles\_CuraEngine.dir\src\utils\ExtrusionSegment.cpp.obj: CXX_COMPILER___CuraEngine_unscanned_Release C$:\Users\wsd07\vscode\Cura-Dev\CuraEngine\src\utils\ExtrusionSegment.cpp || cmake_object_order_depends_target__CuraEngine
  CONFIG = Release
  DEFINES = -DAGRPC_BOOST_ASIO -DARCUS -DBOOST_ALL_NO_LIB -DBOOST_STACKTRACE_USE_NOOP -DBOOST_STACKTRACE_USE_WINDBG -DBOOST_STACKTRACE_USE_WINDBG_CACHED -DCARES_STATICLIB -DCURA_ENGINE_HASH=\"unknown\" -DCURA_ENGINE_VERSION=\"5.11.0-alpha.0\" -DENABLE_PLUGINS -DNOMINMAX -DSENTRY_ENVIRONMENT=\"development\" -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSTB_TEXTEDIT_KEYTYPE=unsigned
  FLAGS = /DWIN32 /D_WINDOWS /EHsc /O2 /Ob2 /DNDEBUG -std:c++20 -MD /utf-8 /permissive-
  INCLUDES = -IC:\Users\<USER>\vscode\Cura-Dev\CuraEngine\include -IC:\Users\<USER>\vscode\Cura-Dev\CuraEngine\build\Release -external:IC:\Users\<USER>\.conan2\p\b\arcusee7666f716199\p\include -external:IC:\Users\<USER>\.conan2\p\proto47a676cb9257b\p\include -external:IC:\Users\<USER>\.conan2\p\zlibcfb9789dc2a53\p\include -external:IC:\Users\<USER>\.conan2\p\spdlo2d4b540ab0f22\p\include -external:IC:\Users\<USER>\.conan2\p\fmtdb20aad6e1bd4\p\include -external:IC:\Users\<USER>\.conan2\p\range0301bf3d76d5d\p\include -external:IC:\Users\<USER>\.conan2\p\b\clippf8ddcd4a5d961\p\include -external:IC:\Users\<USER>\.conan2\p\mapbo34ee9aeb574f9\p\include -external:IC:\Users\<USER>\.conan2\p\mapbofe72fb50cf7c4\p\include -external:IC:\Users\<USER>\.conan2\p\mapbo1b62b48748b82\p\include -external:IC:\Users\<USER>\.conan2\p\rapidf7a3355ba53c4\p\include -external:IC:\Users\<USER>\.conan2\p\stb6342cecb318f5\p\include -external:IC:\Users\<USER>\.conan2\p\boosta79d738920bc3\p\include -external:IC:\Users\<USER>\.conan2\p\scrip4cfd014ad4660\p\include -external:IC:\Users\<USER>\.conan2\p\nearg388d58da7a54c\p\include -external:IC:\Users\<USER>\.conan2\p\b\curaebb245e64b5ac7\p\include -external:IC:\Users\<USER>\.conan2\p\asio-c31dc2a380bf0\p\include -external:IC:\Users\<USER>\.conan2\p\grpc8b6c73b4e5756\p\include -external:IC:\Users\<USER>\.conan2\p\absei6a15444d60d47\p\include -external:W0
  OBJECT_DIR = CMakeFiles\_CuraEngine.dir
  OBJECT_FILE_DIR = CMakeFiles\_CuraEngine.dir\src\utils
  TARGET_COMPILE_PDB = CMakeFiles\_CuraEngine.dir\_CuraEngine.pdb
  TARGET_PDB = _CuraEngine.pdb

build CMakeFiles\_CuraEngine.dir\src\utils\gettime.cpp.obj: CXX_COMPILER___CuraEngine_unscanned_Release C$:\Users\wsd07\vscode\Cura-Dev\CuraEngine\src\utils\gettime.cpp || cmake_object_order_depends_target__CuraEngine
  CONFIG = Release
  DEFINES = -DAGRPC_BOOST_ASIO -DARCUS -DBOOST_ALL_NO_LIB -DBOOST_STACKTRACE_USE_NOOP -DBOOST_STACKTRACE_USE_WINDBG -DBOOST_STACKTRACE_USE_WINDBG_CACHED -DCARES_STATICLIB -DCURA_ENGINE_HASH=\"unknown\" -DCURA_ENGINE_VERSION=\"5.11.0-alpha.0\" -DENABLE_PLUGINS -DNOMINMAX -DSENTRY_ENVIRONMENT=\"development\" -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSTB_TEXTEDIT_KEYTYPE=unsigned
  FLAGS = /DWIN32 /D_WINDOWS /EHsc /O2 /Ob2 /DNDEBUG -std:c++20 -MD /utf-8 /permissive-
  INCLUDES = -IC:\Users\<USER>\vscode\Cura-Dev\CuraEngine\include -IC:\Users\<USER>\vscode\Cura-Dev\CuraEngine\build\Release -external:IC:\Users\<USER>\.conan2\p\b\arcusee7666f716199\p\include -external:IC:\Users\<USER>\.conan2\p\proto47a676cb9257b\p\include -external:IC:\Users\<USER>\.conan2\p\zlibcfb9789dc2a53\p\include -external:IC:\Users\<USER>\.conan2\p\spdlo2d4b540ab0f22\p\include -external:IC:\Users\<USER>\.conan2\p\fmtdb20aad6e1bd4\p\include -external:IC:\Users\<USER>\.conan2\p\range0301bf3d76d5d\p\include -external:IC:\Users\<USER>\.conan2\p\b\clippf8ddcd4a5d961\p\include -external:IC:\Users\<USER>\.conan2\p\mapbo34ee9aeb574f9\p\include -external:IC:\Users\<USER>\.conan2\p\mapbofe72fb50cf7c4\p\include -external:IC:\Users\<USER>\.conan2\p\mapbo1b62b48748b82\p\include -external:IC:\Users\<USER>\.conan2\p\rapidf7a3355ba53c4\p\include -external:IC:\Users\<USER>\.conan2\p\stb6342cecb318f5\p\include -external:IC:\Users\<USER>\.conan2\p\boosta79d738920bc3\p\include -external:IC:\Users\<USER>\.conan2\p\scrip4cfd014ad4660\p\include -external:IC:\Users\<USER>\.conan2\p\nearg388d58da7a54c\p\include -external:IC:\Users\<USER>\.conan2\p\b\curaebb245e64b5ac7\p\include -external:IC:\Users\<USER>\.conan2\p\asio-c31dc2a380bf0\p\include -external:IC:\Users\<USER>\.conan2\p\grpc8b6c73b4e5756\p\include -external:IC:\Users\<USER>\.conan2\p\absei6a15444d60d47\p\include -external:W0
  OBJECT_DIR = CMakeFiles\_CuraEngine.dir
  OBJECT_FILE_DIR = CMakeFiles\_CuraEngine.dir\src\utils
  TARGET_COMPILE_PDB = CMakeFiles\_CuraEngine.dir\_CuraEngine.pdb
  TARGET_PDB = _CuraEngine.pdb

build CMakeFiles\_CuraEngine.dir\src\utils\linearAlg2D.cpp.obj: CXX_COMPILER___CuraEngine_unscanned_Release C$:\Users\wsd07\vscode\Cura-Dev\CuraEngine\src\utils\linearAlg2D.cpp || cmake_object_order_depends_target__CuraEngine
  CONFIG = Release
  DEFINES = -DAGRPC_BOOST_ASIO -DARCUS -DBOOST_ALL_NO_LIB -DBOOST_STACKTRACE_USE_NOOP -DBOOST_STACKTRACE_USE_WINDBG -DBOOST_STACKTRACE_USE_WINDBG_CACHED -DCARES_STATICLIB -DCURA_ENGINE_HASH=\"unknown\" -DCURA_ENGINE_VERSION=\"5.11.0-alpha.0\" -DENABLE_PLUGINS -DNOMINMAX -DSENTRY_ENVIRONMENT=\"development\" -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSTB_TEXTEDIT_KEYTYPE=unsigned
  FLAGS = /DWIN32 /D_WINDOWS /EHsc /O2 /Ob2 /DNDEBUG -std:c++20 -MD /utf-8 /permissive-
  INCLUDES = -IC:\Users\<USER>\vscode\Cura-Dev\CuraEngine\include -IC:\Users\<USER>\vscode\Cura-Dev\CuraEngine\build\Release -external:IC:\Users\<USER>\.conan2\p\b\arcusee7666f716199\p\include -external:IC:\Users\<USER>\.conan2\p\proto47a676cb9257b\p\include -external:IC:\Users\<USER>\.conan2\p\zlibcfb9789dc2a53\p\include -external:IC:\Users\<USER>\.conan2\p\spdlo2d4b540ab0f22\p\include -external:IC:\Users\<USER>\.conan2\p\fmtdb20aad6e1bd4\p\include -external:IC:\Users\<USER>\.conan2\p\range0301bf3d76d5d\p\include -external:IC:\Users\<USER>\.conan2\p\b\clippf8ddcd4a5d961\p\include -external:IC:\Users\<USER>\.conan2\p\mapbo34ee9aeb574f9\p\include -external:IC:\Users\<USER>\.conan2\p\mapbofe72fb50cf7c4\p\include -external:IC:\Users\<USER>\.conan2\p\mapbo1b62b48748b82\p\include -external:IC:\Users\<USER>\.conan2\p\rapidf7a3355ba53c4\p\include -external:IC:\Users\<USER>\.conan2\p\stb6342cecb318f5\p\include -external:IC:\Users\<USER>\.conan2\p\boosta79d738920bc3\p\include -external:IC:\Users\<USER>\.conan2\p\scrip4cfd014ad4660\p\include -external:IC:\Users\<USER>\.conan2\p\nearg388d58da7a54c\p\include -external:IC:\Users\<USER>\.conan2\p\b\curaebb245e64b5ac7\p\include -external:IC:\Users\<USER>\.conan2\p\asio-c31dc2a380bf0\p\include -external:IC:\Users\<USER>\.conan2\p\grpc8b6c73b4e5756\p\include -external:IC:\Users\<USER>\.conan2\p\absei6a15444d60d47\p\include -external:W0
  OBJECT_DIR = CMakeFiles\_CuraEngine.dir
  OBJECT_FILE_DIR = CMakeFiles\_CuraEngine.dir\src\utils
  TARGET_COMPILE_PDB = CMakeFiles\_CuraEngine.dir\_CuraEngine.pdb
  TARGET_PDB = _CuraEngine.pdb

build CMakeFiles\_CuraEngine.dir\src\utils\ListPolyIt.cpp.obj: CXX_COMPILER___CuraEngine_unscanned_Release C$:\Users\wsd07\vscode\Cura-Dev\CuraEngine\src\utils\ListPolyIt.cpp || cmake_object_order_depends_target__CuraEngine
  CONFIG = Release
  DEFINES = -DAGRPC_BOOST_ASIO -DARCUS -DBOOST_ALL_NO_LIB -DBOOST_STACKTRACE_USE_NOOP -DBOOST_STACKTRACE_USE_WINDBG -DBOOST_STACKTRACE_USE_WINDBG_CACHED -DCARES_STATICLIB -DCURA_ENGINE_HASH=\"unknown\" -DCURA_ENGINE_VERSION=\"5.11.0-alpha.0\" -DENABLE_PLUGINS -DNOMINMAX -DSENTRY_ENVIRONMENT=\"development\" -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSTB_TEXTEDIT_KEYTYPE=unsigned
  FLAGS = /DWIN32 /D_WINDOWS /EHsc /O2 /Ob2 /DNDEBUG -std:c++20 -MD /utf-8 /permissive-
  INCLUDES = -IC:\Users\<USER>\vscode\Cura-Dev\CuraEngine\include -IC:\Users\<USER>\vscode\Cura-Dev\CuraEngine\build\Release -external:IC:\Users\<USER>\.conan2\p\b\arcusee7666f716199\p\include -external:IC:\Users\<USER>\.conan2\p\proto47a676cb9257b\p\include -external:IC:\Users\<USER>\.conan2\p\zlibcfb9789dc2a53\p\include -external:IC:\Users\<USER>\.conan2\p\spdlo2d4b540ab0f22\p\include -external:IC:\Users\<USER>\.conan2\p\fmtdb20aad6e1bd4\p\include -external:IC:\Users\<USER>\.conan2\p\range0301bf3d76d5d\p\include -external:IC:\Users\<USER>\.conan2\p\b\clippf8ddcd4a5d961\p\include -external:IC:\Users\<USER>\.conan2\p\mapbo34ee9aeb574f9\p\include -external:IC:\Users\<USER>\.conan2\p\mapbofe72fb50cf7c4\p\include -external:IC:\Users\<USER>\.conan2\p\mapbo1b62b48748b82\p\include -external:IC:\Users\<USER>\.conan2\p\rapidf7a3355ba53c4\p\include -external:IC:\Users\<USER>\.conan2\p\stb6342cecb318f5\p\include -external:IC:\Users\<USER>\.conan2\p\boosta79d738920bc3\p\include -external:IC:\Users\<USER>\.conan2\p\scrip4cfd014ad4660\p\include -external:IC:\Users\<USER>\.conan2\p\nearg388d58da7a54c\p\include -external:IC:\Users\<USER>\.conan2\p\b\curaebb245e64b5ac7\p\include -external:IC:\Users\<USER>\.conan2\p\asio-c31dc2a380bf0\p\include -external:IC:\Users\<USER>\.conan2\p\grpc8b6c73b4e5756\p\include -external:IC:\Users\<USER>\.conan2\p\absei6a15444d60d47\p\include -external:W0
  OBJECT_DIR = CMakeFiles\_CuraEngine.dir
  OBJECT_FILE_DIR = CMakeFiles\_CuraEngine.dir\src\utils
  TARGET_COMPILE_PDB = CMakeFiles\_CuraEngine.dir\_CuraEngine.pdb
  TARGET_PDB = _CuraEngine.pdb

build CMakeFiles\_CuraEngine.dir\src\utils\Matrix4x3D.cpp.obj: CXX_COMPILER___CuraEngine_unscanned_Release C$:\Users\wsd07\vscode\Cura-Dev\CuraEngine\src\utils\Matrix4x3D.cpp || cmake_object_order_depends_target__CuraEngine
  CONFIG = Release
  DEFINES = -DAGRPC_BOOST_ASIO -DARCUS -DBOOST_ALL_NO_LIB -DBOOST_STACKTRACE_USE_NOOP -DBOOST_STACKTRACE_USE_WINDBG -DBOOST_STACKTRACE_USE_WINDBG_CACHED -DCARES_STATICLIB -DCURA_ENGINE_HASH=\"unknown\" -DCURA_ENGINE_VERSION=\"5.11.0-alpha.0\" -DENABLE_PLUGINS -DNOMINMAX -DSENTRY_ENVIRONMENT=\"development\" -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSTB_TEXTEDIT_KEYTYPE=unsigned
  FLAGS = /DWIN32 /D_WINDOWS /EHsc /O2 /Ob2 /DNDEBUG -std:c++20 -MD /utf-8 /permissive-
  INCLUDES = -IC:\Users\<USER>\vscode\Cura-Dev\CuraEngine\include -IC:\Users\<USER>\vscode\Cura-Dev\CuraEngine\build\Release -external:IC:\Users\<USER>\.conan2\p\b\arcusee7666f716199\p\include -external:IC:\Users\<USER>\.conan2\p\proto47a676cb9257b\p\include -external:IC:\Users\<USER>\.conan2\p\zlibcfb9789dc2a53\p\include -external:IC:\Users\<USER>\.conan2\p\spdlo2d4b540ab0f22\p\include -external:IC:\Users\<USER>\.conan2\p\fmtdb20aad6e1bd4\p\include -external:IC:\Users\<USER>\.conan2\p\range0301bf3d76d5d\p\include -external:IC:\Users\<USER>\.conan2\p\b\clippf8ddcd4a5d961\p\include -external:IC:\Users\<USER>\.conan2\p\mapbo34ee9aeb574f9\p\include -external:IC:\Users\<USER>\.conan2\p\mapbofe72fb50cf7c4\p\include -external:IC:\Users\<USER>\.conan2\p\mapbo1b62b48748b82\p\include -external:IC:\Users\<USER>\.conan2\p\rapidf7a3355ba53c4\p\include -external:IC:\Users\<USER>\.conan2\p\stb6342cecb318f5\p\include -external:IC:\Users\<USER>\.conan2\p\boosta79d738920bc3\p\include -external:IC:\Users\<USER>\.conan2\p\scrip4cfd014ad4660\p\include -external:IC:\Users\<USER>\.conan2\p\nearg388d58da7a54c\p\include -external:IC:\Users\<USER>\.conan2\p\b\curaebb245e64b5ac7\p\include -external:IC:\Users\<USER>\.conan2\p\asio-c31dc2a380bf0\p\include -external:IC:\Users\<USER>\.conan2\p\grpc8b6c73b4e5756\p\include -external:IC:\Users\<USER>\.conan2\p\absei6a15444d60d47\p\include -external:W0
  OBJECT_DIR = CMakeFiles\_CuraEngine.dir
  OBJECT_FILE_DIR = CMakeFiles\_CuraEngine.dir\src\utils
  TARGET_COMPILE_PDB = CMakeFiles\_CuraEngine.dir\_CuraEngine.pdb
  TARGET_PDB = _CuraEngine.pdb

build CMakeFiles\_CuraEngine.dir\src\utils\MinimumSpanningTree.cpp.obj: CXX_COMPILER___CuraEngine_unscanned_Release C$:\Users\wsd07\vscode\Cura-Dev\CuraEngine\src\utils\MinimumSpanningTree.cpp || cmake_object_order_depends_target__CuraEngine
  CONFIG = Release
  DEFINES = -DAGRPC_BOOST_ASIO -DARCUS -DBOOST_ALL_NO_LIB -DBOOST_STACKTRACE_USE_NOOP -DBOOST_STACKTRACE_USE_WINDBG -DBOOST_STACKTRACE_USE_WINDBG_CACHED -DCARES_STATICLIB -DCURA_ENGINE_HASH=\"unknown\" -DCURA_ENGINE_VERSION=\"5.11.0-alpha.0\" -DENABLE_PLUGINS -DNOMINMAX -DSENTRY_ENVIRONMENT=\"development\" -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSTB_TEXTEDIT_KEYTYPE=unsigned
  FLAGS = /DWIN32 /D_WINDOWS /EHsc /O2 /Ob2 /DNDEBUG -std:c++20 -MD /utf-8 /permissive-
  INCLUDES = -IC:\Users\<USER>\vscode\Cura-Dev\CuraEngine\include -IC:\Users\<USER>\vscode\Cura-Dev\CuraEngine\build\Release -external:IC:\Users\<USER>\.conan2\p\b\arcusee7666f716199\p\include -external:IC:\Users\<USER>\.conan2\p\proto47a676cb9257b\p\include -external:IC:\Users\<USER>\.conan2\p\zlibcfb9789dc2a53\p\include -external:IC:\Users\<USER>\.conan2\p\spdlo2d4b540ab0f22\p\include -external:IC:\Users\<USER>\.conan2\p\fmtdb20aad6e1bd4\p\include -external:IC:\Users\<USER>\.conan2\p\range0301bf3d76d5d\p\include -external:IC:\Users\<USER>\.conan2\p\b\clippf8ddcd4a5d961\p\include -external:IC:\Users\<USER>\.conan2\p\mapbo34ee9aeb574f9\p\include -external:IC:\Users\<USER>\.conan2\p\mapbofe72fb50cf7c4\p\include -external:IC:\Users\<USER>\.conan2\p\mapbo1b62b48748b82\p\include -external:IC:\Users\<USER>\.conan2\p\rapidf7a3355ba53c4\p\include -external:IC:\Users\<USER>\.conan2\p\stb6342cecb318f5\p\include -external:IC:\Users\<USER>\.conan2\p\boosta79d738920bc3\p\include -external:IC:\Users\<USER>\.conan2\p\scrip4cfd014ad4660\p\include -external:IC:\Users\<USER>\.conan2\p\nearg388d58da7a54c\p\include -external:IC:\Users\<USER>\.conan2\p\b\curaebb245e64b5ac7\p\include -external:IC:\Users\<USER>\.conan2\p\asio-c31dc2a380bf0\p\include -external:IC:\Users\<USER>\.conan2\p\grpc8b6c73b4e5756\p\include -external:IC:\Users\<USER>\.conan2\p\absei6a15444d60d47\p\include -external:W0
  OBJECT_DIR = CMakeFiles\_CuraEngine.dir
  OBJECT_FILE_DIR = CMakeFiles\_CuraEngine.dir\src\utils
  TARGET_COMPILE_PDB = CMakeFiles\_CuraEngine.dir\_CuraEngine.pdb
  TARGET_PDB = _CuraEngine.pdb

build CMakeFiles\_CuraEngine.dir\src\utils\PolygonConnector.cpp.obj: CXX_COMPILER___CuraEngine_unscanned_Release C$:\Users\wsd07\vscode\Cura-Dev\CuraEngine\src\utils\PolygonConnector.cpp || cmake_object_order_depends_target__CuraEngine
  CONFIG = Release
  DEFINES = -DAGRPC_BOOST_ASIO -DARCUS -DBOOST_ALL_NO_LIB -DBOOST_STACKTRACE_USE_NOOP -DBOOST_STACKTRACE_USE_WINDBG -DBOOST_STACKTRACE_USE_WINDBG_CACHED -DCARES_STATICLIB -DCURA_ENGINE_HASH=\"unknown\" -DCURA_ENGINE_VERSION=\"5.11.0-alpha.0\" -DENABLE_PLUGINS -DNOMINMAX -DSENTRY_ENVIRONMENT=\"development\" -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSTB_TEXTEDIT_KEYTYPE=unsigned
  FLAGS = /DWIN32 /D_WINDOWS /EHsc /O2 /Ob2 /DNDEBUG -std:c++20 -MD /utf-8 /permissive-
  INCLUDES = -IC:\Users\<USER>\vscode\Cura-Dev\CuraEngine\include -IC:\Users\<USER>\vscode\Cura-Dev\CuraEngine\build\Release -external:IC:\Users\<USER>\.conan2\p\b\arcusee7666f716199\p\include -external:IC:\Users\<USER>\.conan2\p\proto47a676cb9257b\p\include -external:IC:\Users\<USER>\.conan2\p\zlibcfb9789dc2a53\p\include -external:IC:\Users\<USER>\.conan2\p\spdlo2d4b540ab0f22\p\include -external:IC:\Users\<USER>\.conan2\p\fmtdb20aad6e1bd4\p\include -external:IC:\Users\<USER>\.conan2\p\range0301bf3d76d5d\p\include -external:IC:\Users\<USER>\.conan2\p\b\clippf8ddcd4a5d961\p\include -external:IC:\Users\<USER>\.conan2\p\mapbo34ee9aeb574f9\p\include -external:IC:\Users\<USER>\.conan2\p\mapbofe72fb50cf7c4\p\include -external:IC:\Users\<USER>\.conan2\p\mapbo1b62b48748b82\p\include -external:IC:\Users\<USER>\.conan2\p\rapidf7a3355ba53c4\p\include -external:IC:\Users\<USER>\.conan2\p\stb6342cecb318f5\p\include -external:IC:\Users\<USER>\.conan2\p\boosta79d738920bc3\p\include -external:IC:\Users\<USER>\.conan2\p\scrip4cfd014ad4660\p\include -external:IC:\Users\<USER>\.conan2\p\nearg388d58da7a54c\p\include -external:IC:\Users\<USER>\.conan2\p\b\curaebb245e64b5ac7\p\include -external:IC:\Users\<USER>\.conan2\p\asio-c31dc2a380bf0\p\include -external:IC:\Users\<USER>\.conan2\p\grpc8b6c73b4e5756\p\include -external:IC:\Users\<USER>\.conan2\p\absei6a15444d60d47\p\include -external:W0
  OBJECT_DIR = CMakeFiles\_CuraEngine.dir
  OBJECT_FILE_DIR = CMakeFiles\_CuraEngine.dir\src\utils
  TARGET_COMPILE_PDB = CMakeFiles\_CuraEngine.dir\_CuraEngine.pdb
  TARGET_PDB = _CuraEngine.pdb

build CMakeFiles\_CuraEngine.dir\src\utils\PolygonsPointIndex.cpp.obj: CXX_COMPILER___CuraEngine_unscanned_Release C$:\Users\wsd07\vscode\Cura-Dev\CuraEngine\src\utils\PolygonsPointIndex.cpp || cmake_object_order_depends_target__CuraEngine
  CONFIG = Release
  DEFINES = -DAGRPC_BOOST_ASIO -DARCUS -DBOOST_ALL_NO_LIB -DBOOST_STACKTRACE_USE_NOOP -DBOOST_STACKTRACE_USE_WINDBG -DBOOST_STACKTRACE_USE_WINDBG_CACHED -DCARES_STATICLIB -DCURA_ENGINE_HASH=\"unknown\" -DCURA_ENGINE_VERSION=\"5.11.0-alpha.0\" -DENABLE_PLUGINS -DNOMINMAX -DSENTRY_ENVIRONMENT=\"development\" -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSTB_TEXTEDIT_KEYTYPE=unsigned
  FLAGS = /DWIN32 /D_WINDOWS /EHsc /O2 /Ob2 /DNDEBUG -std:c++20 -MD /utf-8 /permissive-
  INCLUDES = -IC:\Users\<USER>\vscode\Cura-Dev\CuraEngine\include -IC:\Users\<USER>\vscode\Cura-Dev\CuraEngine\build\Release -external:IC:\Users\<USER>\.conan2\p\b\arcusee7666f716199\p\include -external:IC:\Users\<USER>\.conan2\p\proto47a676cb9257b\p\include -external:IC:\Users\<USER>\.conan2\p\zlibcfb9789dc2a53\p\include -external:IC:\Users\<USER>\.conan2\p\spdlo2d4b540ab0f22\p\include -external:IC:\Users\<USER>\.conan2\p\fmtdb20aad6e1bd4\p\include -external:IC:\Users\<USER>\.conan2\p\range0301bf3d76d5d\p\include -external:IC:\Users\<USER>\.conan2\p\b\clippf8ddcd4a5d961\p\include -external:IC:\Users\<USER>\.conan2\p\mapbo34ee9aeb574f9\p\include -external:IC:\Users\<USER>\.conan2\p\mapbofe72fb50cf7c4\p\include -external:IC:\Users\<USER>\.conan2\p\mapbo1b62b48748b82\p\include -external:IC:\Users\<USER>\.conan2\p\rapidf7a3355ba53c4\p\include -external:IC:\Users\<USER>\.conan2\p\stb6342cecb318f5\p\include -external:IC:\Users\<USER>\.conan2\p\boosta79d738920bc3\p\include -external:IC:\Users\<USER>\.conan2\p\scrip4cfd014ad4660\p\include -external:IC:\Users\<USER>\.conan2\p\nearg388d58da7a54c\p\include -external:IC:\Users\<USER>\.conan2\p\b\curaebb245e64b5ac7\p\include -external:IC:\Users\<USER>\.conan2\p\asio-c31dc2a380bf0\p\include -external:IC:\Users\<USER>\.conan2\p\grpc8b6c73b4e5756\p\include -external:IC:\Users\<USER>\.conan2\p\absei6a15444d60d47\p\include -external:W0
  OBJECT_DIR = CMakeFiles\_CuraEngine.dir
  OBJECT_FILE_DIR = CMakeFiles\_CuraEngine.dir\src\utils
  TARGET_COMPILE_PDB = CMakeFiles\_CuraEngine.dir\_CuraEngine.pdb
  TARGET_PDB = _CuraEngine.pdb

build CMakeFiles\_CuraEngine.dir\src\utils\PolygonsSegmentIndex.cpp.obj: CXX_COMPILER___CuraEngine_unscanned_Release C$:\Users\wsd07\vscode\Cura-Dev\CuraEngine\src\utils\PolygonsSegmentIndex.cpp || cmake_object_order_depends_target__CuraEngine
  CONFIG = Release
  DEFINES = -DAGRPC_BOOST_ASIO -DARCUS -DBOOST_ALL_NO_LIB -DBOOST_STACKTRACE_USE_NOOP -DBOOST_STACKTRACE_USE_WINDBG -DBOOST_STACKTRACE_USE_WINDBG_CACHED -DCARES_STATICLIB -DCURA_ENGINE_HASH=\"unknown\" -DCURA_ENGINE_VERSION=\"5.11.0-alpha.0\" -DENABLE_PLUGINS -DNOMINMAX -DSENTRY_ENVIRONMENT=\"development\" -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSTB_TEXTEDIT_KEYTYPE=unsigned
  FLAGS = /DWIN32 /D_WINDOWS /EHsc /O2 /Ob2 /DNDEBUG -std:c++20 -MD /utf-8 /permissive-
  INCLUDES = -IC:\Users\<USER>\vscode\Cura-Dev\CuraEngine\include -IC:\Users\<USER>\vscode\Cura-Dev\CuraEngine\build\Release -external:IC:\Users\<USER>\.conan2\p\b\arcusee7666f716199\p\include -external:IC:\Users\<USER>\.conan2\p\proto47a676cb9257b\p\include -external:IC:\Users\<USER>\.conan2\p\zlibcfb9789dc2a53\p\include -external:IC:\Users\<USER>\.conan2\p\spdlo2d4b540ab0f22\p\include -external:IC:\Users\<USER>\.conan2\p\fmtdb20aad6e1bd4\p\include -external:IC:\Users\<USER>\.conan2\p\range0301bf3d76d5d\p\include -external:IC:\Users\<USER>\.conan2\p\b\clippf8ddcd4a5d961\p\include -external:IC:\Users\<USER>\.conan2\p\mapbo34ee9aeb574f9\p\include -external:IC:\Users\<USER>\.conan2\p\mapbofe72fb50cf7c4\p\include -external:IC:\Users\<USER>\.conan2\p\mapbo1b62b48748b82\p\include -external:IC:\Users\<USER>\.conan2\p\rapidf7a3355ba53c4\p\include -external:IC:\Users\<USER>\.conan2\p\stb6342cecb318f5\p\include -external:IC:\Users\<USER>\.conan2\p\boosta79d738920bc3\p\include -external:IC:\Users\<USER>\.conan2\p\scrip4cfd014ad4660\p\include -external:IC:\Users\<USER>\.conan2\p\nearg388d58da7a54c\p\include -external:IC:\Users\<USER>\.conan2\p\b\curaebb245e64b5ac7\p\include -external:IC:\Users\<USER>\.conan2\p\asio-c31dc2a380bf0\p\include -external:IC:\Users\<USER>\.conan2\p\grpc8b6c73b4e5756\p\include -external:IC:\Users\<USER>\.conan2\p\absei6a15444d60d47\p\include -external:W0
  OBJECT_DIR = CMakeFiles\_CuraEngine.dir
  OBJECT_FILE_DIR = CMakeFiles\_CuraEngine.dir\src\utils
  TARGET_COMPILE_PDB = CMakeFiles\_CuraEngine.dir\_CuraEngine.pdb
  TARGET_PDB = _CuraEngine.pdb

build CMakeFiles\_CuraEngine.dir\src\utils\polygonUtils.cpp.obj: CXX_COMPILER___CuraEngine_unscanned_Release C$:\Users\wsd07\vscode\Cura-Dev\CuraEngine\src\utils\polygonUtils.cpp || cmake_object_order_depends_target__CuraEngine
  CONFIG = Release
  DEFINES = -DAGRPC_BOOST_ASIO -DARCUS -DBOOST_ALL_NO_LIB -DBOOST_STACKTRACE_USE_NOOP -DBOOST_STACKTRACE_USE_WINDBG -DBOOST_STACKTRACE_USE_WINDBG_CACHED -DCARES_STATICLIB -DCURA_ENGINE_HASH=\"unknown\" -DCURA_ENGINE_VERSION=\"5.11.0-alpha.0\" -DENABLE_PLUGINS -DNOMINMAX -DSENTRY_ENVIRONMENT=\"development\" -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSTB_TEXTEDIT_KEYTYPE=unsigned
  FLAGS = /DWIN32 /D_WINDOWS /EHsc /O2 /Ob2 /DNDEBUG -std:c++20 -MD /utf-8 /permissive-
  INCLUDES = -IC:\Users\<USER>\vscode\Cura-Dev\CuraEngine\include -IC:\Users\<USER>\vscode\Cura-Dev\CuraEngine\build\Release -external:IC:\Users\<USER>\.conan2\p\b\arcusee7666f716199\p\include -external:IC:\Users\<USER>\.conan2\p\proto47a676cb9257b\p\include -external:IC:\Users\<USER>\.conan2\p\zlibcfb9789dc2a53\p\include -external:IC:\Users\<USER>\.conan2\p\spdlo2d4b540ab0f22\p\include -external:IC:\Users\<USER>\.conan2\p\fmtdb20aad6e1bd4\p\include -external:IC:\Users\<USER>\.conan2\p\range0301bf3d76d5d\p\include -external:IC:\Users\<USER>\.conan2\p\b\clippf8ddcd4a5d961\p\include -external:IC:\Users\<USER>\.conan2\p\mapbo34ee9aeb574f9\p\include -external:IC:\Users\<USER>\.conan2\p\mapbofe72fb50cf7c4\p\include -external:IC:\Users\<USER>\.conan2\p\mapbo1b62b48748b82\p\include -external:IC:\Users\<USER>\.conan2\p\rapidf7a3355ba53c4\p\include -external:IC:\Users\<USER>\.conan2\p\stb6342cecb318f5\p\include -external:IC:\Users\<USER>\.conan2\p\boosta79d738920bc3\p\include -external:IC:\Users\<USER>\.conan2\p\scrip4cfd014ad4660\p\include -external:IC:\Users\<USER>\.conan2\p\nearg388d58da7a54c\p\include -external:IC:\Users\<USER>\.conan2\p\b\curaebb245e64b5ac7\p\include -external:IC:\Users\<USER>\.conan2\p\asio-c31dc2a380bf0\p\include -external:IC:\Users\<USER>\.conan2\p\grpc8b6c73b4e5756\p\include -external:IC:\Users\<USER>\.conan2\p\absei6a15444d60d47\p\include -external:W0
  OBJECT_DIR = CMakeFiles\_CuraEngine.dir
  OBJECT_FILE_DIR = CMakeFiles\_CuraEngine.dir\src\utils
  TARGET_COMPILE_PDB = CMakeFiles\_CuraEngine.dir\_CuraEngine.pdb
  TARGET_PDB = _CuraEngine.pdb

build CMakeFiles\_CuraEngine.dir\src\utils\PolylineStitcher.cpp.obj: CXX_COMPILER___CuraEngine_unscanned_Release C$:\Users\wsd07\vscode\Cura-Dev\CuraEngine\src\utils\PolylineStitcher.cpp || cmake_object_order_depends_target__CuraEngine
  CONFIG = Release
  DEFINES = -DAGRPC_BOOST_ASIO -DARCUS -DBOOST_ALL_NO_LIB -DBOOST_STACKTRACE_USE_NOOP -DBOOST_STACKTRACE_USE_WINDBG -DBOOST_STACKTRACE_USE_WINDBG_CACHED -DCARES_STATICLIB -DCURA_ENGINE_HASH=\"unknown\" -DCURA_ENGINE_VERSION=\"5.11.0-alpha.0\" -DENABLE_PLUGINS -DNOMINMAX -DSENTRY_ENVIRONMENT=\"development\" -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSTB_TEXTEDIT_KEYTYPE=unsigned
  FLAGS = /DWIN32 /D_WINDOWS /EHsc /O2 /Ob2 /DNDEBUG -std:c++20 -MD /utf-8 /permissive-
  INCLUDES = -IC:\Users\<USER>\vscode\Cura-Dev\CuraEngine\include -IC:\Users\<USER>\vscode\Cura-Dev\CuraEngine\build\Release -external:IC:\Users\<USER>\.conan2\p\b\arcusee7666f716199\p\include -external:IC:\Users\<USER>\.conan2\p\proto47a676cb9257b\p\include -external:IC:\Users\<USER>\.conan2\p\zlibcfb9789dc2a53\p\include -external:IC:\Users\<USER>\.conan2\p\spdlo2d4b540ab0f22\p\include -external:IC:\Users\<USER>\.conan2\p\fmtdb20aad6e1bd4\p\include -external:IC:\Users\<USER>\.conan2\p\range0301bf3d76d5d\p\include -external:IC:\Users\<USER>\.conan2\p\b\clippf8ddcd4a5d961\p\include -external:IC:\Users\<USER>\.conan2\p\mapbo34ee9aeb574f9\p\include -external:IC:\Users\<USER>\.conan2\p\mapbofe72fb50cf7c4\p\include -external:IC:\Users\<USER>\.conan2\p\mapbo1b62b48748b82\p\include -external:IC:\Users\<USER>\.conan2\p\rapidf7a3355ba53c4\p\include -external:IC:\Users\<USER>\.conan2\p\stb6342cecb318f5\p\include -external:IC:\Users\<USER>\.conan2\p\boosta79d738920bc3\p\include -external:IC:\Users\<USER>\.conan2\p\scrip4cfd014ad4660\p\include -external:IC:\Users\<USER>\.conan2\p\nearg388d58da7a54c\p\include -external:IC:\Users\<USER>\.conan2\p\b\curaebb245e64b5ac7\p\include -external:IC:\Users\<USER>\.conan2\p\asio-c31dc2a380bf0\p\include -external:IC:\Users\<USER>\.conan2\p\grpc8b6c73b4e5756\p\include -external:IC:\Users\<USER>\.conan2\p\absei6a15444d60d47\p\include -external:W0
  OBJECT_DIR = CMakeFiles\_CuraEngine.dir
  OBJECT_FILE_DIR = CMakeFiles\_CuraEngine.dir\src\utils
  TARGET_COMPILE_PDB = CMakeFiles\_CuraEngine.dir\_CuraEngine.pdb
  TARGET_PDB = _CuraEngine.pdb

build CMakeFiles\_CuraEngine.dir\src\utils\Simplify.cpp.obj: CXX_COMPILER___CuraEngine_unscanned_Release C$:\Users\wsd07\vscode\Cura-Dev\CuraEngine\src\utils\Simplify.cpp || cmake_object_order_depends_target__CuraEngine
  CONFIG = Release
  DEFINES = -DAGRPC_BOOST_ASIO -DARCUS -DBOOST_ALL_NO_LIB -DBOOST_STACKTRACE_USE_NOOP -DBOOST_STACKTRACE_USE_WINDBG -DBOOST_STACKTRACE_USE_WINDBG_CACHED -DCARES_STATICLIB -DCURA_ENGINE_HASH=\"unknown\" -DCURA_ENGINE_VERSION=\"5.11.0-alpha.0\" -DENABLE_PLUGINS -DNOMINMAX -DSENTRY_ENVIRONMENT=\"development\" -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSTB_TEXTEDIT_KEYTYPE=unsigned
  FLAGS = /DWIN32 /D_WINDOWS /EHsc /O2 /Ob2 /DNDEBUG -std:c++20 -MD /utf-8 /permissive-
  INCLUDES = -IC:\Users\<USER>\vscode\Cura-Dev\CuraEngine\include -IC:\Users\<USER>\vscode\Cura-Dev\CuraEngine\build\Release -external:IC:\Users\<USER>\.conan2\p\b\arcusee7666f716199\p\include -external:IC:\Users\<USER>\.conan2\p\proto47a676cb9257b\p\include -external:IC:\Users\<USER>\.conan2\p\zlibcfb9789dc2a53\p\include -external:IC:\Users\<USER>\.conan2\p\spdlo2d4b540ab0f22\p\include -external:IC:\Users\<USER>\.conan2\p\fmtdb20aad6e1bd4\p\include -external:IC:\Users\<USER>\.conan2\p\range0301bf3d76d5d\p\include -external:IC:\Users\<USER>\.conan2\p\b\clippf8ddcd4a5d961\p\include -external:IC:\Users\<USER>\.conan2\p\mapbo34ee9aeb574f9\p\include -external:IC:\Users\<USER>\.conan2\p\mapbofe72fb50cf7c4\p\include -external:IC:\Users\<USER>\.conan2\p\mapbo1b62b48748b82\p\include -external:IC:\Users\<USER>\.conan2\p\rapidf7a3355ba53c4\p\include -external:IC:\Users\<USER>\.conan2\p\stb6342cecb318f5\p\include -external:IC:\Users\<USER>\.conan2\p\boosta79d738920bc3\p\include -external:IC:\Users\<USER>\.conan2\p\scrip4cfd014ad4660\p\include -external:IC:\Users\<USER>\.conan2\p\nearg388d58da7a54c\p\include -external:IC:\Users\<USER>\.conan2\p\b\curaebb245e64b5ac7\p\include -external:IC:\Users\<USER>\.conan2\p\asio-c31dc2a380bf0\p\include -external:IC:\Users\<USER>\.conan2\p\grpc8b6c73b4e5756\p\include -external:IC:\Users\<USER>\.conan2\p\absei6a15444d60d47\p\include -external:W0
  OBJECT_DIR = CMakeFiles\_CuraEngine.dir
  OBJECT_FILE_DIR = CMakeFiles\_CuraEngine.dir\src\utils
  TARGET_COMPILE_PDB = CMakeFiles\_CuraEngine.dir\_CuraEngine.pdb
  TARGET_PDB = _CuraEngine.pdb

build CMakeFiles\_CuraEngine.dir\src\utils\SVG.cpp.obj: CXX_COMPILER___CuraEngine_unscanned_Release C$:\Users\wsd07\vscode\Cura-Dev\CuraEngine\src\utils\SVG.cpp || cmake_object_order_depends_target__CuraEngine
  CONFIG = Release
  DEFINES = -DAGRPC_BOOST_ASIO -DARCUS -DBOOST_ALL_NO_LIB -DBOOST_STACKTRACE_USE_NOOP -DBOOST_STACKTRACE_USE_WINDBG -DBOOST_STACKTRACE_USE_WINDBG_CACHED -DCARES_STATICLIB -DCURA_ENGINE_HASH=\"unknown\" -DCURA_ENGINE_VERSION=\"5.11.0-alpha.0\" -DENABLE_PLUGINS -DNOMINMAX -DSENTRY_ENVIRONMENT=\"development\" -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSTB_TEXTEDIT_KEYTYPE=unsigned
  FLAGS = /DWIN32 /D_WINDOWS /EHsc /O2 /Ob2 /DNDEBUG -std:c++20 -MD /utf-8 /permissive-
  INCLUDES = -IC:\Users\<USER>\vscode\Cura-Dev\CuraEngine\include -IC:\Users\<USER>\vscode\Cura-Dev\CuraEngine\build\Release -external:IC:\Users\<USER>\.conan2\p\b\arcusee7666f716199\p\include -external:IC:\Users\<USER>\.conan2\p\proto47a676cb9257b\p\include -external:IC:\Users\<USER>\.conan2\p\zlibcfb9789dc2a53\p\include -external:IC:\Users\<USER>\.conan2\p\spdlo2d4b540ab0f22\p\include -external:IC:\Users\<USER>\.conan2\p\fmtdb20aad6e1bd4\p\include -external:IC:\Users\<USER>\.conan2\p\range0301bf3d76d5d\p\include -external:IC:\Users\<USER>\.conan2\p\b\clippf8ddcd4a5d961\p\include -external:IC:\Users\<USER>\.conan2\p\mapbo34ee9aeb574f9\p\include -external:IC:\Users\<USER>\.conan2\p\mapbofe72fb50cf7c4\p\include -external:IC:\Users\<USER>\.conan2\p\mapbo1b62b48748b82\p\include -external:IC:\Users\<USER>\.conan2\p\rapidf7a3355ba53c4\p\include -external:IC:\Users\<USER>\.conan2\p\stb6342cecb318f5\p\include -external:IC:\Users\<USER>\.conan2\p\boosta79d738920bc3\p\include -external:IC:\Users\<USER>\.conan2\p\scrip4cfd014ad4660\p\include -external:IC:\Users\<USER>\.conan2\p\nearg388d58da7a54c\p\include -external:IC:\Users\<USER>\.conan2\p\b\curaebb245e64b5ac7\p\include -external:IC:\Users\<USER>\.conan2\p\asio-c31dc2a380bf0\p\include -external:IC:\Users\<USER>\.conan2\p\grpc8b6c73b4e5756\p\include -external:IC:\Users\<USER>\.conan2\p\absei6a15444d60d47\p\include -external:W0
  OBJECT_DIR = CMakeFiles\_CuraEngine.dir
  OBJECT_FILE_DIR = CMakeFiles\_CuraEngine.dir\src\utils
  TARGET_COMPILE_PDB = CMakeFiles\_CuraEngine.dir\_CuraEngine.pdb
  TARGET_PDB = _CuraEngine.pdb

build CMakeFiles\_CuraEngine.dir\src\utils\SquareGrid.cpp.obj: CXX_COMPILER___CuraEngine_unscanned_Release C$:\Users\wsd07\vscode\Cura-Dev\CuraEngine\src\utils\SquareGrid.cpp || cmake_object_order_depends_target__CuraEngine
  CONFIG = Release
  DEFINES = -DAGRPC_BOOST_ASIO -DARCUS -DBOOST_ALL_NO_LIB -DBOOST_STACKTRACE_USE_NOOP -DBOOST_STACKTRACE_USE_WINDBG -DBOOST_STACKTRACE_USE_WINDBG_CACHED -DCARES_STATICLIB -DCURA_ENGINE_HASH=\"unknown\" -DCURA_ENGINE_VERSION=\"5.11.0-alpha.0\" -DENABLE_PLUGINS -DNOMINMAX -DSENTRY_ENVIRONMENT=\"development\" -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSTB_TEXTEDIT_KEYTYPE=unsigned
  FLAGS = /DWIN32 /D_WINDOWS /EHsc /O2 /Ob2 /DNDEBUG -std:c++20 -MD /utf-8 /permissive-
  INCLUDES = -IC:\Users\<USER>\vscode\Cura-Dev\CuraEngine\include -IC:\Users\<USER>\vscode\Cura-Dev\CuraEngine\build\Release -external:IC:\Users\<USER>\.conan2\p\b\arcusee7666f716199\p\include -external:IC:\Users\<USER>\.conan2\p\proto47a676cb9257b\p\include -external:IC:\Users\<USER>\.conan2\p\zlibcfb9789dc2a53\p\include -external:IC:\Users\<USER>\.conan2\p\spdlo2d4b540ab0f22\p\include -external:IC:\Users\<USER>\.conan2\p\fmtdb20aad6e1bd4\p\include -external:IC:\Users\<USER>\.conan2\p\range0301bf3d76d5d\p\include -external:IC:\Users\<USER>\.conan2\p\b\clippf8ddcd4a5d961\p\include -external:IC:\Users\<USER>\.conan2\p\mapbo34ee9aeb574f9\p\include -external:IC:\Users\<USER>\.conan2\p\mapbofe72fb50cf7c4\p\include -external:IC:\Users\<USER>\.conan2\p\mapbo1b62b48748b82\p\include -external:IC:\Users\<USER>\.conan2\p\rapidf7a3355ba53c4\p\include -external:IC:\Users\<USER>\.conan2\p\stb6342cecb318f5\p\include -external:IC:\Users\<USER>\.conan2\p\boosta79d738920bc3\p\include -external:IC:\Users\<USER>\.conan2\p\scrip4cfd014ad4660\p\include -external:IC:\Users\<USER>\.conan2\p\nearg388d58da7a54c\p\include -external:IC:\Users\<USER>\.conan2\p\b\curaebb245e64b5ac7\p\include -external:IC:\Users\<USER>\.conan2\p\asio-c31dc2a380bf0\p\include -external:IC:\Users\<USER>\.conan2\p\grpc8b6c73b4e5756\p\include -external:IC:\Users\<USER>\.conan2\p\absei6a15444d60d47\p\include -external:W0
  OBJECT_DIR = CMakeFiles\_CuraEngine.dir
  OBJECT_FILE_DIR = CMakeFiles\_CuraEngine.dir\src\utils
  TARGET_COMPILE_PDB = CMakeFiles\_CuraEngine.dir\_CuraEngine.pdb
  TARGET_PDB = _CuraEngine.pdb

build CMakeFiles\_CuraEngine.dir\src\utils\ThreadPool.cpp.obj: CXX_COMPILER___CuraEngine_unscanned_Release C$:\Users\wsd07\vscode\Cura-Dev\CuraEngine\src\utils\ThreadPool.cpp || cmake_object_order_depends_target__CuraEngine
  CONFIG = Release
  DEFINES = -DAGRPC_BOOST_ASIO -DARCUS -DBOOST_ALL_NO_LIB -DBOOST_STACKTRACE_USE_NOOP -DBOOST_STACKTRACE_USE_WINDBG -DBOOST_STACKTRACE_USE_WINDBG_CACHED -DCARES_STATICLIB -DCURA_ENGINE_HASH=\"unknown\" -DCURA_ENGINE_VERSION=\"5.11.0-alpha.0\" -DENABLE_PLUGINS -DNOMINMAX -DSENTRY_ENVIRONMENT=\"development\" -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSTB_TEXTEDIT_KEYTYPE=unsigned
  FLAGS = /DWIN32 /D_WINDOWS /EHsc /O2 /Ob2 /DNDEBUG -std:c++20 -MD /utf-8 /permissive-
  INCLUDES = -IC:\Users\<USER>\vscode\Cura-Dev\CuraEngine\include -IC:\Users\<USER>\vscode\Cura-Dev\CuraEngine\build\Release -external:IC:\Users\<USER>\.conan2\p\b\arcusee7666f716199\p\include -external:IC:\Users\<USER>\.conan2\p\proto47a676cb9257b\p\include -external:IC:\Users\<USER>\.conan2\p\zlibcfb9789dc2a53\p\include -external:IC:\Users\<USER>\.conan2\p\spdlo2d4b540ab0f22\p\include -external:IC:\Users\<USER>\.conan2\p\fmtdb20aad6e1bd4\p\include -external:IC:\Users\<USER>\.conan2\p\range0301bf3d76d5d\p\include -external:IC:\Users\<USER>\.conan2\p\b\clippf8ddcd4a5d961\p\include -external:IC:\Users\<USER>\.conan2\p\mapbo34ee9aeb574f9\p\include -external:IC:\Users\<USER>\.conan2\p\mapbofe72fb50cf7c4\p\include -external:IC:\Users\<USER>\.conan2\p\mapbo1b62b48748b82\p\include -external:IC:\Users\<USER>\.conan2\p\rapidf7a3355ba53c4\p\include -external:IC:\Users\<USER>\.conan2\p\stb6342cecb318f5\p\include -external:IC:\Users\<USER>\.conan2\p\boosta79d738920bc3\p\include -external:IC:\Users\<USER>\.conan2\p\scrip4cfd014ad4660\p\include -external:IC:\Users\<USER>\.conan2\p\nearg388d58da7a54c\p\include -external:IC:\Users\<USER>\.conan2\p\b\curaebb245e64b5ac7\p\include -external:IC:\Users\<USER>\.conan2\p\asio-c31dc2a380bf0\p\include -external:IC:\Users\<USER>\.conan2\p\grpc8b6c73b4e5756\p\include -external:IC:\Users\<USER>\.conan2\p\absei6a15444d60d47\p\include -external:W0
  OBJECT_DIR = CMakeFiles\_CuraEngine.dir
  OBJECT_FILE_DIR = CMakeFiles\_CuraEngine.dir\src\utils
  TARGET_COMPILE_PDB = CMakeFiles\_CuraEngine.dir\_CuraEngine.pdb
  TARGET_PDB = _CuraEngine.pdb

build CMakeFiles\_CuraEngine.dir\src\utils\ToolpathVisualizer.cpp.obj: CXX_COMPILER___CuraEngine_unscanned_Release C$:\Users\wsd07\vscode\Cura-Dev\CuraEngine\src\utils\ToolpathVisualizer.cpp || cmake_object_order_depends_target__CuraEngine
  CONFIG = Release
  DEFINES = -DAGRPC_BOOST_ASIO -DARCUS -DBOOST_ALL_NO_LIB -DBOOST_STACKTRACE_USE_NOOP -DBOOST_STACKTRACE_USE_WINDBG -DBOOST_STACKTRACE_USE_WINDBG_CACHED -DCARES_STATICLIB -DCURA_ENGINE_HASH=\"unknown\" -DCURA_ENGINE_VERSION=\"5.11.0-alpha.0\" -DENABLE_PLUGINS -DNOMINMAX -DSENTRY_ENVIRONMENT=\"development\" -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSTB_TEXTEDIT_KEYTYPE=unsigned
  FLAGS = /DWIN32 /D_WINDOWS /EHsc /O2 /Ob2 /DNDEBUG -std:c++20 -MD /utf-8 /permissive-
  INCLUDES = -IC:\Users\<USER>\vscode\Cura-Dev\CuraEngine\include -IC:\Users\<USER>\vscode\Cura-Dev\CuraEngine\build\Release -external:IC:\Users\<USER>\.conan2\p\b\arcusee7666f716199\p\include -external:IC:\Users\<USER>\.conan2\p\proto47a676cb9257b\p\include -external:IC:\Users\<USER>\.conan2\p\zlibcfb9789dc2a53\p\include -external:IC:\Users\<USER>\.conan2\p\spdlo2d4b540ab0f22\p\include -external:IC:\Users\<USER>\.conan2\p\fmtdb20aad6e1bd4\p\include -external:IC:\Users\<USER>\.conan2\p\range0301bf3d76d5d\p\include -external:IC:\Users\<USER>\.conan2\p\b\clippf8ddcd4a5d961\p\include -external:IC:\Users\<USER>\.conan2\p\mapbo34ee9aeb574f9\p\include -external:IC:\Users\<USER>\.conan2\p\mapbofe72fb50cf7c4\p\include -external:IC:\Users\<USER>\.conan2\p\mapbo1b62b48748b82\p\include -external:IC:\Users\<USER>\.conan2\p\rapidf7a3355ba53c4\p\include -external:IC:\Users\<USER>\.conan2\p\stb6342cecb318f5\p\include -external:IC:\Users\<USER>\.conan2\p\boosta79d738920bc3\p\include -external:IC:\Users\<USER>\.conan2\p\scrip4cfd014ad4660\p\include -external:IC:\Users\<USER>\.conan2\p\nearg388d58da7a54c\p\include -external:IC:\Users\<USER>\.conan2\p\b\curaebb245e64b5ac7\p\include -external:IC:\Users\<USER>\.conan2\p\asio-c31dc2a380bf0\p\include -external:IC:\Users\<USER>\.conan2\p\grpc8b6c73b4e5756\p\include -external:IC:\Users\<USER>\.conan2\p\absei6a15444d60d47\p\include -external:W0
  OBJECT_DIR = CMakeFiles\_CuraEngine.dir
  OBJECT_FILE_DIR = CMakeFiles\_CuraEngine.dir\src\utils
  TARGET_COMPILE_PDB = CMakeFiles\_CuraEngine.dir\_CuraEngine.pdb
  TARGET_PDB = _CuraEngine.pdb

build CMakeFiles\_CuraEngine.dir\src\utils\VoronoiUtils.cpp.obj: CXX_COMPILER___CuraEngine_unscanned_Release C$:\Users\wsd07\vscode\Cura-Dev\CuraEngine\src\utils\VoronoiUtils.cpp || cmake_object_order_depends_target__CuraEngine
  CONFIG = Release
  DEFINES = -DAGRPC_BOOST_ASIO -DARCUS -DBOOST_ALL_NO_LIB -DBOOST_STACKTRACE_USE_NOOP -DBOOST_STACKTRACE_USE_WINDBG -DBOOST_STACKTRACE_USE_WINDBG_CACHED -DCARES_STATICLIB -DCURA_ENGINE_HASH=\"unknown\" -DCURA_ENGINE_VERSION=\"5.11.0-alpha.0\" -DENABLE_PLUGINS -DNOMINMAX -DSENTRY_ENVIRONMENT=\"development\" -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSTB_TEXTEDIT_KEYTYPE=unsigned
  FLAGS = /DWIN32 /D_WINDOWS /EHsc /O2 /Ob2 /DNDEBUG -std:c++20 -MD /utf-8 /permissive-
  INCLUDES = -IC:\Users\<USER>\vscode\Cura-Dev\CuraEngine\include -IC:\Users\<USER>\vscode\Cura-Dev\CuraEngine\build\Release -external:IC:\Users\<USER>\.conan2\p\b\arcusee7666f716199\p\include -external:IC:\Users\<USER>\.conan2\p\proto47a676cb9257b\p\include -external:IC:\Users\<USER>\.conan2\p\zlibcfb9789dc2a53\p\include -external:IC:\Users\<USER>\.conan2\p\spdlo2d4b540ab0f22\p\include -external:IC:\Users\<USER>\.conan2\p\fmtdb20aad6e1bd4\p\include -external:IC:\Users\<USER>\.conan2\p\range0301bf3d76d5d\p\include -external:IC:\Users\<USER>\.conan2\p\b\clippf8ddcd4a5d961\p\include -external:IC:\Users\<USER>\.conan2\p\mapbo34ee9aeb574f9\p\include -external:IC:\Users\<USER>\.conan2\p\mapbofe72fb50cf7c4\p\include -external:IC:\Users\<USER>\.conan2\p\mapbo1b62b48748b82\p\include -external:IC:\Users\<USER>\.conan2\p\rapidf7a3355ba53c4\p\include -external:IC:\Users\<USER>\.conan2\p\stb6342cecb318f5\p\include -external:IC:\Users\<USER>\.conan2\p\boosta79d738920bc3\p\include -external:IC:\Users\<USER>\.conan2\p\scrip4cfd014ad4660\p\include -external:IC:\Users\<USER>\.conan2\p\nearg388d58da7a54c\p\include -external:IC:\Users\<USER>\.conan2\p\b\curaebb245e64b5ac7\p\include -external:IC:\Users\<USER>\.conan2\p\asio-c31dc2a380bf0\p\include -external:IC:\Users\<USER>\.conan2\p\grpc8b6c73b4e5756\p\include -external:IC:\Users\<USER>\.conan2\p\absei6a15444d60d47\p\include -external:W0
  OBJECT_DIR = CMakeFiles\_CuraEngine.dir
  OBJECT_FILE_DIR = CMakeFiles\_CuraEngine.dir\src\utils
  TARGET_COMPILE_PDB = CMakeFiles\_CuraEngine.dir\_CuraEngine.pdb
  TARGET_PDB = _CuraEngine.pdb

build CMakeFiles\_CuraEngine.dir\src\utils\VoxelUtils.cpp.obj: CXX_COMPILER___CuraEngine_unscanned_Release C$:\Users\wsd07\vscode\Cura-Dev\CuraEngine\src\utils\VoxelUtils.cpp || cmake_object_order_depends_target__CuraEngine
  CONFIG = Release
  DEFINES = -DAGRPC_BOOST_ASIO -DARCUS -DBOOST_ALL_NO_LIB -DBOOST_STACKTRACE_USE_NOOP -DBOOST_STACKTRACE_USE_WINDBG -DBOOST_STACKTRACE_USE_WINDBG_CACHED -DCARES_STATICLIB -DCURA_ENGINE_HASH=\"unknown\" -DCURA_ENGINE_VERSION=\"5.11.0-alpha.0\" -DENABLE_PLUGINS -DNOMINMAX -DSENTRY_ENVIRONMENT=\"development\" -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSTB_TEXTEDIT_KEYTYPE=unsigned
  FLAGS = /DWIN32 /D_WINDOWS /EHsc /O2 /Ob2 /DNDEBUG -std:c++20 -MD /utf-8 /permissive-
  INCLUDES = -IC:\Users\<USER>\vscode\Cura-Dev\CuraEngine\include -IC:\Users\<USER>\vscode\Cura-Dev\CuraEngine\build\Release -external:IC:\Users\<USER>\.conan2\p\b\arcusee7666f716199\p\include -external:IC:\Users\<USER>\.conan2\p\proto47a676cb9257b\p\include -external:IC:\Users\<USER>\.conan2\p\zlibcfb9789dc2a53\p\include -external:IC:\Users\<USER>\.conan2\p\spdlo2d4b540ab0f22\p\include -external:IC:\Users\<USER>\.conan2\p\fmtdb20aad6e1bd4\p\include -external:IC:\Users\<USER>\.conan2\p\range0301bf3d76d5d\p\include -external:IC:\Users\<USER>\.conan2\p\b\clippf8ddcd4a5d961\p\include -external:IC:\Users\<USER>\.conan2\p\mapbo34ee9aeb574f9\p\include -external:IC:\Users\<USER>\.conan2\p\mapbofe72fb50cf7c4\p\include -external:IC:\Users\<USER>\.conan2\p\mapbo1b62b48748b82\p\include -external:IC:\Users\<USER>\.conan2\p\rapidf7a3355ba53c4\p\include -external:IC:\Users\<USER>\.conan2\p\stb6342cecb318f5\p\include -external:IC:\Users\<USER>\.conan2\p\boosta79d738920bc3\p\include -external:IC:\Users\<USER>\.conan2\p\scrip4cfd014ad4660\p\include -external:IC:\Users\<USER>\.conan2\p\nearg388d58da7a54c\p\include -external:IC:\Users\<USER>\.conan2\p\b\curaebb245e64b5ac7\p\include -external:IC:\Users\<USER>\.conan2\p\asio-c31dc2a380bf0\p\include -external:IC:\Users\<USER>\.conan2\p\grpc8b6c73b4e5756\p\include -external:IC:\Users\<USER>\.conan2\p\absei6a15444d60d47\p\include -external:W0
  OBJECT_DIR = CMakeFiles\_CuraEngine.dir
  OBJECT_FILE_DIR = CMakeFiles\_CuraEngine.dir\src\utils
  TARGET_COMPILE_PDB = CMakeFiles\_CuraEngine.dir\_CuraEngine.pdb
  TARGET_PDB = _CuraEngine.pdb

build CMakeFiles\_CuraEngine.dir\src\utils\MixedPolylineStitcher.cpp.obj: CXX_COMPILER___CuraEngine_unscanned_Release C$:\Users\wsd07\vscode\Cura-Dev\CuraEngine\src\utils\MixedPolylineStitcher.cpp || cmake_object_order_depends_target__CuraEngine
  CONFIG = Release
  DEFINES = -DAGRPC_BOOST_ASIO -DARCUS -DBOOST_ALL_NO_LIB -DBOOST_STACKTRACE_USE_NOOP -DBOOST_STACKTRACE_USE_WINDBG -DBOOST_STACKTRACE_USE_WINDBG_CACHED -DCARES_STATICLIB -DCURA_ENGINE_HASH=\"unknown\" -DCURA_ENGINE_VERSION=\"5.11.0-alpha.0\" -DENABLE_PLUGINS -DNOMINMAX -DSENTRY_ENVIRONMENT=\"development\" -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSTB_TEXTEDIT_KEYTYPE=unsigned
  FLAGS = /DWIN32 /D_WINDOWS /EHsc /O2 /Ob2 /DNDEBUG -std:c++20 -MD /utf-8 /permissive-
  INCLUDES = -IC:\Users\<USER>\vscode\Cura-Dev\CuraEngine\include -IC:\Users\<USER>\vscode\Cura-Dev\CuraEngine\build\Release -external:IC:\Users\<USER>\.conan2\p\b\arcusee7666f716199\p\include -external:IC:\Users\<USER>\.conan2\p\proto47a676cb9257b\p\include -external:IC:\Users\<USER>\.conan2\p\zlibcfb9789dc2a53\p\include -external:IC:\Users\<USER>\.conan2\p\spdlo2d4b540ab0f22\p\include -external:IC:\Users\<USER>\.conan2\p\fmtdb20aad6e1bd4\p\include -external:IC:\Users\<USER>\.conan2\p\range0301bf3d76d5d\p\include -external:IC:\Users\<USER>\.conan2\p\b\clippf8ddcd4a5d961\p\include -external:IC:\Users\<USER>\.conan2\p\mapbo34ee9aeb574f9\p\include -external:IC:\Users\<USER>\.conan2\p\mapbofe72fb50cf7c4\p\include -external:IC:\Users\<USER>\.conan2\p\mapbo1b62b48748b82\p\include -external:IC:\Users\<USER>\.conan2\p\rapidf7a3355ba53c4\p\include -external:IC:\Users\<USER>\.conan2\p\stb6342cecb318f5\p\include -external:IC:\Users\<USER>\.conan2\p\boosta79d738920bc3\p\include -external:IC:\Users\<USER>\.conan2\p\scrip4cfd014ad4660\p\include -external:IC:\Users\<USER>\.conan2\p\nearg388d58da7a54c\p\include -external:IC:\Users\<USER>\.conan2\p\b\curaebb245e64b5ac7\p\include -external:IC:\Users\<USER>\.conan2\p\asio-c31dc2a380bf0\p\include -external:IC:\Users\<USER>\.conan2\p\grpc8b6c73b4e5756\p\include -external:IC:\Users\<USER>\.conan2\p\absei6a15444d60d47\p\include -external:W0
  OBJECT_DIR = CMakeFiles\_CuraEngine.dir
  OBJECT_FILE_DIR = CMakeFiles\_CuraEngine.dir\src\utils
  TARGET_COMPILE_PDB = CMakeFiles\_CuraEngine.dir\_CuraEngine.pdb
  TARGET_PDB = _CuraEngine.pdb

build CMakeFiles\_CuraEngine.dir\src\utils\scoring\BestElementFinder.cpp.obj: CXX_COMPILER___CuraEngine_unscanned_Release C$:\Users\wsd07\vscode\Cura-Dev\CuraEngine\src\utils\scoring\BestElementFinder.cpp || cmake_object_order_depends_target__CuraEngine
  CONFIG = Release
  DEFINES = -DAGRPC_BOOST_ASIO -DARCUS -DBOOST_ALL_NO_LIB -DBOOST_STACKTRACE_USE_NOOP -DBOOST_STACKTRACE_USE_WINDBG -DBOOST_STACKTRACE_USE_WINDBG_CACHED -DCARES_STATICLIB -DCURA_ENGINE_HASH=\"unknown\" -DCURA_ENGINE_VERSION=\"5.11.0-alpha.0\" -DENABLE_PLUGINS -DNOMINMAX -DSENTRY_ENVIRONMENT=\"development\" -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSTB_TEXTEDIT_KEYTYPE=unsigned
  FLAGS = /DWIN32 /D_WINDOWS /EHsc /O2 /Ob2 /DNDEBUG -std:c++20 -MD /utf-8 /permissive-
  INCLUDES = -IC:\Users\<USER>\vscode\Cura-Dev\CuraEngine\include -IC:\Users\<USER>\vscode\Cura-Dev\CuraEngine\build\Release -external:IC:\Users\<USER>\.conan2\p\b\arcusee7666f716199\p\include -external:IC:\Users\<USER>\.conan2\p\proto47a676cb9257b\p\include -external:IC:\Users\<USER>\.conan2\p\zlibcfb9789dc2a53\p\include -external:IC:\Users\<USER>\.conan2\p\spdlo2d4b540ab0f22\p\include -external:IC:\Users\<USER>\.conan2\p\fmtdb20aad6e1bd4\p\include -external:IC:\Users\<USER>\.conan2\p\range0301bf3d76d5d\p\include -external:IC:\Users\<USER>\.conan2\p\b\clippf8ddcd4a5d961\p\include -external:IC:\Users\<USER>\.conan2\p\mapbo34ee9aeb574f9\p\include -external:IC:\Users\<USER>\.conan2\p\mapbofe72fb50cf7c4\p\include -external:IC:\Users\<USER>\.conan2\p\mapbo1b62b48748b82\p\include -external:IC:\Users\<USER>\.conan2\p\rapidf7a3355ba53c4\p\include -external:IC:\Users\<USER>\.conan2\p\stb6342cecb318f5\p\include -external:IC:\Users\<USER>\.conan2\p\boosta79d738920bc3\p\include -external:IC:\Users\<USER>\.conan2\p\scrip4cfd014ad4660\p\include -external:IC:\Users\<USER>\.conan2\p\nearg388d58da7a54c\p\include -external:IC:\Users\<USER>\.conan2\p\b\curaebb245e64b5ac7\p\include -external:IC:\Users\<USER>\.conan2\p\asio-c31dc2a380bf0\p\include -external:IC:\Users\<USER>\.conan2\p\grpc8b6c73b4e5756\p\include -external:IC:\Users\<USER>\.conan2\p\absei6a15444d60d47\p\include -external:W0
  OBJECT_DIR = CMakeFiles\_CuraEngine.dir
  OBJECT_FILE_DIR = CMakeFiles\_CuraEngine.dir\src\utils\scoring
  TARGET_COMPILE_PDB = CMakeFiles\_CuraEngine.dir\_CuraEngine.pdb
  TARGET_PDB = _CuraEngine.pdb

build CMakeFiles\_CuraEngine.dir\src\utils\scoring\CornerScoringCriterion.cpp.obj: CXX_COMPILER___CuraEngine_unscanned_Release C$:\Users\wsd07\vscode\Cura-Dev\CuraEngine\src\utils\scoring\CornerScoringCriterion.cpp || cmake_object_order_depends_target__CuraEngine
  CONFIG = Release
  DEFINES = -DAGRPC_BOOST_ASIO -DARCUS -DBOOST_ALL_NO_LIB -DBOOST_STACKTRACE_USE_NOOP -DBOOST_STACKTRACE_USE_WINDBG -DBOOST_STACKTRACE_USE_WINDBG_CACHED -DCARES_STATICLIB -DCURA_ENGINE_HASH=\"unknown\" -DCURA_ENGINE_VERSION=\"5.11.0-alpha.0\" -DENABLE_PLUGINS -DNOMINMAX -DSENTRY_ENVIRONMENT=\"development\" -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSTB_TEXTEDIT_KEYTYPE=unsigned
  FLAGS = /DWIN32 /D_WINDOWS /EHsc /O2 /Ob2 /DNDEBUG -std:c++20 -MD /utf-8 /permissive-
  INCLUDES = -IC:\Users\<USER>\vscode\Cura-Dev\CuraEngine\include -IC:\Users\<USER>\vscode\Cura-Dev\CuraEngine\build\Release -external:IC:\Users\<USER>\.conan2\p\b\arcusee7666f716199\p\include -external:IC:\Users\<USER>\.conan2\p\proto47a676cb9257b\p\include -external:IC:\Users\<USER>\.conan2\p\zlibcfb9789dc2a53\p\include -external:IC:\Users\<USER>\.conan2\p\spdlo2d4b540ab0f22\p\include -external:IC:\Users\<USER>\.conan2\p\fmtdb20aad6e1bd4\p\include -external:IC:\Users\<USER>\.conan2\p\range0301bf3d76d5d\p\include -external:IC:\Users\<USER>\.conan2\p\b\clippf8ddcd4a5d961\p\include -external:IC:\Users\<USER>\.conan2\p\mapbo34ee9aeb574f9\p\include -external:IC:\Users\<USER>\.conan2\p\mapbofe72fb50cf7c4\p\include -external:IC:\Users\<USER>\.conan2\p\mapbo1b62b48748b82\p\include -external:IC:\Users\<USER>\.conan2\p\rapidf7a3355ba53c4\p\include -external:IC:\Users\<USER>\.conan2\p\stb6342cecb318f5\p\include -external:IC:\Users\<USER>\.conan2\p\boosta79d738920bc3\p\include -external:IC:\Users\<USER>\.conan2\p\scrip4cfd014ad4660\p\include -external:IC:\Users\<USER>\.conan2\p\nearg388d58da7a54c\p\include -external:IC:\Users\<USER>\.conan2\p\b\curaebb245e64b5ac7\p\include -external:IC:\Users\<USER>\.conan2\p\asio-c31dc2a380bf0\p\include -external:IC:\Users\<USER>\.conan2\p\grpc8b6c73b4e5756\p\include -external:IC:\Users\<USER>\.conan2\p\absei6a15444d60d47\p\include -external:W0
  OBJECT_DIR = CMakeFiles\_CuraEngine.dir
  OBJECT_FILE_DIR = CMakeFiles\_CuraEngine.dir\src\utils\scoring
  TARGET_COMPILE_PDB = CMakeFiles\_CuraEngine.dir\_CuraEngine.pdb
  TARGET_PDB = _CuraEngine.pdb

build CMakeFiles\_CuraEngine.dir\src\utils\scoring\DistanceScoringCriterion.cpp.obj: CXX_COMPILER___CuraEngine_unscanned_Release C$:\Users\wsd07\vscode\Cura-Dev\CuraEngine\src\utils\scoring\DistanceScoringCriterion.cpp || cmake_object_order_depends_target__CuraEngine
  CONFIG = Release
  DEFINES = -DAGRPC_BOOST_ASIO -DARCUS -DBOOST_ALL_NO_LIB -DBOOST_STACKTRACE_USE_NOOP -DBOOST_STACKTRACE_USE_WINDBG -DBOOST_STACKTRACE_USE_WINDBG_CACHED -DCARES_STATICLIB -DCURA_ENGINE_HASH=\"unknown\" -DCURA_ENGINE_VERSION=\"5.11.0-alpha.0\" -DENABLE_PLUGINS -DNOMINMAX -DSENTRY_ENVIRONMENT=\"development\" -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSTB_TEXTEDIT_KEYTYPE=unsigned
  FLAGS = /DWIN32 /D_WINDOWS /EHsc /O2 /Ob2 /DNDEBUG -std:c++20 -MD /utf-8 /permissive-
  INCLUDES = -IC:\Users\<USER>\vscode\Cura-Dev\CuraEngine\include -IC:\Users\<USER>\vscode\Cura-Dev\CuraEngine\build\Release -external:IC:\Users\<USER>\.conan2\p\b\arcusee7666f716199\p\include -external:IC:\Users\<USER>\.conan2\p\proto47a676cb9257b\p\include -external:IC:\Users\<USER>\.conan2\p\zlibcfb9789dc2a53\p\include -external:IC:\Users\<USER>\.conan2\p\spdlo2d4b540ab0f22\p\include -external:IC:\Users\<USER>\.conan2\p\fmtdb20aad6e1bd4\p\include -external:IC:\Users\<USER>\.conan2\p\range0301bf3d76d5d\p\include -external:IC:\Users\<USER>\.conan2\p\b\clippf8ddcd4a5d961\p\include -external:IC:\Users\<USER>\.conan2\p\mapbo34ee9aeb574f9\p\include -external:IC:\Users\<USER>\.conan2\p\mapbofe72fb50cf7c4\p\include -external:IC:\Users\<USER>\.conan2\p\mapbo1b62b48748b82\p\include -external:IC:\Users\<USER>\.conan2\p\rapidf7a3355ba53c4\p\include -external:IC:\Users\<USER>\.conan2\p\stb6342cecb318f5\p\include -external:IC:\Users\<USER>\.conan2\p\boosta79d738920bc3\p\include -external:IC:\Users\<USER>\.conan2\p\scrip4cfd014ad4660\p\include -external:IC:\Users\<USER>\.conan2\p\nearg388d58da7a54c\p\include -external:IC:\Users\<USER>\.conan2\p\b\curaebb245e64b5ac7\p\include -external:IC:\Users\<USER>\.conan2\p\asio-c31dc2a380bf0\p\include -external:IC:\Users\<USER>\.conan2\p\grpc8b6c73b4e5756\p\include -external:IC:\Users\<USER>\.conan2\p\absei6a15444d60d47\p\include -external:W0
  OBJECT_DIR = CMakeFiles\_CuraEngine.dir
  OBJECT_FILE_DIR = CMakeFiles\_CuraEngine.dir\src\utils\scoring
  TARGET_COMPILE_PDB = CMakeFiles\_CuraEngine.dir\_CuraEngine.pdb
  TARGET_PDB = _CuraEngine.pdb

build CMakeFiles\_CuraEngine.dir\src\utils\scoring\ExclusionAreaScoringCriterion.cpp.obj: CXX_COMPILER___CuraEngine_unscanned_Release C$:\Users\wsd07\vscode\Cura-Dev\CuraEngine\src\utils\scoring\ExclusionAreaScoringCriterion.cpp || cmake_object_order_depends_target__CuraEngine
  CONFIG = Release
  DEFINES = -DAGRPC_BOOST_ASIO -DARCUS -DBOOST_ALL_NO_LIB -DBOOST_STACKTRACE_USE_NOOP -DBOOST_STACKTRACE_USE_WINDBG -DBOOST_STACKTRACE_USE_WINDBG_CACHED -DCARES_STATICLIB -DCURA_ENGINE_HASH=\"unknown\" -DCURA_ENGINE_VERSION=\"5.11.0-alpha.0\" -DENABLE_PLUGINS -DNOMINMAX -DSENTRY_ENVIRONMENT=\"development\" -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSTB_TEXTEDIT_KEYTYPE=unsigned
  FLAGS = /DWIN32 /D_WINDOWS /EHsc /O2 /Ob2 /DNDEBUG -std:c++20 -MD /utf-8 /permissive-
  INCLUDES = -IC:\Users\<USER>\vscode\Cura-Dev\CuraEngine\include -IC:\Users\<USER>\vscode\Cura-Dev\CuraEngine\build\Release -external:IC:\Users\<USER>\.conan2\p\b\arcusee7666f716199\p\include -external:IC:\Users\<USER>\.conan2\p\proto47a676cb9257b\p\include -external:IC:\Users\<USER>\.conan2\p\zlibcfb9789dc2a53\p\include -external:IC:\Users\<USER>\.conan2\p\spdlo2d4b540ab0f22\p\include -external:IC:\Users\<USER>\.conan2\p\fmtdb20aad6e1bd4\p\include -external:IC:\Users\<USER>\.conan2\p\range0301bf3d76d5d\p\include -external:IC:\Users\<USER>\.conan2\p\b\clippf8ddcd4a5d961\p\include -external:IC:\Users\<USER>\.conan2\p\mapbo34ee9aeb574f9\p\include -external:IC:\Users\<USER>\.conan2\p\mapbofe72fb50cf7c4\p\include -external:IC:\Users\<USER>\.conan2\p\mapbo1b62b48748b82\p\include -external:IC:\Users\<USER>\.conan2\p\rapidf7a3355ba53c4\p\include -external:IC:\Users\<USER>\.conan2\p\stb6342cecb318f5\p\include -external:IC:\Users\<USER>\.conan2\p\boosta79d738920bc3\p\include -external:IC:\Users\<USER>\.conan2\p\scrip4cfd014ad4660\p\include -external:IC:\Users\<USER>\.conan2\p\nearg388d58da7a54c\p\include -external:IC:\Users\<USER>\.conan2\p\b\curaebb245e64b5ac7\p\include -external:IC:\Users\<USER>\.conan2\p\asio-c31dc2a380bf0\p\include -external:IC:\Users\<USER>\.conan2\p\grpc8b6c73b4e5756\p\include -external:IC:\Users\<USER>\.conan2\p\absei6a15444d60d47\p\include -external:W0
  OBJECT_DIR = CMakeFiles\_CuraEngine.dir
  OBJECT_FILE_DIR = CMakeFiles\_CuraEngine.dir\src\utils\scoring
  TARGET_COMPILE_PDB = CMakeFiles\_CuraEngine.dir\_CuraEngine.pdb
  TARGET_PDB = _CuraEngine.pdb

build CMakeFiles\_CuraEngine.dir\src\utils\scoring\RandomScoringCriterion.cpp.obj: CXX_COMPILER___CuraEngine_unscanned_Release C$:\Users\wsd07\vscode\Cura-Dev\CuraEngine\src\utils\scoring\RandomScoringCriterion.cpp || cmake_object_order_depends_target__CuraEngine
  CONFIG = Release
  DEFINES = -DAGRPC_BOOST_ASIO -DARCUS -DBOOST_ALL_NO_LIB -DBOOST_STACKTRACE_USE_NOOP -DBOOST_STACKTRACE_USE_WINDBG -DBOOST_STACKTRACE_USE_WINDBG_CACHED -DCARES_STATICLIB -DCURA_ENGINE_HASH=\"unknown\" -DCURA_ENGINE_VERSION=\"5.11.0-alpha.0\" -DENABLE_PLUGINS -DNOMINMAX -DSENTRY_ENVIRONMENT=\"development\" -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSTB_TEXTEDIT_KEYTYPE=unsigned
  FLAGS = /DWIN32 /D_WINDOWS /EHsc /O2 /Ob2 /DNDEBUG -std:c++20 -MD /utf-8 /permissive-
  INCLUDES = -IC:\Users\<USER>\vscode\Cura-Dev\CuraEngine\include -IC:\Users\<USER>\vscode\Cura-Dev\CuraEngine\build\Release -external:IC:\Users\<USER>\.conan2\p\b\arcusee7666f716199\p\include -external:IC:\Users\<USER>\.conan2\p\proto47a676cb9257b\p\include -external:IC:\Users\<USER>\.conan2\p\zlibcfb9789dc2a53\p\include -external:IC:\Users\<USER>\.conan2\p\spdlo2d4b540ab0f22\p\include -external:IC:\Users\<USER>\.conan2\p\fmtdb20aad6e1bd4\p\include -external:IC:\Users\<USER>\.conan2\p\range0301bf3d76d5d\p\include -external:IC:\Users\<USER>\.conan2\p\b\clippf8ddcd4a5d961\p\include -external:IC:\Users\<USER>\.conan2\p\mapbo34ee9aeb574f9\p\include -external:IC:\Users\<USER>\.conan2\p\mapbofe72fb50cf7c4\p\include -external:IC:\Users\<USER>\.conan2\p\mapbo1b62b48748b82\p\include -external:IC:\Users\<USER>\.conan2\p\rapidf7a3355ba53c4\p\include -external:IC:\Users\<USER>\.conan2\p\stb6342cecb318f5\p\include -external:IC:\Users\<USER>\.conan2\p\boosta79d738920bc3\p\include -external:IC:\Users\<USER>\.conan2\p\scrip4cfd014ad4660\p\include -external:IC:\Users\<USER>\.conan2\p\nearg388d58da7a54c\p\include -external:IC:\Users\<USER>\.conan2\p\b\curaebb245e64b5ac7\p\include -external:IC:\Users\<USER>\.conan2\p\asio-c31dc2a380bf0\p\include -external:IC:\Users\<USER>\.conan2\p\grpc8b6c73b4e5756\p\include -external:IC:\Users\<USER>\.conan2\p\absei6a15444d60d47\p\include -external:W0
  OBJECT_DIR = CMakeFiles\_CuraEngine.dir
  OBJECT_FILE_DIR = CMakeFiles\_CuraEngine.dir\src\utils\scoring
  TARGET_COMPILE_PDB = CMakeFiles\_CuraEngine.dir\_CuraEngine.pdb
  TARGET_PDB = _CuraEngine.pdb

build CMakeFiles\_CuraEngine.dir\src\geometry\Point2LL.cpp.obj: CXX_COMPILER___CuraEngine_unscanned_Release C$:\Users\wsd07\vscode\Cura-Dev\CuraEngine\src\geometry\Point2LL.cpp || cmake_object_order_depends_target__CuraEngine
  CONFIG = Release
  DEFINES = -DAGRPC_BOOST_ASIO -DARCUS -DBOOST_ALL_NO_LIB -DBOOST_STACKTRACE_USE_NOOP -DBOOST_STACKTRACE_USE_WINDBG -DBOOST_STACKTRACE_USE_WINDBG_CACHED -DCARES_STATICLIB -DCURA_ENGINE_HASH=\"unknown\" -DCURA_ENGINE_VERSION=\"5.11.0-alpha.0\" -DENABLE_PLUGINS -DNOMINMAX -DSENTRY_ENVIRONMENT=\"development\" -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSTB_TEXTEDIT_KEYTYPE=unsigned
  FLAGS = /DWIN32 /D_WINDOWS /EHsc /O2 /Ob2 /DNDEBUG -std:c++20 -MD /utf-8 /permissive-
  INCLUDES = -IC:\Users\<USER>\vscode\Cura-Dev\CuraEngine\include -IC:\Users\<USER>\vscode\Cura-Dev\CuraEngine\build\Release -external:IC:\Users\<USER>\.conan2\p\b\arcusee7666f716199\p\include -external:IC:\Users\<USER>\.conan2\p\proto47a676cb9257b\p\include -external:IC:\Users\<USER>\.conan2\p\zlibcfb9789dc2a53\p\include -external:IC:\Users\<USER>\.conan2\p\spdlo2d4b540ab0f22\p\include -external:IC:\Users\<USER>\.conan2\p\fmtdb20aad6e1bd4\p\include -external:IC:\Users\<USER>\.conan2\p\range0301bf3d76d5d\p\include -external:IC:\Users\<USER>\.conan2\p\b\clippf8ddcd4a5d961\p\include -external:IC:\Users\<USER>\.conan2\p\mapbo34ee9aeb574f9\p\include -external:IC:\Users\<USER>\.conan2\p\mapbofe72fb50cf7c4\p\include -external:IC:\Users\<USER>\.conan2\p\mapbo1b62b48748b82\p\include -external:IC:\Users\<USER>\.conan2\p\rapidf7a3355ba53c4\p\include -external:IC:\Users\<USER>\.conan2\p\stb6342cecb318f5\p\include -external:IC:\Users\<USER>\.conan2\p\boosta79d738920bc3\p\include -external:IC:\Users\<USER>\.conan2\p\scrip4cfd014ad4660\p\include -external:IC:\Users\<USER>\.conan2\p\nearg388d58da7a54c\p\include -external:IC:\Users\<USER>\.conan2\p\b\curaebb245e64b5ac7\p\include -external:IC:\Users\<USER>\.conan2\p\asio-c31dc2a380bf0\p\include -external:IC:\Users\<USER>\.conan2\p\grpc8b6c73b4e5756\p\include -external:IC:\Users\<USER>\.conan2\p\absei6a15444d60d47\p\include -external:W0
  OBJECT_DIR = CMakeFiles\_CuraEngine.dir
  OBJECT_FILE_DIR = CMakeFiles\_CuraEngine.dir\src\geometry
  TARGET_COMPILE_PDB = CMakeFiles\_CuraEngine.dir\_CuraEngine.pdb
  TARGET_PDB = _CuraEngine.pdb

build CMakeFiles\_CuraEngine.dir\src\geometry\Point3LL.cpp.obj: CXX_COMPILER___CuraEngine_unscanned_Release C$:\Users\wsd07\vscode\Cura-Dev\CuraEngine\src\geometry\Point3LL.cpp || cmake_object_order_depends_target__CuraEngine
  CONFIG = Release
  DEFINES = -DAGRPC_BOOST_ASIO -DARCUS -DBOOST_ALL_NO_LIB -DBOOST_STACKTRACE_USE_NOOP -DBOOST_STACKTRACE_USE_WINDBG -DBOOST_STACKTRACE_USE_WINDBG_CACHED -DCARES_STATICLIB -DCURA_ENGINE_HASH=\"unknown\" -DCURA_ENGINE_VERSION=\"5.11.0-alpha.0\" -DENABLE_PLUGINS -DNOMINMAX -DSENTRY_ENVIRONMENT=\"development\" -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSTB_TEXTEDIT_KEYTYPE=unsigned
  FLAGS = /DWIN32 /D_WINDOWS /EHsc /O2 /Ob2 /DNDEBUG -std:c++20 -MD /utf-8 /permissive-
  INCLUDES = -IC:\Users\<USER>\vscode\Cura-Dev\CuraEngine\include -IC:\Users\<USER>\vscode\Cura-Dev\CuraEngine\build\Release -external:IC:\Users\<USER>\.conan2\p\b\arcusee7666f716199\p\include -external:IC:\Users\<USER>\.conan2\p\proto47a676cb9257b\p\include -external:IC:\Users\<USER>\.conan2\p\zlibcfb9789dc2a53\p\include -external:IC:\Users\<USER>\.conan2\p\spdlo2d4b540ab0f22\p\include -external:IC:\Users\<USER>\.conan2\p\fmtdb20aad6e1bd4\p\include -external:IC:\Users\<USER>\.conan2\p\range0301bf3d76d5d\p\include -external:IC:\Users\<USER>\.conan2\p\b\clippf8ddcd4a5d961\p\include -external:IC:\Users\<USER>\.conan2\p\mapbo34ee9aeb574f9\p\include -external:IC:\Users\<USER>\.conan2\p\mapbofe72fb50cf7c4\p\include -external:IC:\Users\<USER>\.conan2\p\mapbo1b62b48748b82\p\include -external:IC:\Users\<USER>\.conan2\p\rapidf7a3355ba53c4\p\include -external:IC:\Users\<USER>\.conan2\p\stb6342cecb318f5\p\include -external:IC:\Users\<USER>\.conan2\p\boosta79d738920bc3\p\include -external:IC:\Users\<USER>\.conan2\p\scrip4cfd014ad4660\p\include -external:IC:\Users\<USER>\.conan2\p\nearg388d58da7a54c\p\include -external:IC:\Users\<USER>\.conan2\p\b\curaebb245e64b5ac7\p\include -external:IC:\Users\<USER>\.conan2\p\asio-c31dc2a380bf0\p\include -external:IC:\Users\<USER>\.conan2\p\grpc8b6c73b4e5756\p\include -external:IC:\Users\<USER>\.conan2\p\absei6a15444d60d47\p\include -external:W0
  OBJECT_DIR = CMakeFiles\_CuraEngine.dir
  OBJECT_FILE_DIR = CMakeFiles\_CuraEngine.dir\src\geometry
  TARGET_COMPILE_PDB = CMakeFiles\_CuraEngine.dir\_CuraEngine.pdb
  TARGET_PDB = _CuraEngine.pdb

build CMakeFiles\_CuraEngine.dir\src\geometry\Polygon.cpp.obj: CXX_COMPILER___CuraEngine_unscanned_Release C$:\Users\wsd07\vscode\Cura-Dev\CuraEngine\src\geometry\Polygon.cpp || cmake_object_order_depends_target__CuraEngine
  CONFIG = Release
  DEFINES = -DAGRPC_BOOST_ASIO -DARCUS -DBOOST_ALL_NO_LIB -DBOOST_STACKTRACE_USE_NOOP -DBOOST_STACKTRACE_USE_WINDBG -DBOOST_STACKTRACE_USE_WINDBG_CACHED -DCARES_STATICLIB -DCURA_ENGINE_HASH=\"unknown\" -DCURA_ENGINE_VERSION=\"5.11.0-alpha.0\" -DENABLE_PLUGINS -DNOMINMAX -DSENTRY_ENVIRONMENT=\"development\" -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSTB_TEXTEDIT_KEYTYPE=unsigned
  FLAGS = /DWIN32 /D_WINDOWS /EHsc /O2 /Ob2 /DNDEBUG -std:c++20 -MD /utf-8 /permissive-
  INCLUDES = -IC:\Users\<USER>\vscode\Cura-Dev\CuraEngine\include -IC:\Users\<USER>\vscode\Cura-Dev\CuraEngine\build\Release -external:IC:\Users\<USER>\.conan2\p\b\arcusee7666f716199\p\include -external:IC:\Users\<USER>\.conan2\p\proto47a676cb9257b\p\include -external:IC:\Users\<USER>\.conan2\p\zlibcfb9789dc2a53\p\include -external:IC:\Users\<USER>\.conan2\p\spdlo2d4b540ab0f22\p\include -external:IC:\Users\<USER>\.conan2\p\fmtdb20aad6e1bd4\p\include -external:IC:\Users\<USER>\.conan2\p\range0301bf3d76d5d\p\include -external:IC:\Users\<USER>\.conan2\p\b\clippf8ddcd4a5d961\p\include -external:IC:\Users\<USER>\.conan2\p\mapbo34ee9aeb574f9\p\include -external:IC:\Users\<USER>\.conan2\p\mapbofe72fb50cf7c4\p\include -external:IC:\Users\<USER>\.conan2\p\mapbo1b62b48748b82\p\include -external:IC:\Users\<USER>\.conan2\p\rapidf7a3355ba53c4\p\include -external:IC:\Users\<USER>\.conan2\p\stb6342cecb318f5\p\include -external:IC:\Users\<USER>\.conan2\p\boosta79d738920bc3\p\include -external:IC:\Users\<USER>\.conan2\p\scrip4cfd014ad4660\p\include -external:IC:\Users\<USER>\.conan2\p\nearg388d58da7a54c\p\include -external:IC:\Users\<USER>\.conan2\p\b\curaebb245e64b5ac7\p\include -external:IC:\Users\<USER>\.conan2\p\asio-c31dc2a380bf0\p\include -external:IC:\Users\<USER>\.conan2\p\grpc8b6c73b4e5756\p\include -external:IC:\Users\<USER>\.conan2\p\absei6a15444d60d47\p\include -external:W0
  OBJECT_DIR = CMakeFiles\_CuraEngine.dir
  OBJECT_FILE_DIR = CMakeFiles\_CuraEngine.dir\src\geometry
  TARGET_COMPILE_PDB = CMakeFiles\_CuraEngine.dir\_CuraEngine.pdb
  TARGET_PDB = _CuraEngine.pdb

build CMakeFiles\_CuraEngine.dir\src\geometry\Shape.cpp.obj: CXX_COMPILER___CuraEngine_unscanned_Release C$:\Users\wsd07\vscode\Cura-Dev\CuraEngine\src\geometry\Shape.cpp || cmake_object_order_depends_target__CuraEngine
  CONFIG = Release
  DEFINES = -DAGRPC_BOOST_ASIO -DARCUS -DBOOST_ALL_NO_LIB -DBOOST_STACKTRACE_USE_NOOP -DBOOST_STACKTRACE_USE_WINDBG -DBOOST_STACKTRACE_USE_WINDBG_CACHED -DCARES_STATICLIB -DCURA_ENGINE_HASH=\"unknown\" -DCURA_ENGINE_VERSION=\"5.11.0-alpha.0\" -DENABLE_PLUGINS -DNOMINMAX -DSENTRY_ENVIRONMENT=\"development\" -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSTB_TEXTEDIT_KEYTYPE=unsigned
  FLAGS = /DWIN32 /D_WINDOWS /EHsc /O2 /Ob2 /DNDEBUG -std:c++20 -MD /utf-8 /permissive-
  INCLUDES = -IC:\Users\<USER>\vscode\Cura-Dev\CuraEngine\include -IC:\Users\<USER>\vscode\Cura-Dev\CuraEngine\build\Release -external:IC:\Users\<USER>\.conan2\p\b\arcusee7666f716199\p\include -external:IC:\Users\<USER>\.conan2\p\proto47a676cb9257b\p\include -external:IC:\Users\<USER>\.conan2\p\zlibcfb9789dc2a53\p\include -external:IC:\Users\<USER>\.conan2\p\spdlo2d4b540ab0f22\p\include -external:IC:\Users\<USER>\.conan2\p\fmtdb20aad6e1bd4\p\include -external:IC:\Users\<USER>\.conan2\p\range0301bf3d76d5d\p\include -external:IC:\Users\<USER>\.conan2\p\b\clippf8ddcd4a5d961\p\include -external:IC:\Users\<USER>\.conan2\p\mapbo34ee9aeb574f9\p\include -external:IC:\Users\<USER>\.conan2\p\mapbofe72fb50cf7c4\p\include -external:IC:\Users\<USER>\.conan2\p\mapbo1b62b48748b82\p\include -external:IC:\Users\<USER>\.conan2\p\rapidf7a3355ba53c4\p\include -external:IC:\Users\<USER>\.conan2\p\stb6342cecb318f5\p\include -external:IC:\Users\<USER>\.conan2\p\boosta79d738920bc3\p\include -external:IC:\Users\<USER>\.conan2\p\scrip4cfd014ad4660\p\include -external:IC:\Users\<USER>\.conan2\p\nearg388d58da7a54c\p\include -external:IC:\Users\<USER>\.conan2\p\b\curaebb245e64b5ac7\p\include -external:IC:\Users\<USER>\.conan2\p\asio-c31dc2a380bf0\p\include -external:IC:\Users\<USER>\.conan2\p\grpc8b6c73b4e5756\p\include -external:IC:\Users\<USER>\.conan2\p\absei6a15444d60d47\p\include -external:W0
  OBJECT_DIR = CMakeFiles\_CuraEngine.dir
  OBJECT_FILE_DIR = CMakeFiles\_CuraEngine.dir\src\geometry
  TARGET_COMPILE_PDB = CMakeFiles\_CuraEngine.dir\_CuraEngine.pdb
  TARGET_PDB = _CuraEngine.pdb

build CMakeFiles\_CuraEngine.dir\src\geometry\PointsSet.cpp.obj: CXX_COMPILER___CuraEngine_unscanned_Release C$:\Users\wsd07\vscode\Cura-Dev\CuraEngine\src\geometry\PointsSet.cpp || cmake_object_order_depends_target__CuraEngine
  CONFIG = Release
  DEFINES = -DAGRPC_BOOST_ASIO -DARCUS -DBOOST_ALL_NO_LIB -DBOOST_STACKTRACE_USE_NOOP -DBOOST_STACKTRACE_USE_WINDBG -DBOOST_STACKTRACE_USE_WINDBG_CACHED -DCARES_STATICLIB -DCURA_ENGINE_HASH=\"unknown\" -DCURA_ENGINE_VERSION=\"5.11.0-alpha.0\" -DENABLE_PLUGINS -DNOMINMAX -DSENTRY_ENVIRONMENT=\"development\" -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSTB_TEXTEDIT_KEYTYPE=unsigned
  FLAGS = /DWIN32 /D_WINDOWS /EHsc /O2 /Ob2 /DNDEBUG -std:c++20 -MD /utf-8 /permissive-
  INCLUDES = -IC:\Users\<USER>\vscode\Cura-Dev\CuraEngine\include -IC:\Users\<USER>\vscode\Cura-Dev\CuraEngine\build\Release -external:IC:\Users\<USER>\.conan2\p\b\arcusee7666f716199\p\include -external:IC:\Users\<USER>\.conan2\p\proto47a676cb9257b\p\include -external:IC:\Users\<USER>\.conan2\p\zlibcfb9789dc2a53\p\include -external:IC:\Users\<USER>\.conan2\p\spdlo2d4b540ab0f22\p\include -external:IC:\Users\<USER>\.conan2\p\fmtdb20aad6e1bd4\p\include -external:IC:\Users\<USER>\.conan2\p\range0301bf3d76d5d\p\include -external:IC:\Users\<USER>\.conan2\p\b\clippf8ddcd4a5d961\p\include -external:IC:\Users\<USER>\.conan2\p\mapbo34ee9aeb574f9\p\include -external:IC:\Users\<USER>\.conan2\p\mapbofe72fb50cf7c4\p\include -external:IC:\Users\<USER>\.conan2\p\mapbo1b62b48748b82\p\include -external:IC:\Users\<USER>\.conan2\p\rapidf7a3355ba53c4\p\include -external:IC:\Users\<USER>\.conan2\p\stb6342cecb318f5\p\include -external:IC:\Users\<USER>\.conan2\p\boosta79d738920bc3\p\include -external:IC:\Users\<USER>\.conan2\p\scrip4cfd014ad4660\p\include -external:IC:\Users\<USER>\.conan2\p\nearg388d58da7a54c\p\include -external:IC:\Users\<USER>\.conan2\p\b\curaebb245e64b5ac7\p\include -external:IC:\Users\<USER>\.conan2\p\asio-c31dc2a380bf0\p\include -external:IC:\Users\<USER>\.conan2\p\grpc8b6c73b4e5756\p\include -external:IC:\Users\<USER>\.conan2\p\absei6a15444d60d47\p\include -external:W0
  OBJECT_DIR = CMakeFiles\_CuraEngine.dir
  OBJECT_FILE_DIR = CMakeFiles\_CuraEngine.dir\src\geometry
  TARGET_COMPILE_PDB = CMakeFiles\_CuraEngine.dir\_CuraEngine.pdb
  TARGET_PDB = _CuraEngine.pdb

build CMakeFiles\_CuraEngine.dir\src\geometry\SingleShape.cpp.obj: CXX_COMPILER___CuraEngine_unscanned_Release C$:\Users\wsd07\vscode\Cura-Dev\CuraEngine\src\geometry\SingleShape.cpp || cmake_object_order_depends_target__CuraEngine
  CONFIG = Release
  DEFINES = -DAGRPC_BOOST_ASIO -DARCUS -DBOOST_ALL_NO_LIB -DBOOST_STACKTRACE_USE_NOOP -DBOOST_STACKTRACE_USE_WINDBG -DBOOST_STACKTRACE_USE_WINDBG_CACHED -DCARES_STATICLIB -DCURA_ENGINE_HASH=\"unknown\" -DCURA_ENGINE_VERSION=\"5.11.0-alpha.0\" -DENABLE_PLUGINS -DNOMINMAX -DSENTRY_ENVIRONMENT=\"development\" -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSTB_TEXTEDIT_KEYTYPE=unsigned
  FLAGS = /DWIN32 /D_WINDOWS /EHsc /O2 /Ob2 /DNDEBUG -std:c++20 -MD /utf-8 /permissive-
  INCLUDES = -IC:\Users\<USER>\vscode\Cura-Dev\CuraEngine\include -IC:\Users\<USER>\vscode\Cura-Dev\CuraEngine\build\Release -external:IC:\Users\<USER>\.conan2\p\b\arcusee7666f716199\p\include -external:IC:\Users\<USER>\.conan2\p\proto47a676cb9257b\p\include -external:IC:\Users\<USER>\.conan2\p\zlibcfb9789dc2a53\p\include -external:IC:\Users\<USER>\.conan2\p\spdlo2d4b540ab0f22\p\include -external:IC:\Users\<USER>\.conan2\p\fmtdb20aad6e1bd4\p\include -external:IC:\Users\<USER>\.conan2\p\range0301bf3d76d5d\p\include -external:IC:\Users\<USER>\.conan2\p\b\clippf8ddcd4a5d961\p\include -external:IC:\Users\<USER>\.conan2\p\mapbo34ee9aeb574f9\p\include -external:IC:\Users\<USER>\.conan2\p\mapbofe72fb50cf7c4\p\include -external:IC:\Users\<USER>\.conan2\p\mapbo1b62b48748b82\p\include -external:IC:\Users\<USER>\.conan2\p\rapidf7a3355ba53c4\p\include -external:IC:\Users\<USER>\.conan2\p\stb6342cecb318f5\p\include -external:IC:\Users\<USER>\.conan2\p\boosta79d738920bc3\p\include -external:IC:\Users\<USER>\.conan2\p\scrip4cfd014ad4660\p\include -external:IC:\Users\<USER>\.conan2\p\nearg388d58da7a54c\p\include -external:IC:\Users\<USER>\.conan2\p\b\curaebb245e64b5ac7\p\include -external:IC:\Users\<USER>\.conan2\p\asio-c31dc2a380bf0\p\include -external:IC:\Users\<USER>\.conan2\p\grpc8b6c73b4e5756\p\include -external:IC:\Users\<USER>\.conan2\p\absei6a15444d60d47\p\include -external:W0
  OBJECT_DIR = CMakeFiles\_CuraEngine.dir
  OBJECT_FILE_DIR = CMakeFiles\_CuraEngine.dir\src\geometry
  TARGET_COMPILE_PDB = CMakeFiles\_CuraEngine.dir\_CuraEngine.pdb
  TARGET_PDB = _CuraEngine.pdb

build CMakeFiles\_CuraEngine.dir\src\geometry\PartsView.cpp.obj: CXX_COMPILER___CuraEngine_unscanned_Release C$:\Users\wsd07\vscode\Cura-Dev\CuraEngine\src\geometry\PartsView.cpp || cmake_object_order_depends_target__CuraEngine
  CONFIG = Release
  DEFINES = -DAGRPC_BOOST_ASIO -DARCUS -DBOOST_ALL_NO_LIB -DBOOST_STACKTRACE_USE_NOOP -DBOOST_STACKTRACE_USE_WINDBG -DBOOST_STACKTRACE_USE_WINDBG_CACHED -DCARES_STATICLIB -DCURA_ENGINE_HASH=\"unknown\" -DCURA_ENGINE_VERSION=\"5.11.0-alpha.0\" -DENABLE_PLUGINS -DNOMINMAX -DSENTRY_ENVIRONMENT=\"development\" -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSTB_TEXTEDIT_KEYTYPE=unsigned
  FLAGS = /DWIN32 /D_WINDOWS /EHsc /O2 /Ob2 /DNDEBUG -std:c++20 -MD /utf-8 /permissive-
  INCLUDES = -IC:\Users\<USER>\vscode\Cura-Dev\CuraEngine\include -IC:\Users\<USER>\vscode\Cura-Dev\CuraEngine\build\Release -external:IC:\Users\<USER>\.conan2\p\b\arcusee7666f716199\p\include -external:IC:\Users\<USER>\.conan2\p\proto47a676cb9257b\p\include -external:IC:\Users\<USER>\.conan2\p\zlibcfb9789dc2a53\p\include -external:IC:\Users\<USER>\.conan2\p\spdlo2d4b540ab0f22\p\include -external:IC:\Users\<USER>\.conan2\p\fmtdb20aad6e1bd4\p\include -external:IC:\Users\<USER>\.conan2\p\range0301bf3d76d5d\p\include -external:IC:\Users\<USER>\.conan2\p\b\clippf8ddcd4a5d961\p\include -external:IC:\Users\<USER>\.conan2\p\mapbo34ee9aeb574f9\p\include -external:IC:\Users\<USER>\.conan2\p\mapbofe72fb50cf7c4\p\include -external:IC:\Users\<USER>\.conan2\p\mapbo1b62b48748b82\p\include -external:IC:\Users\<USER>\.conan2\p\rapidf7a3355ba53c4\p\include -external:IC:\Users\<USER>\.conan2\p\stb6342cecb318f5\p\include -external:IC:\Users\<USER>\.conan2\p\boosta79d738920bc3\p\include -external:IC:\Users\<USER>\.conan2\p\scrip4cfd014ad4660\p\include -external:IC:\Users\<USER>\.conan2\p\nearg388d58da7a54c\p\include -external:IC:\Users\<USER>\.conan2\p\b\curaebb245e64b5ac7\p\include -external:IC:\Users\<USER>\.conan2\p\asio-c31dc2a380bf0\p\include -external:IC:\Users\<USER>\.conan2\p\grpc8b6c73b4e5756\p\include -external:IC:\Users\<USER>\.conan2\p\absei6a15444d60d47\p\include -external:W0
  OBJECT_DIR = CMakeFiles\_CuraEngine.dir
  OBJECT_FILE_DIR = CMakeFiles\_CuraEngine.dir\src\geometry
  TARGET_COMPILE_PDB = CMakeFiles\_CuraEngine.dir\_CuraEngine.pdb
  TARGET_PDB = _CuraEngine.pdb

build CMakeFiles\_CuraEngine.dir\src\geometry\LinesSet.cpp.obj: CXX_COMPILER___CuraEngine_unscanned_Release C$:\Users\wsd07\vscode\Cura-Dev\CuraEngine\src\geometry\LinesSet.cpp || cmake_object_order_depends_target__CuraEngine
  CONFIG = Release
  DEFINES = -DAGRPC_BOOST_ASIO -DARCUS -DBOOST_ALL_NO_LIB -DBOOST_STACKTRACE_USE_NOOP -DBOOST_STACKTRACE_USE_WINDBG -DBOOST_STACKTRACE_USE_WINDBG_CACHED -DCARES_STATICLIB -DCURA_ENGINE_HASH=\"unknown\" -DCURA_ENGINE_VERSION=\"5.11.0-alpha.0\" -DENABLE_PLUGINS -DNOMINMAX -DSENTRY_ENVIRONMENT=\"development\" -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSTB_TEXTEDIT_KEYTYPE=unsigned
  FLAGS = /DWIN32 /D_WINDOWS /EHsc /O2 /Ob2 /DNDEBUG -std:c++20 -MD /utf-8 /permissive-
  INCLUDES = -IC:\Users\<USER>\vscode\Cura-Dev\CuraEngine\include -IC:\Users\<USER>\vscode\Cura-Dev\CuraEngine\build\Release -external:IC:\Users\<USER>\.conan2\p\b\arcusee7666f716199\p\include -external:IC:\Users\<USER>\.conan2\p\proto47a676cb9257b\p\include -external:IC:\Users\<USER>\.conan2\p\zlibcfb9789dc2a53\p\include -external:IC:\Users\<USER>\.conan2\p\spdlo2d4b540ab0f22\p\include -external:IC:\Users\<USER>\.conan2\p\fmtdb20aad6e1bd4\p\include -external:IC:\Users\<USER>\.conan2\p\range0301bf3d76d5d\p\include -external:IC:\Users\<USER>\.conan2\p\b\clippf8ddcd4a5d961\p\include -external:IC:\Users\<USER>\.conan2\p\mapbo34ee9aeb574f9\p\include -external:IC:\Users\<USER>\.conan2\p\mapbofe72fb50cf7c4\p\include -external:IC:\Users\<USER>\.conan2\p\mapbo1b62b48748b82\p\include -external:IC:\Users\<USER>\.conan2\p\rapidf7a3355ba53c4\p\include -external:IC:\Users\<USER>\.conan2\p\stb6342cecb318f5\p\include -external:IC:\Users\<USER>\.conan2\p\boosta79d738920bc3\p\include -external:IC:\Users\<USER>\.conan2\p\scrip4cfd014ad4660\p\include -external:IC:\Users\<USER>\.conan2\p\nearg388d58da7a54c\p\include -external:IC:\Users\<USER>\.conan2\p\b\curaebb245e64b5ac7\p\include -external:IC:\Users\<USER>\.conan2\p\asio-c31dc2a380bf0\p\include -external:IC:\Users\<USER>\.conan2\p\grpc8b6c73b4e5756\p\include -external:IC:\Users\<USER>\.conan2\p\absei6a15444d60d47\p\include -external:W0
  OBJECT_DIR = CMakeFiles\_CuraEngine.dir
  OBJECT_FILE_DIR = CMakeFiles\_CuraEngine.dir\src\geometry
  TARGET_COMPILE_PDB = CMakeFiles\_CuraEngine.dir\_CuraEngine.pdb
  TARGET_PDB = _CuraEngine.pdb

build CMakeFiles\_CuraEngine.dir\src\geometry\Polyline.cpp.obj: CXX_COMPILER___CuraEngine_unscanned_Release C$:\Users\wsd07\vscode\Cura-Dev\CuraEngine\src\geometry\Polyline.cpp || cmake_object_order_depends_target__CuraEngine
  CONFIG = Release
  DEFINES = -DAGRPC_BOOST_ASIO -DARCUS -DBOOST_ALL_NO_LIB -DBOOST_STACKTRACE_USE_NOOP -DBOOST_STACKTRACE_USE_WINDBG -DBOOST_STACKTRACE_USE_WINDBG_CACHED -DCARES_STATICLIB -DCURA_ENGINE_HASH=\"unknown\" -DCURA_ENGINE_VERSION=\"5.11.0-alpha.0\" -DENABLE_PLUGINS -DNOMINMAX -DSENTRY_ENVIRONMENT=\"development\" -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSTB_TEXTEDIT_KEYTYPE=unsigned
  FLAGS = /DWIN32 /D_WINDOWS /EHsc /O2 /Ob2 /DNDEBUG -std:c++20 -MD /utf-8 /permissive-
  INCLUDES = -IC:\Users\<USER>\vscode\Cura-Dev\CuraEngine\include -IC:\Users\<USER>\vscode\Cura-Dev\CuraEngine\build\Release -external:IC:\Users\<USER>\.conan2\p\b\arcusee7666f716199\p\include -external:IC:\Users\<USER>\.conan2\p\proto47a676cb9257b\p\include -external:IC:\Users\<USER>\.conan2\p\zlibcfb9789dc2a53\p\include -external:IC:\Users\<USER>\.conan2\p\spdlo2d4b540ab0f22\p\include -external:IC:\Users\<USER>\.conan2\p\fmtdb20aad6e1bd4\p\include -external:IC:\Users\<USER>\.conan2\p\range0301bf3d76d5d\p\include -external:IC:\Users\<USER>\.conan2\p\b\clippf8ddcd4a5d961\p\include -external:IC:\Users\<USER>\.conan2\p\mapbo34ee9aeb574f9\p\include -external:IC:\Users\<USER>\.conan2\p\mapbofe72fb50cf7c4\p\include -external:IC:\Users\<USER>\.conan2\p\mapbo1b62b48748b82\p\include -external:IC:\Users\<USER>\.conan2\p\rapidf7a3355ba53c4\p\include -external:IC:\Users\<USER>\.conan2\p\stb6342cecb318f5\p\include -external:IC:\Users\<USER>\.conan2\p\boosta79d738920bc3\p\include -external:IC:\Users\<USER>\.conan2\p\scrip4cfd014ad4660\p\include -external:IC:\Users\<USER>\.conan2\p\nearg388d58da7a54c\p\include -external:IC:\Users\<USER>\.conan2\p\b\curaebb245e64b5ac7\p\include -external:IC:\Users\<USER>\.conan2\p\asio-c31dc2a380bf0\p\include -external:IC:\Users\<USER>\.conan2\p\grpc8b6c73b4e5756\p\include -external:IC:\Users\<USER>\.conan2\p\absei6a15444d60d47\p\include -external:W0
  OBJECT_DIR = CMakeFiles\_CuraEngine.dir
  OBJECT_FILE_DIR = CMakeFiles\_CuraEngine.dir\src\geometry
  TARGET_COMPILE_PDB = CMakeFiles\_CuraEngine.dir\_CuraEngine.pdb
  TARGET_PDB = _CuraEngine.pdb

build CMakeFiles\_CuraEngine.dir\src\geometry\ClosedPolyline.cpp.obj: CXX_COMPILER___CuraEngine_unscanned_Release C$:\Users\wsd07\vscode\Cura-Dev\CuraEngine\src\geometry\ClosedPolyline.cpp || cmake_object_order_depends_target__CuraEngine
  CONFIG = Release
  DEFINES = -DAGRPC_BOOST_ASIO -DARCUS -DBOOST_ALL_NO_LIB -DBOOST_STACKTRACE_USE_NOOP -DBOOST_STACKTRACE_USE_WINDBG -DBOOST_STACKTRACE_USE_WINDBG_CACHED -DCARES_STATICLIB -DCURA_ENGINE_HASH=\"unknown\" -DCURA_ENGINE_VERSION=\"5.11.0-alpha.0\" -DENABLE_PLUGINS -DNOMINMAX -DSENTRY_ENVIRONMENT=\"development\" -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSTB_TEXTEDIT_KEYTYPE=unsigned
  FLAGS = /DWIN32 /D_WINDOWS /EHsc /O2 /Ob2 /DNDEBUG -std:c++20 -MD /utf-8 /permissive-
  INCLUDES = -IC:\Users\<USER>\vscode\Cura-Dev\CuraEngine\include -IC:\Users\<USER>\vscode\Cura-Dev\CuraEngine\build\Release -external:IC:\Users\<USER>\.conan2\p\b\arcusee7666f716199\p\include -external:IC:\Users\<USER>\.conan2\p\proto47a676cb9257b\p\include -external:IC:\Users\<USER>\.conan2\p\zlibcfb9789dc2a53\p\include -external:IC:\Users\<USER>\.conan2\p\spdlo2d4b540ab0f22\p\include -external:IC:\Users\<USER>\.conan2\p\fmtdb20aad6e1bd4\p\include -external:IC:\Users\<USER>\.conan2\p\range0301bf3d76d5d\p\include -external:IC:\Users\<USER>\.conan2\p\b\clippf8ddcd4a5d961\p\include -external:IC:\Users\<USER>\.conan2\p\mapbo34ee9aeb574f9\p\include -external:IC:\Users\<USER>\.conan2\p\mapbofe72fb50cf7c4\p\include -external:IC:\Users\<USER>\.conan2\p\mapbo1b62b48748b82\p\include -external:IC:\Users\<USER>\.conan2\p\rapidf7a3355ba53c4\p\include -external:IC:\Users\<USER>\.conan2\p\stb6342cecb318f5\p\include -external:IC:\Users\<USER>\.conan2\p\boosta79d738920bc3\p\include -external:IC:\Users\<USER>\.conan2\p\scrip4cfd014ad4660\p\include -external:IC:\Users\<USER>\.conan2\p\nearg388d58da7a54c\p\include -external:IC:\Users\<USER>\.conan2\p\b\curaebb245e64b5ac7\p\include -external:IC:\Users\<USER>\.conan2\p\asio-c31dc2a380bf0\p\include -external:IC:\Users\<USER>\.conan2\p\grpc8b6c73b4e5756\p\include -external:IC:\Users\<USER>\.conan2\p\absei6a15444d60d47\p\include -external:W0
  OBJECT_DIR = CMakeFiles\_CuraEngine.dir
  OBJECT_FILE_DIR = CMakeFiles\_CuraEngine.dir\src\geometry
  TARGET_COMPILE_PDB = CMakeFiles\_CuraEngine.dir\_CuraEngine.pdb
  TARGET_PDB = _CuraEngine.pdb

build CMakeFiles\_CuraEngine.dir\src\geometry\MixedLinesSet.cpp.obj: CXX_COMPILER___CuraEngine_unscanned_Release C$:\Users\wsd07\vscode\Cura-Dev\CuraEngine\src\geometry\MixedLinesSet.cpp || cmake_object_order_depends_target__CuraEngine
  CONFIG = Release
  DEFINES = -DAGRPC_BOOST_ASIO -DARCUS -DBOOST_ALL_NO_LIB -DBOOST_STACKTRACE_USE_NOOP -DBOOST_STACKTRACE_USE_WINDBG -DBOOST_STACKTRACE_USE_WINDBG_CACHED -DCARES_STATICLIB -DCURA_ENGINE_HASH=\"unknown\" -DCURA_ENGINE_VERSION=\"5.11.0-alpha.0\" -DENABLE_PLUGINS -DNOMINMAX -DSENTRY_ENVIRONMENT=\"development\" -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSTB_TEXTEDIT_KEYTYPE=unsigned
  FLAGS = /DWIN32 /D_WINDOWS /EHsc /O2 /Ob2 /DNDEBUG -std:c++20 -MD /utf-8 /permissive-
  INCLUDES = -IC:\Users\<USER>\vscode\Cura-Dev\CuraEngine\include -IC:\Users\<USER>\vscode\Cura-Dev\CuraEngine\build\Release -external:IC:\Users\<USER>\.conan2\p\b\arcusee7666f716199\p\include -external:IC:\Users\<USER>\.conan2\p\proto47a676cb9257b\p\include -external:IC:\Users\<USER>\.conan2\p\zlibcfb9789dc2a53\p\include -external:IC:\Users\<USER>\.conan2\p\spdlo2d4b540ab0f22\p\include -external:IC:\Users\<USER>\.conan2\p\fmtdb20aad6e1bd4\p\include -external:IC:\Users\<USER>\.conan2\p\range0301bf3d76d5d\p\include -external:IC:\Users\<USER>\.conan2\p\b\clippf8ddcd4a5d961\p\include -external:IC:\Users\<USER>\.conan2\p\mapbo34ee9aeb574f9\p\include -external:IC:\Users\<USER>\.conan2\p\mapbofe72fb50cf7c4\p\include -external:IC:\Users\<USER>\.conan2\p\mapbo1b62b48748b82\p\include -external:IC:\Users\<USER>\.conan2\p\rapidf7a3355ba53c4\p\include -external:IC:\Users\<USER>\.conan2\p\stb6342cecb318f5\p\include -external:IC:\Users\<USER>\.conan2\p\boosta79d738920bc3\p\include -external:IC:\Users\<USER>\.conan2\p\scrip4cfd014ad4660\p\include -external:IC:\Users\<USER>\.conan2\p\nearg388d58da7a54c\p\include -external:IC:\Users\<USER>\.conan2\p\b\curaebb245e64b5ac7\p\include -external:IC:\Users\<USER>\.conan2\p\asio-c31dc2a380bf0\p\include -external:IC:\Users\<USER>\.conan2\p\grpc8b6c73b4e5756\p\include -external:IC:\Users\<USER>\.conan2\p\absei6a15444d60d47\p\include -external:W0
  OBJECT_DIR = CMakeFiles\_CuraEngine.dir
  OBJECT_FILE_DIR = CMakeFiles\_CuraEngine.dir\src\geometry
  TARGET_COMPILE_PDB = CMakeFiles\_CuraEngine.dir\_CuraEngine.pdb
  TARGET_PDB = _CuraEngine.pdb

build CMakeFiles\_CuraEngine.dir\src\boost_exception_fix.cpp.obj: CXX_COMPILER___CuraEngine_unscanned_Release C$:\Users\wsd07\vscode\Cura-Dev\CuraEngine\src\boost_exception_fix.cpp || cmake_object_order_depends_target__CuraEngine
  CONFIG = Release
  DEFINES = -DAGRPC_BOOST_ASIO -DARCUS -DBOOST_ALL_NO_LIB -DBOOST_STACKTRACE_USE_NOOP -DBOOST_STACKTRACE_USE_WINDBG -DBOOST_STACKTRACE_USE_WINDBG_CACHED -DCARES_STATICLIB -DCURA_ENGINE_HASH=\"unknown\" -DCURA_ENGINE_VERSION=\"5.11.0-alpha.0\" -DENABLE_PLUGINS -DNOMINMAX -DSENTRY_ENVIRONMENT=\"development\" -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSTB_TEXTEDIT_KEYTYPE=unsigned
  FLAGS = /DWIN32 /D_WINDOWS /EHsc /O2 /Ob2 /DNDEBUG -std:c++20 -MD /utf-8 /permissive-
  INCLUDES = -IC:\Users\<USER>\vscode\Cura-Dev\CuraEngine\include -IC:\Users\<USER>\vscode\Cura-Dev\CuraEngine\build\Release -external:IC:\Users\<USER>\.conan2\p\b\arcusee7666f716199\p\include -external:IC:\Users\<USER>\.conan2\p\proto47a676cb9257b\p\include -external:IC:\Users\<USER>\.conan2\p\zlibcfb9789dc2a53\p\include -external:IC:\Users\<USER>\.conan2\p\spdlo2d4b540ab0f22\p\include -external:IC:\Users\<USER>\.conan2\p\fmtdb20aad6e1bd4\p\include -external:IC:\Users\<USER>\.conan2\p\range0301bf3d76d5d\p\include -external:IC:\Users\<USER>\.conan2\p\b\clippf8ddcd4a5d961\p\include -external:IC:\Users\<USER>\.conan2\p\mapbo34ee9aeb574f9\p\include -external:IC:\Users\<USER>\.conan2\p\mapbofe72fb50cf7c4\p\include -external:IC:\Users\<USER>\.conan2\p\mapbo1b62b48748b82\p\include -external:IC:\Users\<USER>\.conan2\p\rapidf7a3355ba53c4\p\include -external:IC:\Users\<USER>\.conan2\p\stb6342cecb318f5\p\include -external:IC:\Users\<USER>\.conan2\p\boosta79d738920bc3\p\include -external:IC:\Users\<USER>\.conan2\p\scrip4cfd014ad4660\p\include -external:IC:\Users\<USER>\.conan2\p\nearg388d58da7a54c\p\include -external:IC:\Users\<USER>\.conan2\p\b\curaebb245e64b5ac7\p\include -external:IC:\Users\<USER>\.conan2\p\asio-c31dc2a380bf0\p\include -external:IC:\Users\<USER>\.conan2\p\grpc8b6c73b4e5756\p\include -external:IC:\Users\<USER>\.conan2\p\absei6a15444d60d47\p\include -external:W0
  OBJECT_DIR = CMakeFiles\_CuraEngine.dir
  OBJECT_FILE_DIR = CMakeFiles\_CuraEngine.dir\src
  TARGET_COMPILE_PDB = CMakeFiles\_CuraEngine.dir\_CuraEngine.pdb
  TARGET_PDB = _CuraEngine.pdb

build CMakeFiles\_CuraEngine.dir\Cura.pb.cc.obj: CXX_COMPILER___CuraEngine_unscanned_Release C$:\Users\wsd07\vscode\Cura-Dev\CuraEngine\build\Release\Cura.pb.cc || cmake_object_order_depends_target__CuraEngine
  CONFIG = Release
  DEFINES = -DAGRPC_BOOST_ASIO -DARCUS -DBOOST_ALL_NO_LIB -DBOOST_STACKTRACE_USE_NOOP -DBOOST_STACKTRACE_USE_WINDBG -DBOOST_STACKTRACE_USE_WINDBG_CACHED -DCARES_STATICLIB -DCURA_ENGINE_HASH=\"unknown\" -DCURA_ENGINE_VERSION=\"5.11.0-alpha.0\" -DENABLE_PLUGINS -DNOMINMAX -DSENTRY_ENVIRONMENT=\"development\" -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSTB_TEXTEDIT_KEYTYPE=unsigned
  FLAGS = /DWIN32 /D_WINDOWS /EHsc /O2 /Ob2 /DNDEBUG -std:c++20 -MD /utf-8 /permissive-
  INCLUDES = -IC:\Users\<USER>\vscode\Cura-Dev\CuraEngine\include -IC:\Users\<USER>\vscode\Cura-Dev\CuraEngine\build\Release -external:IC:\Users\<USER>\.conan2\p\b\arcusee7666f716199\p\include -external:IC:\Users\<USER>\.conan2\p\proto47a676cb9257b\p\include -external:IC:\Users\<USER>\.conan2\p\zlibcfb9789dc2a53\p\include -external:IC:\Users\<USER>\.conan2\p\spdlo2d4b540ab0f22\p\include -external:IC:\Users\<USER>\.conan2\p\fmtdb20aad6e1bd4\p\include -external:IC:\Users\<USER>\.conan2\p\range0301bf3d76d5d\p\include -external:IC:\Users\<USER>\.conan2\p\b\clippf8ddcd4a5d961\p\include -external:IC:\Users\<USER>\.conan2\p\mapbo34ee9aeb574f9\p\include -external:IC:\Users\<USER>\.conan2\p\mapbofe72fb50cf7c4\p\include -external:IC:\Users\<USER>\.conan2\p\mapbo1b62b48748b82\p\include -external:IC:\Users\<USER>\.conan2\p\rapidf7a3355ba53c4\p\include -external:IC:\Users\<USER>\.conan2\p\stb6342cecb318f5\p\include -external:IC:\Users\<USER>\.conan2\p\boosta79d738920bc3\p\include -external:IC:\Users\<USER>\.conan2\p\scrip4cfd014ad4660\p\include -external:IC:\Users\<USER>\.conan2\p\nearg388d58da7a54c\p\include -external:IC:\Users\<USER>\.conan2\p\b\curaebb245e64b5ac7\p\include -external:IC:\Users\<USER>\.conan2\p\asio-c31dc2a380bf0\p\include -external:IC:\Users\<USER>\.conan2\p\grpc8b6c73b4e5756\p\include -external:IC:\Users\<USER>\.conan2\p\absei6a15444d60d47\p\include -external:W0
  OBJECT_DIR = CMakeFiles\_CuraEngine.dir
  OBJECT_FILE_DIR = CMakeFiles\_CuraEngine.dir
  TARGET_COMPILE_PDB = CMakeFiles\_CuraEngine.dir\_CuraEngine.pdb
  TARGET_PDB = _CuraEngine.pdb


# =============================================================================
# Link build statements for STATIC_LIBRARY target _CuraEngine


#############################################
# Link the static library _CuraEngine.lib

build _CuraEngine.lib: CXX_STATIC_LIBRARY_LINKER___CuraEngine_Release CMakeFiles\_CuraEngine.dir\src\Application.cpp.obj CMakeFiles\_CuraEngine.dir\src\bridge.cpp.obj CMakeFiles\_CuraEngine.dir\src\ConicalOverhang.cpp.obj CMakeFiles\_CuraEngine.dir\src\ExtruderPlan.cpp.obj CMakeFiles\_CuraEngine.dir\src\ExtruderTrain.cpp.obj CMakeFiles\_CuraEngine.dir\src\FffGcodeWriter.cpp.obj CMakeFiles\_CuraEngine.dir\src\FffPolygonGenerator.cpp.obj CMakeFiles\_CuraEngine.dir\src\FffProcessor.cpp.obj CMakeFiles\_CuraEngine.dir\src\gcodeExport.cpp.obj CMakeFiles\_CuraEngine.dir\src\GCodePathConfig.cpp.obj CMakeFiles\_CuraEngine.dir\src\infill.cpp.obj CMakeFiles\_CuraEngine.dir\src\InterlockingGenerator.cpp.obj CMakeFiles\_CuraEngine.dir\src\InsetOrderOptimizer.cpp.obj CMakeFiles\_CuraEngine.dir\src\layerPart.cpp.obj CMakeFiles\_CuraEngine.dir\src\LayerPlan.cpp.obj CMakeFiles\_CuraEngine.dir\src\LayerPlanBuffer.cpp.obj CMakeFiles\_CuraEngine.dir\src\mesh.cpp.obj CMakeFiles\_CuraEngine.dir\src\MeshGroup.cpp.obj CMakeFiles\_CuraEngine.dir\src\Mold.cpp.obj CMakeFiles\_CuraEngine.dir\src\multiVolumes.cpp.obj CMakeFiles\_CuraEngine.dir\src\path_ordering.cpp.obj CMakeFiles\_CuraEngine.dir\src\PathAdapter.cpp.obj CMakeFiles\_CuraEngine.dir\src\Preheat.cpp.obj CMakeFiles\_CuraEngine.dir\src\PrimeTower\PrimeTower.cpp.obj CMakeFiles\_CuraEngine.dir\src\PrimeTower\PrimeTowerNormal.cpp.obj CMakeFiles\_CuraEngine.dir\src\PrimeTower\PrimeTowerInterleaved.cpp.obj CMakeFiles\_CuraEngine.dir\src\raft.cpp.obj CMakeFiles\_CuraEngine.dir\src\Scene.cpp.obj CMakeFiles\_CuraEngine.dir\src\SkeletalTrapezoidation.cpp.obj CMakeFiles\_CuraEngine.dir\src\SkeletalTrapezoidationGraph.cpp.obj CMakeFiles\_CuraEngine.dir\src\skin.cpp.obj CMakeFiles\_CuraEngine.dir\src\SkirtBrim.cpp.obj CMakeFiles\_CuraEngine.dir\src\SupportInfillPart.cpp.obj CMakeFiles\_CuraEngine.dir\src\Slice.cpp.obj CMakeFiles\_CuraEngine.dir\src\sliceDataStorage.cpp.obj CMakeFiles\_CuraEngine.dir\src\slicer.cpp.obj CMakeFiles\_CuraEngine.dir\src\support.cpp.obj CMakeFiles\_CuraEngine.dir\src\timeEstimate.cpp.obj CMakeFiles\_CuraEngine.dir\src\TopSurface.cpp.obj CMakeFiles\_CuraEngine.dir\src\TreeSupportTipGenerator.cpp.obj CMakeFiles\_CuraEngine.dir\src\TreeModelVolumes.cpp.obj CMakeFiles\_CuraEngine.dir\src\TreeSupport.cpp.obj CMakeFiles\_CuraEngine.dir\src\WallsComputation.cpp.obj CMakeFiles\_CuraEngine.dir\src\WallToolPaths.cpp.obj CMakeFiles\_CuraEngine.dir\src\BeadingStrategy\BeadingStrategy.cpp.obj CMakeFiles\_CuraEngine.dir\src\BeadingStrategy\BeadingStrategyFactory.cpp.obj CMakeFiles\_CuraEngine.dir\src\BeadingStrategy\DistributedBeadingStrategy.cpp.obj CMakeFiles\_CuraEngine.dir\src\BeadingStrategy\LimitedBeadingStrategy.cpp.obj CMakeFiles\_CuraEngine.dir\src\BeadingStrategy\RedistributeBeadingStrategy.cpp.obj CMakeFiles\_CuraEngine.dir\src\BeadingStrategy\WideningBeadingStrategy.cpp.obj CMakeFiles\_CuraEngine.dir\src\BeadingStrategy\OuterWallInsetBeadingStrategy.cpp.obj CMakeFiles\_CuraEngine.dir\src\communication\ArcusCommunication.cpp.obj CMakeFiles\_CuraEngine.dir\src\communication\ArcusCommunicationPrivate.cpp.obj CMakeFiles\_CuraEngine.dir\src\communication\CommandLine.cpp.obj CMakeFiles\_CuraEngine.dir\src\communication\EmscriptenCommunication.cpp.obj CMakeFiles\_CuraEngine.dir\src\communication\Listener.cpp.obj CMakeFiles\_CuraEngine.dir\src\infill\ImageBasedDensityProvider.cpp.obj CMakeFiles\_CuraEngine.dir\src\infill\NoZigZagConnectorProcessor.cpp.obj CMakeFiles\_CuraEngine.dir\src\infill\ZigzagConnectorProcessor.cpp.obj CMakeFiles\_CuraEngine.dir\src\infill\LightningDistanceField.cpp.obj CMakeFiles\_CuraEngine.dir\src\infill\LightningGenerator.cpp.obj CMakeFiles\_CuraEngine.dir\src\infill\LightningLayer.cpp.obj CMakeFiles\_CuraEngine.dir\src\infill\LightningTreeNode.cpp.obj CMakeFiles\_CuraEngine.dir\src\infill\SierpinskiFill.cpp.obj CMakeFiles\_CuraEngine.dir\src\infill\SierpinskiFillProvider.cpp.obj CMakeFiles\_CuraEngine.dir\src\infill\SubDivCube.cpp.obj CMakeFiles\_CuraEngine.dir\src\infill\GyroidInfill.cpp.obj CMakeFiles\_CuraEngine.dir\src\pathPlanning\Comb.cpp.obj CMakeFiles\_CuraEngine.dir\src\pathPlanning\GCodePath.cpp.obj CMakeFiles\_CuraEngine.dir\src\pathPlanning\LinePolygonsCrossings.cpp.obj CMakeFiles\_CuraEngine.dir\src\pathPlanning\NozzleTempInsert.cpp.obj CMakeFiles\_CuraEngine.dir\src\pathPlanning\SpeedDerivatives.cpp.obj CMakeFiles\_CuraEngine.dir\src\plugins\converters.cpp.obj CMakeFiles\_CuraEngine.dir\src\progress\Progress.cpp.obj CMakeFiles\_CuraEngine.dir\src\progress\ProgressStageEstimator.cpp.obj CMakeFiles\_CuraEngine.dir\src\settings\AdaptiveLayerHeights.cpp.obj CMakeFiles\_CuraEngine.dir\src\settings\FlowTempGraph.cpp.obj CMakeFiles\_CuraEngine.dir\src\settings\MeshPathConfigs.cpp.obj CMakeFiles\_CuraEngine.dir\src\settings\PathConfigStorage.cpp.obj CMakeFiles\_CuraEngine.dir\src\settings\Settings.cpp.obj CMakeFiles\_CuraEngine.dir\src\settings\ZSeamConfig.cpp.obj CMakeFiles\_CuraEngine.dir\src\utils\AABB.cpp.obj CMakeFiles\_CuraEngine.dir\src\utils\AABB3D.cpp.obj CMakeFiles\_CuraEngine.dir\src\utils\channel.cpp.obj CMakeFiles\_CuraEngine.dir\src\utils\Date.cpp.obj CMakeFiles\_CuraEngine.dir\src\utils\ExtrusionJunction.cpp.obj CMakeFiles\_CuraEngine.dir\src\utils\ExtrusionLine.cpp.obj CMakeFiles\_CuraEngine.dir\src\utils\ExtrusionSegment.cpp.obj CMakeFiles\_CuraEngine.dir\src\utils\gettime.cpp.obj CMakeFiles\_CuraEngine.dir\src\utils\linearAlg2D.cpp.obj CMakeFiles\_CuraEngine.dir\src\utils\ListPolyIt.cpp.obj CMakeFiles\_CuraEngine.dir\src\utils\Matrix4x3D.cpp.obj CMakeFiles\_CuraEngine.dir\src\utils\MinimumSpanningTree.cpp.obj CMakeFiles\_CuraEngine.dir\src\utils\PolygonConnector.cpp.obj CMakeFiles\_CuraEngine.dir\src\utils\PolygonsPointIndex.cpp.obj CMakeFiles\_CuraEngine.dir\src\utils\PolygonsSegmentIndex.cpp.obj CMakeFiles\_CuraEngine.dir\src\utils\polygonUtils.cpp.obj CMakeFiles\_CuraEngine.dir\src\utils\PolylineStitcher.cpp.obj CMakeFiles\_CuraEngine.dir\src\utils\Simplify.cpp.obj CMakeFiles\_CuraEngine.dir\src\utils\SVG.cpp.obj CMakeFiles\_CuraEngine.dir\src\utils\SquareGrid.cpp.obj CMakeFiles\_CuraEngine.dir\src\utils\ThreadPool.cpp.obj CMakeFiles\_CuraEngine.dir\src\utils\ToolpathVisualizer.cpp.obj CMakeFiles\_CuraEngine.dir\src\utils\VoronoiUtils.cpp.obj CMakeFiles\_CuraEngine.dir\src\utils\VoxelUtils.cpp.obj CMakeFiles\_CuraEngine.dir\src\utils\MixedPolylineStitcher.cpp.obj CMakeFiles\_CuraEngine.dir\src\utils\scoring\BestElementFinder.cpp.obj CMakeFiles\_CuraEngine.dir\src\utils\scoring\CornerScoringCriterion.cpp.obj CMakeFiles\_CuraEngine.dir\src\utils\scoring\DistanceScoringCriterion.cpp.obj CMakeFiles\_CuraEngine.dir\src\utils\scoring\ExclusionAreaScoringCriterion.cpp.obj CMakeFiles\_CuraEngine.dir\src\utils\scoring\RandomScoringCriterion.cpp.obj CMakeFiles\_CuraEngine.dir\src\geometry\Point2LL.cpp.obj CMakeFiles\_CuraEngine.dir\src\geometry\Point3LL.cpp.obj CMakeFiles\_CuraEngine.dir\src\geometry\Polygon.cpp.obj CMakeFiles\_CuraEngine.dir\src\geometry\Shape.cpp.obj CMakeFiles\_CuraEngine.dir\src\geometry\PointsSet.cpp.obj CMakeFiles\_CuraEngine.dir\src\geometry\SingleShape.cpp.obj CMakeFiles\_CuraEngine.dir\src\geometry\PartsView.cpp.obj CMakeFiles\_CuraEngine.dir\src\geometry\LinesSet.cpp.obj CMakeFiles\_CuraEngine.dir\src\geometry\Polyline.cpp.obj CMakeFiles\_CuraEngine.dir\src\geometry\ClosedPolyline.cpp.obj CMakeFiles\_CuraEngine.dir\src\geometry\MixedLinesSet.cpp.obj CMakeFiles\_CuraEngine.dir\src\boost_exception_fix.cpp.obj CMakeFiles\_CuraEngine.dir\Cura.pb.cc.obj
  CONFIG = Release
  LANGUAGE_COMPILE_FLAGS = /DWIN32 /D_WINDOWS /EHsc /O2 /Ob2 /DNDEBUG -MD
  LINK_FLAGS = /machine:x64
  OBJECT_DIR = CMakeFiles\_CuraEngine.dir
  POST_BUILD = cd .
  PRE_LINK = cd .
  TARGET_COMPILE_PDB = CMakeFiles\_CuraEngine.dir\_CuraEngine.pdb
  TARGET_FILE = _CuraEngine.lib
  TARGET_PDB = _CuraEngine.pdb
  RSP_FILE = CMakeFiles\_CuraEngine.rsp

# =============================================================================
# Object build statements for EXECUTABLE target CuraEngine


#############################################
# Order-only phony target for CuraEngine

build cmake_object_order_depends_target_CuraEngine: phony || cmake_object_order_depends_target__CuraEngine

build CMakeFiles\CuraEngine.dir\src\main.cpp.obj: CXX_COMPILER__CuraEngine_unscanned_Release C$:\Users\wsd07\vscode\Cura-Dev\CuraEngine\src\main.cpp || cmake_object_order_depends_target_CuraEngine
  CONFIG = Release
  DEFINES = -DAGRPC_BOOST_ASIO -DARCUS -DBOOST_ALL_NO_LIB -DBOOST_STACKTRACE_USE_NOOP -DBOOST_STACKTRACE_USE_WINDBG -DBOOST_STACKTRACE_USE_WINDBG_CACHED -DCARES_STATICLIB -DCURA_ENGINE_HASH=\"unknown\" -DCURA_ENGINE_VERSION=\"5.11.0-alpha.0\" -DENABLE_PLUGINS -DSENTRY_ENVIRONMENT=\"development\" -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSTB_TEXTEDIT_KEYTYPE=unsigned -DVERSION=\"5.11.0-alpha.0\"
  FLAGS = /DWIN32 /D_WINDOWS /EHsc /O2 /Ob2 /DNDEBUG -std:c++20 -MD /utf-8 /permissive-
  INCLUDES = -IC:\Users\<USER>\vscode\Cura-Dev\CuraEngine\include -external:IC:\Users\<USER>\.conan2\p\b\arcusee7666f716199\p\include -external:IC:\Users\<USER>\.conan2\p\proto47a676cb9257b\p\include -external:IC:\Users\<USER>\.conan2\p\zlibcfb9789dc2a53\p\include -external:IC:\Users\<USER>\.conan2\p\spdlo2d4b540ab0f22\p\include -external:IC:\Users\<USER>\.conan2\p\fmtdb20aad6e1bd4\p\include -external:IC:\Users\<USER>\.conan2\p\range0301bf3d76d5d\p\include -external:IC:\Users\<USER>\.conan2\p\b\clippf8ddcd4a5d961\p\include -external:IC:\Users\<USER>\.conan2\p\mapbo34ee9aeb574f9\p\include -external:IC:\Users\<USER>\.conan2\p\mapbofe72fb50cf7c4\p\include -external:IC:\Users\<USER>\.conan2\p\mapbo1b62b48748b82\p\include -external:IC:\Users\<USER>\.conan2\p\rapidf7a3355ba53c4\p\include -external:IC:\Users\<USER>\.conan2\p\stb6342cecb318f5\p\include -external:IC:\Users\<USER>\.conan2\p\boosta79d738920bc3\p\include -external:IC:\Users\<USER>\.conan2\p\scrip4cfd014ad4660\p\include -external:IC:\Users\<USER>\.conan2\p\nearg388d58da7a54c\p\include -external:IC:\Users\<USER>\.conan2\p\b\curaebb245e64b5ac7\p\include -external:IC:\Users\<USER>\.conan2\p\asio-c31dc2a380bf0\p\include -external:IC:\Users\<USER>\.conan2\p\grpc8b6c73b4e5756\p\include -external:IC:\Users\<USER>\.conan2\p\absei6a15444d60d47\p\include -external:W0
  OBJECT_DIR = CMakeFiles\CuraEngine.dir
  OBJECT_FILE_DIR = CMakeFiles\CuraEngine.dir\src
  TARGET_COMPILE_PDB = CMakeFiles\CuraEngine.dir\
  TARGET_PDB = CuraEngine.pdb

build CMakeFiles\CuraEngine.dir\CuraEngine.rc.res: RC_COMPILER__CuraEngine_unscanned_Release C$:\Users\wsd07\vscode\Cura-Dev\CuraEngine\CuraEngine.rc || cmake_object_order_depends_target_CuraEngine
  CONFIG = Release
  DEFINES = -DAGRPC_BOOST_ASIO -DARCUS -DBOOST_ALL_NO_LIB -DBOOST_STACKTRACE_USE_NOOP -DBOOST_STACKTRACE_USE_WINDBG -DBOOST_STACKTRACE_USE_WINDBG_CACHED -DCARES_STATICLIB -DCURA_ENGINE_HASH=\"unknown\" -DCURA_ENGINE_VERSION=\"5.11.0-alpha.0\" -DENABLE_PLUGINS -DSENTRY_ENVIRONMENT=\"development\" -DSPDLOG_COMPILED_LIB -DSPDLOG_FMT_EXTERNAL -DSTB_TEXTEDIT_KEYTYPE=unsigned -DVERSION=\"5.11.0-alpha.0\"
  DEP_FILE = CMakeFiles\CuraEngine.dir\CuraEngine.rc.res.d
  FLAGS = -DWIN32
  INCLUDES = -I C:\Users\<USER>\vscode\Cura-Dev\CuraEngine\include -I C:\Users\<USER>\.conan2\p\b\arcusee7666f716199\p\include -I C:\Users\<USER>\.conan2\p\proto47a676cb9257b\p\include -I C:\Users\<USER>\.conan2\p\zlibcfb9789dc2a53\p\include -I C:\Users\<USER>\.conan2\p\spdlo2d4b540ab0f22\p\include -I C:\Users\<USER>\.conan2\p\fmtdb20aad6e1bd4\p\include -I C:\Users\<USER>\.conan2\p\range0301bf3d76d5d\p\include -I C:\Users\<USER>\.conan2\p\b\clippf8ddcd4a5d961\p\include -I C:\Users\<USER>\.conan2\p\mapbo34ee9aeb574f9\p\include -I C:\Users\<USER>\.conan2\p\mapbofe72fb50cf7c4\p\include -I C:\Users\<USER>\.conan2\p\mapbo1b62b48748b82\p\include -I C:\Users\<USER>\.conan2\p\rapidf7a3355ba53c4\p\include -I C:\Users\<USER>\.conan2\p\stb6342cecb318f5\p\include -I C:\Users\<USER>\.conan2\p\boosta79d738920bc3\p\include -I C:\Users\<USER>\.conan2\p\scrip4cfd014ad4660\p\include -I C:\Users\<USER>\.conan2\p\nearg388d58da7a54c\p\include -I C:\Users\<USER>\.conan2\p\b\curaebb245e64b5ac7\p\include -I C:\Users\<USER>\.conan2\p\asio-c31dc2a380bf0\p\include -I C:\Users\<USER>\.conan2\p\grpc8b6c73b4e5756\p\include -I C:\Users\<USER>\.conan2\p\absei6a15444d60d47\p\include
  OBJECT_DIR = CMakeFiles\CuraEngine.dir
  OBJECT_FILE_DIR = CMakeFiles\CuraEngine.dir
  TARGET_COMPILE_PDB = CMakeFiles\CuraEngine.dir\
  TARGET_PDB = CuraEngine.pdb


# =============================================================================
# Link build statements for EXECUTABLE target CuraEngine


#############################################
# Link the executable CuraEngine.exe

build CuraEngine.exe: CXX_EXECUTABLE_LINKER__CuraEngine_Release CMakeFiles\CuraEngine.dir\src\main.cpp.obj CMakeFiles\CuraEngine.dir\CuraEngine.rc.res | _CuraEngine.lib C$:\Users\wsd07\.conan2\p\b\arcusee7666f716199\p\lib\Arcus.lib C$:\Users\wsd07\.conan2\p\spdlo2d4b540ab0f22\p\lib\spdlog.lib C$:\Users\wsd07\.conan2\p\fmtdb20aad6e1bd4\p\lib\fmt.lib C$:\Users\wsd07\.conan2\p\b\clippf8ddcd4a5d961\p\lib\polyclipping.lib C$:\Users\wsd07\.conan2\p\boosta79d738920bc3\p\lib\libboost_log_setup.lib C$:\Users\wsd07\.conan2\p\boosta79d738920bc3\p\lib\libboost_unit_test_framework.lib C$:\Users\wsd07\.conan2\p\boosta79d738920bc3\p\lib\libboost_type_erasure.lib C$:\Users\wsd07\.conan2\p\boosta79d738920bc3\p\lib\libboost_log.lib C$:\Users\wsd07\.conan2\p\boosta79d738920bc3\p\lib\libboost_locale.lib C$:\Users\wsd07\.conan2\p\boosta79d738920bc3\p\lib\libboost_contract.lib C$:\Users\wsd07\.conan2\p\boosta79d738920bc3\p\lib\libboost_wave.lib C$:\Users\wsd07\.conan2\p\boosta79d738920bc3\p\lib\libboost_thread.lib C$:\Users\wsd07\.conan2\p\boosta79d738920bc3\p\lib\libboost_test_exec_monitor.lib C$:\Users\wsd07\.conan2\p\boosta79d738920bc3\p\lib\libboost_prg_exec_monitor.lib C$:\Users\wsd07\.conan2\p\boosta79d738920bc3\p\lib\libboost_nowide.lib C$:\Users\wsd07\.conan2\p\boosta79d738920bc3\p\lib\libboost_iostreams.lib C$:\Users\wsd07\.conan2\p\bzip2e1752ec0a98ec\p\lib\bz2.lib C$:\Users\wsd07\.conan2\p\boosta79d738920bc3\p\lib\libboost_graph.lib C$:\Users\wsd07\.conan2\p\boosta79d738920bc3\p\lib\libboost_wserialization.lib C$:\Users\wsd07\.conan2\p\boosta79d738920bc3\p\lib\libboost_url.lib C$:\Users\wsd07\.conan2\p\boosta79d738920bc3\p\lib\libboost_stacktrace_windbg_cached.lib C$:\Users\wsd07\.conan2\p\boosta79d738920bc3\p\lib\libboost_stacktrace_windbg.lib C$:\Users\wsd07\.conan2\p\boosta79d738920bc3\p\lib\libboost_stacktrace_noop.lib C$:\Users\wsd07\.conan2\p\boosta79d738920bc3\p\lib\libboost_stacktrace_from_exception.lib C$:\Users\wsd07\.conan2\p\boosta79d738920bc3\p\lib\libboost_random.lib C$:\Users\wsd07\.conan2\p\boosta79d738920bc3\p\lib\libboost_math_tr1l.lib C$:\Users\wsd07\.conan2\p\boosta79d738920bc3\p\lib\libboost_math_tr1f.lib C$:\Users\wsd07\.conan2\p\boosta79d738920bc3\p\lib\libboost_math_tr1.lib C$:\Users\wsd07\.conan2\p\boosta79d738920bc3\p\lib\libboost_math_c99l.lib C$:\Users\wsd07\.conan2\p\boosta79d738920bc3\p\lib\libboost_math_c99f.lib C$:\Users\wsd07\.conan2\p\boosta79d738920bc3\p\lib\libboost_math_c99.lib C$:\Users\wsd07\.conan2\p\boosta79d738920bc3\p\lib\libboost_json.lib C$:\Users\wsd07\.conan2\p\boosta79d738920bc3\p\lib\libboost_filesystem.lib C$:\Users\wsd07\.conan2\p\boosta79d738920bc3\p\lib\libboost_coroutine.lib C$:\Users\wsd07\.conan2\p\boosta79d738920bc3\p\lib\libboost_chrono.lib C$:\Users\wsd07\.conan2\p\boosta79d738920bc3\p\lib\libboost_timer.lib C$:\Users\wsd07\.conan2\p\boosta79d738920bc3\p\lib\libboost_serialization.lib C$:\Users\wsd07\.conan2\p\boosta79d738920bc3\p\lib\libboost_regex.lib C$:\Users\wsd07\.conan2\p\boosta79d738920bc3\p\lib\libboost_program_options.lib C$:\Users\wsd07\.conan2\p\boosta79d738920bc3\p\lib\libboost_exception.lib C$:\Users\wsd07\.conan2\p\boosta79d738920bc3\p\lib\libboost_date_time.lib C$:\Users\wsd07\.conan2\p\boosta79d738920bc3\p\lib\libboost_context.lib C$:\Users\wsd07\.conan2\p\boosta79d738920bc3\p\lib\libboost_container.lib C$:\Users\wsd07\.conan2\p\boosta79d738920bc3\p\lib\libboost_charconv.lib C$:\Users\wsd07\.conan2\p\boosta79d738920bc3\p\lib\libboost_atomic.lib C$:\Users\wsd07\.conan2\p\b\curaebb245e64b5ac7\p\lib\curaengine_grpc_definitions.lib C$:\Users\wsd07\.conan2\p\grpc8b6c73b4e5756\p\lib\grpcpp_channelz.lib C$:\Users\wsd07\.conan2\p\grpc8b6c73b4e5756\p\lib\grpc++_reflection.lib C$:\Users\wsd07\.conan2\p\grpc8b6c73b4e5756\p\lib\grpc++_error_details.lib C$:\Users\wsd07\.conan2\p\grpc8b6c73b4e5756\p\lib\grpc++_alts.lib C$:\Users\wsd07\.conan2\p\grpc8b6c73b4e5756\p\lib\grpc++_unsecure.lib C$:\Users\wsd07\.conan2\p\grpc8b6c73b4e5756\p\lib\grpc++.lib C$:\Users\wsd07\.conan2\p\grpc8b6c73b4e5756\p\lib\grpc_authorization_provider.lib C$:\Users\wsd07\.conan2\p\grpc8b6c73b4e5756\p\lib\grpc_unsecure.lib C$:\Users\wsd07\.conan2\p\grpc8b6c73b4e5756\p\lib\grpc.lib C$:\Users\wsd07\.conan2\p\re24eb2cefbcf664\p\lib\re2.lib C$:\Users\wsd07\.conan2\p\absei6a15444d60d47\p\lib\absl_statusor.lib C$:\Users\wsd07\.conan2\p\c-are581008031a482\p\lib\cares.lib C$:\Users\wsd07\.conan2\p\opens13d3a0b103336\p\lib\libssl.lib C$:\Users\wsd07\.conan2\p\opens13d3a0b103336\p\lib\libcrypto.lib C$:\Users\wsd07\.conan2\p\grpc8b6c73b4e5756\p\lib\upb.lib C$:\Users\wsd07\.conan2\p\grpc8b6c73b4e5756\p\lib\grpc_plugin_support.lib C$:\Users\wsd07\.conan2\p\proto47a676cb9257b\p\lib\libprotoc.lib C$:\Users\wsd07\.conan2\p\grpc8b6c73b4e5756\p\lib\gpr.lib C$:\Users\wsd07\.conan2\p\absei6a15444d60d47\p\lib\absl_flags.lib C$:\Users\wsd07\.conan2\p\absei6a15444d60d47\p\lib\absl_flags_internal.lib C$:\Users\wsd07\.conan2\p\absei6a15444d60d47\p\lib\absl_flags_reflection.lib C$:\Users\wsd07\.conan2\p\absei6a15444d60d47\p\lib\absl_raw_hash_set.lib C$:\Users\wsd07\.conan2\p\absei6a15444d60d47\p\lib\absl_hash.lib C$:\Users\wsd07\.conan2\p\absei6a15444d60d47\p\lib\absl_city.lib C$:\Users\wsd07\.conan2\p\absei6a15444d60d47\p\lib\absl_low_level_hash.lib C$:\Users\wsd07\.conan2\p\absei6a15444d60d47\p\lib\absl_hashtablez_sampler.lib C$:\Users\wsd07\.conan2\p\absei6a15444d60d47\p\lib\absl_flags_config.lib C$:\Users\wsd07\.conan2\p\absei6a15444d60d47\p\lib\absl_flags_program_name.lib C$:\Users\wsd07\.conan2\p\absei6a15444d60d47\p\lib\absl_flags_private_handle_accessor.lib C$:\Users\wsd07\.conan2\p\absei6a15444d60d47\p\lib\absl_flags_commandlineflag.lib C$:\Users\wsd07\.conan2\p\absei6a15444d60d47\p\lib\absl_flags_commandlineflag_internal.lib C$:\Users\wsd07\.conan2\p\absei6a15444d60d47\p\lib\absl_flags_marshalling.lib C$:\Users\wsd07\.conan2\p\absei6a15444d60d47\p\lib\absl_random_distributions.lib C$:\Users\wsd07\.conan2\p\absei6a15444d60d47\p\lib\absl_random_seed_sequences.lib C$:\Users\wsd07\.conan2\p\absei6a15444d60d47\p\lib\absl_random_internal_pool_urbg.lib C$:\Users\wsd07\.conan2\p\absei6a15444d60d47\p\lib\absl_random_internal_randen.lib C$:\Users\wsd07\.conan2\p\absei6a15444d60d47\p\lib\absl_random_internal_randen_hwaes.lib C$:\Users\wsd07\.conan2\p\absei6a15444d60d47\p\lib\absl_random_internal_randen_hwaes_impl.lib C$:\Users\wsd07\.conan2\p\absei6a15444d60d47\p\lib\absl_random_internal_randen_slow.lib C$:\Users\wsd07\.conan2\p\absei6a15444d60d47\p\lib\absl_random_internal_platform.lib C$:\Users\wsd07\.conan2\p\absei6a15444d60d47\p\lib\absl_random_internal_seed_material.lib C$:\Users\wsd07\.conan2\p\absei6a15444d60d47\p\lib\absl_random_seed_gen_exception.lib C$:\Users\wsd07\.conan2\p\absei6a15444d60d47\p\lib\absl_status.lib C$:\Users\wsd07\.conan2\p\absei6a15444d60d47\p\lib\absl_strerror.lib C$:\Users\wsd07\.conan2\p\absei6a15444d60d47\p\lib\absl_cord.lib C$:\Users\wsd07\.conan2\p\absei6a15444d60d47\p\lib\absl_cordz_info.lib C$:\Users\wsd07\.conan2\p\absei6a15444d60d47\p\lib\absl_cord_internal.lib C$:\Users\wsd07\.conan2\p\absei6a15444d60d47\p\lib\absl_cordz_functions.lib C$:\Users\wsd07\.conan2\p\absei6a15444d60d47\p\lib\absl_exponential_biased.lib C$:\Users\wsd07\.conan2\p\absei6a15444d60d47\p\lib\absl_cordz_handle.lib C$:\Users\wsd07\.conan2\p\absei6a15444d60d47\p\lib\absl_crc_cord_state.lib C$:\Users\wsd07\.conan2\p\absei6a15444d60d47\p\lib\absl_crc32c.lib C$:\Users\wsd07\.conan2\p\absei6a15444d60d47\p\lib\absl_crc_internal.lib C$:\Users\wsd07\.conan2\p\absei6a15444d60d47\p\lib\absl_crc_cpu_detect.lib C$:\Users\wsd07\.conan2\p\absei6a15444d60d47\p\lib\absl_str_format_internal.lib C$:\Users\wsd07\.conan2\p\absei6a15444d60d47\p\lib\absl_synchronization.lib C$:\Users\wsd07\.conan2\p\absei6a15444d60d47\p\lib\absl_stacktrace.lib C$:\Users\wsd07\.conan2\p\absei6a15444d60d47\p\lib\absl_symbolize.lib C$:\Users\wsd07\.conan2\p\absei6a15444d60d47\p\lib\absl_debugging_internal.lib C$:\Users\wsd07\.conan2\p\absei6a15444d60d47\p\lib\absl_demangle_internal.lib C$:\Users\wsd07\.conan2\p\absei6a15444d60d47\p\lib\absl_graphcycles_internal.lib C$:\Users\wsd07\.conan2\p\absei6a15444d60d47\p\lib\absl_kernel_timeout_internal.lib C$:\Users\wsd07\.conan2\p\absei6a15444d60d47\p\lib\absl_malloc_internal.lib C$:\Users\wsd07\.conan2\p\absei6a15444d60d47\p\lib\absl_time.lib C$:\Users\wsd07\.conan2\p\absei6a15444d60d47\p\lib\absl_strings.lib C$:\Users\wsd07\.conan2\p\absei6a15444d60d47\p\lib\absl_string_view.lib C$:\Users\wsd07\.conan2\p\absei6a15444d60d47\p\lib\absl_throw_delegate.lib C$:\Users\wsd07\.conan2\p\absei6a15444d60d47\p\lib\absl_strings_internal.lib C$:\Users\wsd07\.conan2\p\absei6a15444d60d47\p\lib\absl_base.lib C$:\Users\wsd07\.conan2\p\absei6a15444d60d47\p\lib\absl_spinlock_wait.lib C$:\Users\wsd07\.conan2\p\absei6a15444d60d47\p\lib\absl_int128.lib C$:\Users\wsd07\.conan2\p\absei6a15444d60d47\p\lib\absl_civil_time.lib C$:\Users\wsd07\.conan2\p\absei6a15444d60d47\p\lib\absl_time_zone.lib C$:\Users\wsd07\.conan2\p\absei6a15444d60d47\p\lib\absl_bad_optional_access.lib C$:\Users\wsd07\.conan2\p\absei6a15444d60d47\p\lib\absl_bad_variant_access.lib C$:\Users\wsd07\.conan2\p\absei6a15444d60d47\p\lib\absl_raw_logging_internal.lib C$:\Users\wsd07\.conan2\p\absei6a15444d60d47\p\lib\absl_log_severity.lib C$:\Users\wsd07\.conan2\p\grpc8b6c73b4e5756\p\lib\address_sorting.lib C$:\Users\wsd07\.conan2\p\proto47a676cb9257b\p\lib\libprotobuf.lib C$:\Users\wsd07\.conan2\p\zlibcfb9789dc2a53\p\lib\zlib.lib || _CuraEngine.lib
  CONFIG = Release
  FLAGS = /DWIN32 /D_WINDOWS /EHsc /O2 /Ob2 /DNDEBUG -MD
  LINK_FLAGS = /machine:x64 /INCREMENTAL:NO /subsystem:console
  LINK_LIBRARIES = _CuraEngine.lib  C:\Users\<USER>\.conan2\p\b\arcusee7666f716199\p\lib\Arcus.lib  C:\Users\<USER>\.conan2\p\spdlo2d4b540ab0f22\p\lib\spdlog.lib  C:\Users\<USER>\.conan2\p\fmtdb20aad6e1bd4\p\lib\fmt.lib  C:\Users\<USER>\.conan2\p\b\clippf8ddcd4a5d961\p\lib\polyclipping.lib  C:\Users\<USER>\.conan2\p\boosta79d738920bc3\p\lib\libboost_log_setup.lib  C:\Users\<USER>\.conan2\p\boosta79d738920bc3\p\lib\libboost_unit_test_framework.lib  C:\Users\<USER>\.conan2\p\boosta79d738920bc3\p\lib\libboost_type_erasure.lib  C:\Users\<USER>\.conan2\p\boosta79d738920bc3\p\lib\libboost_log.lib  C:\Users\<USER>\.conan2\p\boosta79d738920bc3\p\lib\libboost_locale.lib  C:\Users\<USER>\.conan2\p\boosta79d738920bc3\p\lib\libboost_contract.lib  C:\Users\<USER>\.conan2\p\boosta79d738920bc3\p\lib\libboost_wave.lib  C:\Users\<USER>\.conan2\p\boosta79d738920bc3\p\lib\libboost_thread.lib  C:\Users\<USER>\.conan2\p\boosta79d738920bc3\p\lib\libboost_test_exec_monitor.lib  C:\Users\<USER>\.conan2\p\boosta79d738920bc3\p\lib\libboost_prg_exec_monitor.lib  C:\Users\<USER>\.conan2\p\boosta79d738920bc3\p\lib\libboost_nowide.lib  C:\Users\<USER>\.conan2\p\boosta79d738920bc3\p\lib\libboost_iostreams.lib  C:\Users\<USER>\.conan2\p\bzip2e1752ec0a98ec\p\lib\bz2.lib  C:\Users\<USER>\.conan2\p\boosta79d738920bc3\p\lib\libboost_graph.lib  C:\Users\<USER>\.conan2\p\boosta79d738920bc3\p\lib\libboost_wserialization.lib  C:\Users\<USER>\.conan2\p\boosta79d738920bc3\p\lib\libboost_url.lib  C:\Users\<USER>\.conan2\p\boosta79d738920bc3\p\lib\libboost_stacktrace_windbg_cached.lib  C:\Users\<USER>\.conan2\p\boosta79d738920bc3\p\lib\libboost_stacktrace_windbg.lib  ole32.lib  dbgeng.lib  C:\Users\<USER>\.conan2\p\boosta79d738920bc3\p\lib\libboost_stacktrace_noop.lib  C:\Users\<USER>\.conan2\p\boosta79d738920bc3\p\lib\libboost_stacktrace_from_exception.lib  C:\Users\<USER>\.conan2\p\boosta79d738920bc3\p\lib\libboost_random.lib  C:\Users\<USER>\.conan2\p\boosta79d738920bc3\p\lib\libboost_math_tr1l.lib  C:\Users\<USER>\.conan2\p\boosta79d738920bc3\p\lib\libboost_math_tr1f.lib  C:\Users\<USER>\.conan2\p\boosta79d738920bc3\p\lib\libboost_math_tr1.lib  C:\Users\<USER>\.conan2\p\boosta79d738920bc3\p\lib\libboost_math_c99l.lib  C:\Users\<USER>\.conan2\p\boosta79d738920bc3\p\lib\libboost_math_c99f.lib  C:\Users\<USER>\.conan2\p\boosta79d738920bc3\p\lib\libboost_math_c99.lib  C:\Users\<USER>\.conan2\p\boosta79d738920bc3\p\lib\libboost_json.lib  C:\Users\<USER>\.conan2\p\boosta79d738920bc3\p\lib\libboost_filesystem.lib  C:\Users\<USER>\.conan2\p\boosta79d738920bc3\p\lib\libboost_coroutine.lib  C:\Users\<USER>\.conan2\p\boosta79d738920bc3\p\lib\libboost_chrono.lib  C:\Users\<USER>\.conan2\p\boosta79d738920bc3\p\lib\libboost_timer.lib  C:\Users\<USER>\.conan2\p\boosta79d738920bc3\p\lib\libboost_serialization.lib  C:\Users\<USER>\.conan2\p\boosta79d738920bc3\p\lib\libboost_regex.lib  C:\Users\<USER>\.conan2\p\boosta79d738920bc3\p\lib\libboost_program_options.lib  C:\Users\<USER>\.conan2\p\boosta79d738920bc3\p\lib\libboost_exception.lib  C:\Users\<USER>\.conan2\p\boosta79d738920bc3\p\lib\libboost_date_time.lib  C:\Users\<USER>\.conan2\p\boosta79d738920bc3\p\lib\libboost_context.lib  C:\Users\<USER>\.conan2\p\boosta79d738920bc3\p\lib\libboost_container.lib  C:\Users\<USER>\.conan2\p\boosta79d738920bc3\p\lib\libboost_charconv.lib  C:\Users\<USER>\.conan2\p\boosta79d738920bc3\p\lib\libboost_atomic.lib  C:\Users\<USER>\.conan2\p\b\curaebb245e64b5ac7\p\lib\curaengine_grpc_definitions.lib  C:\Users\<USER>\.conan2\p\grpc8b6c73b4e5756\p\lib\grpcpp_channelz.lib  C:\Users\<USER>\.conan2\p\grpc8b6c73b4e5756\p\lib\grpc++_reflection.lib  C:\Users\<USER>\.conan2\p\grpc8b6c73b4e5756\p\lib\grpc++_error_details.lib  C:\Users\<USER>\.conan2\p\grpc8b6c73b4e5756\p\lib\grpc++_alts.lib  C:\Users\<USER>\.conan2\p\grpc8b6c73b4e5756\p\lib\grpc++_unsecure.lib  C:\Users\<USER>\.conan2\p\grpc8b6c73b4e5756\p\lib\grpc++.lib  C:\Users\<USER>\.conan2\p\grpc8b6c73b4e5756\p\lib\grpc_authorization_provider.lib  C:\Users\<USER>\.conan2\p\grpc8b6c73b4e5756\p\lib\grpc_unsecure.lib  C:\Users\<USER>\.conan2\p\grpc8b6c73b4e5756\p\lib\grpc.lib  C:\Users\<USER>\.conan2\p\re24eb2cefbcf664\p\lib\re2.lib  C:\Users\<USER>\.conan2\p\absei6a15444d60d47\p\lib\absl_statusor.lib  C:\Users\<USER>\.conan2\p\c-are581008031a482\p\lib\cares.lib  iphlpapi.lib  C:\Users\<USER>\.conan2\p\opens13d3a0b103336\p\lib\libssl.lib  C:\Users\<USER>\.conan2\p\opens13d3a0b103336\p\lib\libcrypto.lib  user32.lib  C:\Users\<USER>\.conan2\p\grpc8b6c73b4e5756\p\lib\upb.lib  C:\Users\<USER>\.conan2\p\grpc8b6c73b4e5756\p\lib\grpc_plugin_support.lib  C:\Users\<USER>\.conan2\p\proto47a676cb9257b\p\lib\libprotoc.lib  C:\Users\<USER>\.conan2\p\grpc8b6c73b4e5756\p\lib\gpr.lib  C:\Users\<USER>\.conan2\p\absei6a15444d60d47\p\lib\absl_flags.lib  C:\Users\<USER>\.conan2\p\absei6a15444d60d47\p\lib\absl_flags_internal.lib  C:\Users\<USER>\.conan2\p\absei6a15444d60d47\p\lib\absl_flags_reflection.lib  C:\Users\<USER>\.conan2\p\absei6a15444d60d47\p\lib\absl_raw_hash_set.lib  C:\Users\<USER>\.conan2\p\absei6a15444d60d47\p\lib\absl_hash.lib  C:\Users\<USER>\.conan2\p\absei6a15444d60d47\p\lib\absl_city.lib  C:\Users\<USER>\.conan2\p\absei6a15444d60d47\p\lib\absl_low_level_hash.lib  C:\Users\<USER>\.conan2\p\absei6a15444d60d47\p\lib\absl_hashtablez_sampler.lib  C:\Users\<USER>\.conan2\p\absei6a15444d60d47\p\lib\absl_flags_config.lib  C:\Users\<USER>\.conan2\p\absei6a15444d60d47\p\lib\absl_flags_program_name.lib  C:\Users\<USER>\.conan2\p\absei6a15444d60d47\p\lib\absl_flags_private_handle_accessor.lib  C:\Users\<USER>\.conan2\p\absei6a15444d60d47\p\lib\absl_flags_commandlineflag.lib  C:\Users\<USER>\.conan2\p\absei6a15444d60d47\p\lib\absl_flags_commandlineflag_internal.lib  C:\Users\<USER>\.conan2\p\absei6a15444d60d47\p\lib\absl_flags_marshalling.lib  C:\Users\<USER>\.conan2\p\absei6a15444d60d47\p\lib\absl_random_distributions.lib  C:\Users\<USER>\.conan2\p\absei6a15444d60d47\p\lib\absl_random_seed_sequences.lib  C:\Users\<USER>\.conan2\p\absei6a15444d60d47\p\lib\absl_random_internal_pool_urbg.lib  C:\Users\<USER>\.conan2\p\absei6a15444d60d47\p\lib\absl_random_internal_randen.lib  C:\Users\<USER>\.conan2\p\absei6a15444d60d47\p\lib\absl_random_internal_randen_hwaes.lib  C:\Users\<USER>\.conan2\p\absei6a15444d60d47\p\lib\absl_random_internal_randen_hwaes_impl.lib  C:\Users\<USER>\.conan2\p\absei6a15444d60d47\p\lib\absl_random_internal_randen_slow.lib  C:\Users\<USER>\.conan2\p\absei6a15444d60d47\p\lib\absl_random_internal_platform.lib  C:\Users\<USER>\.conan2\p\absei6a15444d60d47\p\lib\absl_random_internal_seed_material.lib  bcrypt.lib  C:\Users\<USER>\.conan2\p\absei6a15444d60d47\p\lib\absl_random_seed_gen_exception.lib  C:\Users\<USER>\.conan2\p\absei6a15444d60d47\p\lib\absl_status.lib  C:\Users\<USER>\.conan2\p\absei6a15444d60d47\p\lib\absl_strerror.lib  C:\Users\<USER>\.conan2\p\absei6a15444d60d47\p\lib\absl_cord.lib  C:\Users\<USER>\.conan2\p\absei6a15444d60d47\p\lib\absl_cordz_info.lib  C:\Users\<USER>\.conan2\p\absei6a15444d60d47\p\lib\absl_cord_internal.lib  C:\Users\<USER>\.conan2\p\absei6a15444d60d47\p\lib\absl_cordz_functions.lib  C:\Users\<USER>\.conan2\p\absei6a15444d60d47\p\lib\absl_exponential_biased.lib  C:\Users\<USER>\.conan2\p\absei6a15444d60d47\p\lib\absl_cordz_handle.lib  C:\Users\<USER>\.conan2\p\absei6a15444d60d47\p\lib\absl_crc_cord_state.lib  C:\Users\<USER>\.conan2\p\absei6a15444d60d47\p\lib\absl_crc32c.lib  C:\Users\<USER>\.conan2\p\absei6a15444d60d47\p\lib\absl_crc_internal.lib  C:\Users\<USER>\.conan2\p\absei6a15444d60d47\p\lib\absl_crc_cpu_detect.lib  C:\Users\<USER>\.conan2\p\absei6a15444d60d47\p\lib\absl_str_format_internal.lib  C:\Users\<USER>\.conan2\p\absei6a15444d60d47\p\lib\absl_synchronization.lib  C:\Users\<USER>\.conan2\p\absei6a15444d60d47\p\lib\absl_stacktrace.lib  C:\Users\<USER>\.conan2\p\absei6a15444d60d47\p\lib\absl_symbolize.lib  C:\Users\<USER>\.conan2\p\absei6a15444d60d47\p\lib\absl_debugging_internal.lib  dbghelp.lib  C:\Users\<USER>\.conan2\p\absei6a15444d60d47\p\lib\absl_demangle_internal.lib  C:\Users\<USER>\.conan2\p\absei6a15444d60d47\p\lib\absl_graphcycles_internal.lib  C:\Users\<USER>\.conan2\p\absei6a15444d60d47\p\lib\absl_kernel_timeout_internal.lib  C:\Users\<USER>\.conan2\p\absei6a15444d60d47\p\lib\absl_malloc_internal.lib  C:\Users\<USER>\.conan2\p\absei6a15444d60d47\p\lib\absl_time.lib  C:\Users\<USER>\.conan2\p\absei6a15444d60d47\p\lib\absl_strings.lib  C:\Users\<USER>\.conan2\p\absei6a15444d60d47\p\lib\absl_string_view.lib  C:\Users\<USER>\.conan2\p\absei6a15444d60d47\p\lib\absl_throw_delegate.lib  C:\Users\<USER>\.conan2\p\absei6a15444d60d47\p\lib\absl_strings_internal.lib  C:\Users\<USER>\.conan2\p\absei6a15444d60d47\p\lib\absl_base.lib  advapi32.lib  C:\Users\<USER>\.conan2\p\absei6a15444d60d47\p\lib\absl_spinlock_wait.lib  C:\Users\<USER>\.conan2\p\absei6a15444d60d47\p\lib\absl_int128.lib  C:\Users\<USER>\.conan2\p\absei6a15444d60d47\p\lib\absl_civil_time.lib  C:\Users\<USER>\.conan2\p\absei6a15444d60d47\p\lib\absl_time_zone.lib  C:\Users\<USER>\.conan2\p\absei6a15444d60d47\p\lib\absl_bad_optional_access.lib  C:\Users\<USER>\.conan2\p\absei6a15444d60d47\p\lib\absl_bad_variant_access.lib  C:\Users\<USER>\.conan2\p\absei6a15444d60d47\p\lib\absl_raw_logging_internal.lib  C:\Users\<USER>\.conan2\p\absei6a15444d60d47\p\lib\absl_log_severity.lib  C:\Users\<USER>\.conan2\p\grpc8b6c73b4e5756\p\lib\address_sorting.lib  crypt32.lib  ws2_32.lib  wsock32.lib  C:\Users\<USER>\.conan2\p\proto47a676cb9257b\p\lib\libprotobuf.lib  C:\Users\<USER>\.conan2\p\zlibcfb9789dc2a53\p\lib\zlib.lib  kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib
  LINK_PATH = -LIBPATH:C:\Users\<USER>\.conan2\p\b\arcusee7666f716199\p\lib   -LIBPATH:C:\Users\<USER>\.conan2\p\proto47a676cb9257b\p\lib   -LIBPATH:C:\Users\<USER>\.conan2\p\zlibcfb9789dc2a53\p\lib   -LIBPATH:C:\Users\<USER>\.conan2\p\spdlo2d4b540ab0f22\p\lib   -LIBPATH:C:\Users\<USER>\.conan2\p\fmtdb20aad6e1bd4\p\lib   -LIBPATH:C:\Users\<USER>\.conan2\p\range0301bf3d76d5d\p\lib   -LIBPATH:C:\Users\<USER>\.conan2\p\b\clippf8ddcd4a5d961\p\lib   -LIBPATH:C:\Users\<USER>\.conan2\p\boosta79d738920bc3\p\lib   -LIBPATH:C:\Users\<USER>\.conan2\p\bzip2e1752ec0a98ec\p\lib   -LIBPATH:C:\Users\<USER>\.conan2\p\b\curaebb245e64b5ac7\p\lib   -LIBPATH:C:\Users\<USER>\.conan2\p\grpc8b6c73b4e5756\p\lib   -LIBPATH:C:\Users\<USER>\.conan2\p\c-are581008031a482\p\lib   -LIBPATH:C:\Users\<USER>\.conan2\p\re24eb2cefbcf664\p\lib   -LIBPATH:C:\Users\<USER>\.conan2\p\absei6a15444d60d47\p\lib   -LIBPATH:C:\Users\<USER>\.conan2\p\opens13d3a0b103336\p\lib
  OBJECT_DIR = CMakeFiles\CuraEngine.dir
  POST_BUILD = cd .
  PRE_LINK = cd .
  TARGET_COMPILE_PDB = CMakeFiles\CuraEngine.dir\
  TARGET_FILE = CuraEngine.exe
  TARGET_IMPLIB = CuraEngine.lib
  TARGET_PDB = CuraEngine.pdb
  RSP_FILE = CMakeFiles\CuraEngine.rsp


#############################################
# Utility command for edit_cache

build CMakeFiles\edit_cache.util: CUSTOM_COMMAND
  COMMAND = C:\WINDOWS\system32\cmd.exe /C "cd /D C:\Users\<USER>\vscode\Cura-Dev\CuraEngine\build\Release && "C:\Program Files\CMake\bin\cmake-gui.exe" -SC:\Users\<USER>\vscode\Cura-Dev\CuraEngine -BC:\Users\<USER>\vscode\Cura-Dev\CuraEngine\build\Release"
  DESC = Running CMake cache editor...
  pool = console
  restat = 1

build edit_cache: phony CMakeFiles\edit_cache.util


#############################################
# Utility command for rebuild_cache

build CMakeFiles\rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = C:\WINDOWS\system32\cmd.exe /C "cd /D C:\Users\<USER>\vscode\Cura-Dev\CuraEngine\build\Release && "C:\Program Files\CMake\bin\cmake.exe" --regenerate-during-build -SC:\Users\<USER>\vscode\Cura-Dev\CuraEngine -BC:\Users\<USER>\vscode\Cura-Dev\CuraEngine\build\Release"
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build rebuild_cache: phony CMakeFiles\rebuild_cache.util


#############################################
# Custom command for Cura.pb.h

build Cura.pb.h Cura.pb.cc | ${cmake_ninja_workdir}Cura.pb.h ${cmake_ninja_workdir}Cura.pb.cc: CUSTOM_COMMAND C$:\Users\wsd07\vscode\Cura-Dev\CuraEngine\Cura.proto C$:\Users\wsd07\.conan2\p\proto47a676cb9257b\p\bin\protoc.exe
  COMMAND = C:\WINDOWS\system32\cmd.exe /C "cd /D C:\Users\<USER>\vscode\Cura-Dev\CuraEngine\build\Release && C:\Users\<USER>\.conan2\p\proto47a676cb9257b\p\bin\protoc.exe --cpp_out :C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/build/Release -I C:/Users/<USER>/vscode/Cura-Dev/CuraEngine C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/Cura.proto"
  DESC = Running cpp protocol buffer compiler on Cura.proto
  restat = 1

# =============================================================================
# Target aliases.

build CuraEngine: phony CuraEngine.exe

build _CuraEngine: phony _CuraEngine.lib

# =============================================================================
# Folder targets.

# =============================================================================

#############################################
# Folder: C:/Users/<USER>/vscode/Cura-Dev/CuraEngine/build/Release

build all: phony _CuraEngine.lib CuraEngine.exe

# =============================================================================
# Built-in targets


#############################################
# Re-run CMake if any of its inputs changed.

build build.ninja C$:\Users\wsd07\vscode\Cura-Dev\CuraEngine\build\Release\cmake_install.cmake: RERUN_CMAKE | C$:\Program$ Files\CMake\share\cmake-4.1\Modules\CMakeCCompiler.cmake.in C$:\Program$ Files\CMake\share\cmake-4.1\Modules\CMakeCCompilerABI.c C$:\Program$ Files\CMake\share\cmake-4.1\Modules\CMakeCInformation.cmake C$:\Program$ Files\CMake\share\cmake-4.1\Modules\CMakeCXXCompiler.cmake.in C$:\Program$ Files\CMake\share\cmake-4.1\Modules\CMakeCXXCompilerABI.cpp C$:\Program$ Files\CMake\share\cmake-4.1\Modules\CMakeCXXInformation.cmake C$:\Program$ Files\CMake\share\cmake-4.1\Modules\CMakeCommonLanguageInclude.cmake C$:\Program$ Files\CMake\share\cmake-4.1\Modules\CMakeCompilerIdDetection.cmake C$:\Program$ Files\CMake\share\cmake-4.1\Modules\CMakeDetermineCCompiler.cmake C$:\Program$ Files\CMake\share\cmake-4.1\Modules\CMakeDetermineCXXCompiler.cmake C$:\Program$ Files\CMake\share\cmake-4.1\Modules\CMakeDetermineCompiler.cmake C$:\Program$ Files\CMake\share\cmake-4.1\Modules\CMakeDetermineCompilerABI.cmake C$:\Program$ Files\CMake\share\cmake-4.1\Modules\CMakeDetermineCompilerId.cmake C$:\Program$ Files\CMake\share\cmake-4.1\Modules\CMakeDetermineCompilerSupport.cmake C$:\Program$ Files\CMake\share\cmake-4.1\Modules\CMakeDetermineRCCompiler.cmake C$:\Program$ Files\CMake\share\cmake-4.1\Modules\CMakeDetermineSystem.cmake C$:\Program$ Files\CMake\share\cmake-4.1\Modules\CMakeFindBinUtils.cmake C$:\Program$ Files\CMake\share\cmake-4.1\Modules\CMakeFindDependencyMacro.cmake C$:\Program$ Files\CMake\share\cmake-4.1\Modules\CMakeGenericSystem.cmake C$:\Program$ Files\CMake\share\cmake-4.1\Modules\CMakeInitializeConfigs.cmake C$:\Program$ Files\CMake\share\cmake-4.1\Modules\CMakeLanguageInformation.cmake C$:\Program$ Files\CMake\share\cmake-4.1\Modules\CMakeNinjaFindMake.cmake C$:\Program$ Files\CMake\share\cmake-4.1\Modules\CMakeParseArguments.cmake C$:\Program$ Files\CMake\share\cmake-4.1\Modules\CMakeParseImplicitIncludeInfo.cmake C$:\Program$ Files\CMake\share\cmake-4.1\Modules\CMakeParseImplicitLinkInfo.cmake C$:\Program$ Files\CMake\share\cmake-4.1\Modules\CMakeParseLibraryArchitecture.cmake C$:\Program$ Files\CMake\share\cmake-4.1\Modules\CMakeRCCompiler.cmake.in C$:\Program$ Files\CMake\share\cmake-4.1\Modules\CMakeRCInformation.cmake C$:\Program$ Files\CMake\share\cmake-4.1\Modules\CMakeSystem.cmake.in C$:\Program$ Files\CMake\share\cmake-4.1\Modules\CMakeSystemSpecificInformation.cmake C$:\Program$ Files\CMake\share\cmake-4.1\Modules\CMakeSystemSpecificInitialize.cmake C$:\Program$ Files\CMake\share\cmake-4.1\Modules\CMakeTestCCompiler.cmake C$:\Program$ Files\CMake\share\cmake-4.1\Modules\CMakeTestCXXCompiler.cmake C$:\Program$ Files\CMake\share\cmake-4.1\Modules\CMakeTestCompilerCommon.cmake C$:\Program$ Files\CMake\share\cmake-4.1\Modules\CMakeTestRCCompiler.cmake C$:\Program$ Files\CMake\share\cmake-4.1\Modules\CheckCSourceCompiles.cmake C$:\Program$ Files\CMake\share\cmake-4.1\Modules\CheckIncludeFile.cmake C$:\Program$ Files\CMake\share\cmake-4.1\Modules\CheckLibraryExists.cmake C$:\Program$ Files\CMake\share\cmake-4.1\Modules\Compiler\ADSP-DetermineCompiler.cmake C$:\Program$ Files\CMake\share\cmake-4.1\Modules\Compiler\ARMCC-DetermineCompiler.cmake C$:\Program$ Files\CMake\share\cmake-4.1\Modules\Compiler\ARMClang-DetermineCompiler.cmake C$:\Program$ Files\CMake\share\cmake-4.1\Modules\Compiler\AppleClang-DetermineCompiler.cmake C$:\Program$ Files\CMake\share\cmake-4.1\Modules\Compiler\Borland-DetermineCompiler.cmake C$:\Program$ Files\CMake\share\cmake-4.1\Modules\Compiler\Bruce-C-DetermineCompiler.cmake C$:\Program$ Files\CMake\share\cmake-4.1\Modules\Compiler\CMakeCommonCompilerMacros.cmake C$:\Program$ Files\CMake\share\cmake-4.1\Modules\Compiler\Clang-DetermineCompiler.cmake C$:\Program$ Files\CMake\share\cmake-4.1\Modules\Compiler\Clang-DetermineCompilerInternal.cmake C$:\Program$ Files\CMake\share\cmake-4.1\Modules\Compiler\Compaq-C-DetermineCompiler.cmake C$:\Program$ Files\CMake\share\cmake-4.1\Modules\Compiler\Compaq-CXX-DetermineCompiler.cmake C$:\Program$ Files\CMake\share\cmake-4.1\Modules\Compiler\Cray-DetermineCompiler.cmake C$:\Program$ Files\CMake\share\cmake-4.1\Modules\Compiler\CrayClang-DetermineCompiler.cmake C$:\Program$ Files\CMake\share\cmake-4.1\Modules\Compiler\Diab-DetermineCompiler.cmake C$:\Program$ Files\CMake\share\cmake-4.1\Modules\Compiler\Embarcadero-DetermineCompiler.cmake C$:\Program$ Files\CMake\share\cmake-4.1\Modules\Compiler\Fujitsu-DetermineCompiler.cmake C$:\Program$ Files\CMake\share\cmake-4.1\Modules\Compiler\FujitsuClang-DetermineCompiler.cmake C$:\Program$ Files\CMake\share\cmake-4.1\Modules\Compiler\GHS-DetermineCompiler.cmake C$:\Program$ Files\CMake\share\cmake-4.1\Modules\Compiler\GNU-C-DetermineCompiler.cmake C$:\Program$ Files\CMake\share\cmake-4.1\Modules\Compiler\GNU-CXX-DetermineCompiler.cmake C$:\Program$ Files\CMake\share\cmake-4.1\Modules\Compiler\HP-C-DetermineCompiler.cmake C$:\Program$ Files\CMake\share\cmake-4.1\Modules\Compiler\HP-CXX-DetermineCompiler.cmake C$:\Program$ Files\CMake\share\cmake-4.1\Modules\Compiler\IAR-DetermineCompiler.cmake C$:\Program$ Files\CMake\share\cmake-4.1\Modules\Compiler\IBMCPP-C-DetermineVersionInternal.cmake C$:\Program$ Files\CMake\share\cmake-4.1\Modules\Compiler\IBMCPP-CXX-DetermineVersionInternal.cmake C$:\Program$ Files\CMake\share\cmake-4.1\Modules\Compiler\IBMClang-C-DetermineCompiler.cmake C$:\Program$ Files\CMake\share\cmake-4.1\Modules\Compiler\IBMClang-CXX-DetermineCompiler.cmake C$:\Program$ Files\CMake\share\cmake-4.1\Modules\Compiler\Intel-DetermineCompiler.cmake C$:\Program$ Files\CMake\share\cmake-4.1\Modules\Compiler\IntelLLVM-DetermineCompiler.cmake C$:\Program$ Files\CMake\share\cmake-4.1\Modules\Compiler\LCC-C-DetermineCompiler.cmake C$:\Program$ Files\CMake\share\cmake-4.1\Modules\Compiler\LCC-CXX-DetermineCompiler.cmake C$:\Program$ Files\CMake\share\cmake-4.1\Modules\Compiler\MSVC-C.cmake C$:\Program$ Files\CMake\share\cmake-4.1\Modules\Compiler\MSVC-CXX-CXXImportStd.cmake C$:\Program$ Files\CMake\share\cmake-4.1\Modules\Compiler\MSVC-CXX.cmake C$:\Program$ Files\CMake\share\cmake-4.1\Modules\Compiler\MSVC-DetermineCompiler.cmake C$:\Program$ Files\CMake\share\cmake-4.1\Modules\Compiler\MSVC.cmake C$:\Program$ Files\CMake\share\cmake-4.1\Modules\Compiler\NVHPC-DetermineCompiler.cmake C$:\Program$ Files\CMake\share\cmake-4.1\Modules\Compiler\NVIDIA-DetermineCompiler.cmake C$:\Program$ Files\CMake\share\cmake-4.1\Modules\Compiler\OpenWatcom-DetermineCompiler.cmake C$:\Program$ Files\CMake\share\cmake-4.1\Modules\Compiler\OrangeC-DetermineCompiler.cmake C$:\Program$ Files\CMake\share\cmake-4.1\Modules\Compiler\PGI-DetermineCompiler.cmake C$:\Program$ Files\CMake\share\cmake-4.1\Modules\Compiler\PathScale-DetermineCompiler.cmake C$:\Program$ Files\CMake\share\cmake-4.1\Modules\Compiler\Renesas-DetermineCompiler.cmake C$:\Program$ Files\CMake\share\cmake-4.1\Modules\Compiler\SCO-DetermineCompiler.cmake C$:\Program$ Files\CMake\share\cmake-4.1\Modules\Compiler\SDCC-C-DetermineCompiler.cmake C$:\Program$ Files\CMake\share\cmake-4.1\Modules\Compiler\SunPro-C-DetermineCompiler.cmake C$:\Program$ Files\CMake\share\cmake-4.1\Modules\Compiler\SunPro-CXX-DetermineCompiler.cmake C$:\Program$ Files\CMake\share\cmake-4.1\Modules\Compiler\TI-DetermineCompiler.cmake C$:\Program$ Files\CMake\share\cmake-4.1\Modules\Compiler\TIClang-DetermineCompiler.cmake C$:\Program$ Files\CMake\share\cmake-4.1\Modules\Compiler\Tasking-DetermineCompiler.cmake C$:\Program$ Files\CMake\share\cmake-4.1\Modules\Compiler\TinyCC-C-DetermineCompiler.cmake C$:\Program$ Files\CMake\share\cmake-4.1\Modules\Compiler\VisualAge-C-DetermineCompiler.cmake C$:\Program$ Files\CMake\share\cmake-4.1\Modules\Compiler\VisualAge-CXX-DetermineCompiler.cmake C$:\Program$ Files\CMake\share\cmake-4.1\Modules\Compiler\Watcom-DetermineCompiler.cmake C$:\Program$ Files\CMake\share\cmake-4.1\Modules\Compiler\XL-C-DetermineCompiler.cmake C$:\Program$ Files\CMake\share\cmake-4.1\Modules\Compiler\XL-CXX-DetermineCompiler.cmake C$:\Program$ Files\CMake\share\cmake-4.1\Modules\Compiler\XLClang-C-DetermineCompiler.cmake C$:\Program$ Files\CMake\share\cmake-4.1\Modules\Compiler\XLClang-CXX-DetermineCompiler.cmake C$:\Program$ Files\CMake\share\cmake-4.1\Modules\Compiler\zOS-C-DetermineCompiler.cmake C$:\Program$ Files\CMake\share\cmake-4.1\Modules\Compiler\zOS-CXX-DetermineCompiler.cmake C$:\Program$ Files\CMake\share\cmake-4.1\Modules\FindPackageHandleStandardArgs.cmake C$:\Program$ Files\CMake\share\cmake-4.1\Modules\FindPackageMessage.cmake C$:\Program$ Files\CMake\share\cmake-4.1\Modules\FindThreads.cmake C$:\Program$ Files\CMake\share\cmake-4.1\Modules\GNUInstallDirs.cmake C$:\Program$ Files\CMake\share\cmake-4.1\Modules\Internal\CMakeCLinkerInformation.cmake C$:\Program$ Files\CMake\share\cmake-4.1\Modules\Internal\CMakeCXXLinkerInformation.cmake C$:\Program$ Files\CMake\share\cmake-4.1\Modules\Internal\CMakeCommonLinkerInformation.cmake C$:\Program$ Files\CMake\share\cmake-4.1\Modules\Internal\CMakeDetermineLinkerId.cmake C$:\Program$ Files\CMake\share\cmake-4.1\Modules\Internal\CMakeInspectCLinker.cmake C$:\Program$ Files\CMake\share\cmake-4.1\Modules\Internal\CMakeInspectCXXLinker.cmake C$:\Program$ Files\CMake\share\cmake-4.1\Modules\Internal\CheckSourceCompiles.cmake C$:\Program$ Files\CMake\share\cmake-4.1\Modules\Internal\FeatureTesting.cmake C$:\Program$ Files\CMake\share\cmake-4.1\Modules\Linker\MSVC-C.cmake C$:\Program$ Files\CMake\share\cmake-4.1\Modules\Linker\MSVC-CXX.cmake C$:\Program$ Files\CMake\share\cmake-4.1\Modules\Linker\MSVC.cmake C$:\Program$ Files\CMake\share\cmake-4.1\Modules\Platform\Linker\Windows-MSVC-C.cmake C$:\Program$ Files\CMake\share\cmake-4.1\Modules\Platform\Linker\Windows-MSVC-CXX.cmake C$:\Program$ Files\CMake\share\cmake-4.1\Modules\Platform\Linker\Windows-MSVC.cmake C$:\Program$ Files\CMake\share\cmake-4.1\Modules\Platform\Windows-Determine-CXX.cmake C$:\Program$ Files\CMake\share\cmake-4.1\Modules\Platform\Windows-Initialize.cmake C$:\Program$ Files\CMake\share\cmake-4.1\Modules\Platform\Windows-MSVC-C.cmake C$:\Program$ Files\CMake\share\cmake-4.1\Modules\Platform\Windows-MSVC-CXX.cmake C$:\Program$ Files\CMake\share\cmake-4.1\Modules\Platform\Windows-MSVC.cmake C$:\Program$ Files\CMake\share\cmake-4.1\Modules\Platform\Windows.cmake C$:\Program$ Files\CMake\share\cmake-4.1\Modules\Platform\WindowsPaths.cmake C$:\Users\wsd07\.conan2\p\asio-c31dc2a380bf0\p\lib\cmake\asio-grpc\AsioGrpcProtobufGenerator.cmake C$:\Users\wsd07\.conan2\p\bzip2e1752ec0a98ec\p\lib\cmake\conan-official-bzip2-variables.cmake C$:\Users\wsd07\.conan2\p\grpc8b6c73b4e5756\p\lib\cmake\conan_trick\grpc_cpp_plugin.cmake C$:\Users\wsd07\.conan2\p\opens13d3a0b103336\p\lib\cmake\conan-official-openssl-variables.cmake C$:\Users\wsd07\.conan2\p\proto47a676cb9257b\p\lib\cmake\protobuf\protobuf-conan-protoc-target.cmake C$:\Users\wsd07\.conan2\p\proto47a676cb9257b\p\lib\cmake\protobuf\protobuf-generate.cmake C$:\Users\wsd07\.conan2\p\proto47a676cb9257b\p\lib\cmake\protobuf\protobuf-module.cmake C$:\Users\wsd07\.conan2\p\proto47a676cb9257b\p\lib\cmake\protobuf\protobuf-options.cmake C$:\Users\wsd07\.conan2\p\standb1cf9a2ad7ef9\p\res\cmake\StandardProjectSettings.cmake C$:\Users\wsd07\vscode\Cura-Dev\CuraEngine\CMakeLists.txt CMakeCache.txt CMakeFiles\4.1.0-rc2\CMakeCCompiler.cmake CMakeFiles\4.1.0-rc2\CMakeCXXCompiler.cmake CMakeFiles\4.1.0-rc2\CMakeRCCompiler.cmake CMakeFiles\4.1.0-rc2\CMakeSystem.cmake generators\BZip2-Target-release.cmake generators\BZip2-release-x86_64-data.cmake generators\BZip2Config.cmake generators\BZip2ConfigVersion.cmake generators\BZip2Targets.cmake generators\Boost-Target-release.cmake generators\Boost-release-x86_64-data.cmake generators\BoostConfig.cmake generators\BoostConfigVersion.cmake generators\BoostTargets.cmake generators\OpenSSL-Target-release.cmake generators\OpenSSL-release-x86_64-data.cmake generators\OpenSSLConfig.cmake generators\OpenSSLConfigVersion.cmake generators\OpenSSLTargets.cmake generators\RapidJSON-Target-release.cmake generators\RapidJSON-release-x86_64-data.cmake generators\RapidJSONConfig.cmake generators\RapidJSONConfigVersion.cmake generators\RapidJSONTargets.cmake generators\ZLIB-Target-release.cmake generators\ZLIB-release-x86_64-data.cmake generators\ZLIBConfig.cmake generators\ZLIBConfigVersion.cmake generators\ZLIBTargets.cmake generators\absl-Target-release.cmake generators\absl-config-version.cmake generators\absl-config.cmake generators\absl-release-x86_64-data.cmake generators\abslTargets.cmake generators\arcus-Target-release.cmake generators\arcus-config-version.cmake generators\arcus-config.cmake generators\arcus-release-x86_64-data.cmake generators\arcusTargets.cmake generators\asio-grpc-Target-release.cmake generators\asio-grpc-config-version.cmake generators\asio-grpc-config.cmake generators\asio-grpc-release-x86_64-data.cmake generators\asio-grpcTargets.cmake generators\c-ares-Target-release.cmake generators\c-ares-config-version.cmake generators\c-ares-config.cmake generators\c-ares-release-x86_64-data.cmake generators\c-aresTargets.cmake generators\clipper-Target-release.cmake generators\clipper-config-version.cmake generators\clipper-config.cmake generators\clipper-release-x86_64-data.cmake generators\clipperTargets.cmake generators\cmakedeps_macros.cmake generators\conan_toolchain.cmake generators\curaengine_grpc_definitions-Target-release.cmake generators\curaengine_grpc_definitions-config-version.cmake generators\curaengine_grpc_definitions-config.cmake generators\curaengine_grpc_definitions-release-x86_64-data.cmake generators\curaengine_grpc_definitionsTargets.cmake generators\fmt-Target-release.cmake generators\fmt-config-version.cmake generators\fmt-config.cmake generators\fmt-release-x86_64-data.cmake generators\fmtTargets.cmake generators\gRPC-Target-release.cmake generators\gRPC-release-x86_64-data.cmake generators\gRPCConfig.cmake generators\gRPCConfigVersion.cmake generators\gRPCTargets.cmake generators\mapbox-geometry-Target-release.cmake generators\mapbox-geometry-config-version.cmake generators\mapbox-geometry-config.cmake generators\mapbox-geometry-release-x86_64-data.cmake generators\mapbox-geometryTargets.cmake generators\mapbox-variant-Target-release.cmake generators\mapbox-variant-config-version.cmake generators\mapbox-variant-config.cmake generators\mapbox-variant-release-x86_64-data.cmake generators\mapbox-variantTargets.cmake generators\mapbox-wagyu-Target-release.cmake generators\mapbox-wagyu-config-version.cmake generators\mapbox-wagyu-config.cmake generators\mapbox-wagyu-release-x86_64-data.cmake generators\mapbox-wagyuTargets.cmake generators\protobuf-Target-release.cmake generators\protobuf-config-version.cmake generators\protobuf-config.cmake generators\protobuf-release-x86_64-data.cmake generators\protobufTargets.cmake generators\range-v3-Target-release.cmake generators\range-v3-config-version.cmake generators\range-v3-config.cmake generators\range-v3-release-x86_64-data.cmake generators\range-v3Targets.cmake generators\re2-Target-release.cmake generators\re2-config-version.cmake generators\re2-config.cmake generators\re2-release-x86_64-data.cmake generators\re2Targets.cmake generators\scripta-Target-release.cmake generators\scripta-config-version.cmake generators\scripta-config.cmake generators\scripta-release-x86_64-data.cmake generators\scriptaTargets.cmake generators\semver-Target-release.cmake generators\semver-config-version.cmake generators\semver-config.cmake generators\semver-release-x86_64-data.cmake generators\semverTargets.cmake generators\spdlog-Target-release.cmake generators\spdlog-config-version.cmake generators\spdlog-config.cmake generators\spdlog-release-x86_64-data.cmake generators\spdlogTargets.cmake generators\standardprojectsettings-Target-release.cmake generators\standardprojectsettings-config-version.cmake generators\standardprojectsettings-config.cmake generators\standardprojectsettings-release-x86_64-data.cmake generators\standardprojectsettingsTargets.cmake generators\stb-Target-release.cmake generators\stb-config-version.cmake generators\stb-config.cmake generators\stb-release-x86_64-data.cmake generators\stbTargets.cmake
  pool = console


#############################################
# A missing CMake input file is not an error.

build C$:\Program$ Files\CMake\share\cmake-4.1\Modules\CMakeCCompiler.cmake.in C$:\Program$ Files\CMake\share\cmake-4.1\Modules\CMakeCCompilerABI.c C$:\Program$ Files\CMake\share\cmake-4.1\Modules\CMakeCInformation.cmake C$:\Program$ Files\CMake\share\cmake-4.1\Modules\CMakeCXXCompiler.cmake.in C$:\Program$ Files\CMake\share\cmake-4.1\Modules\CMakeCXXCompilerABI.cpp C$:\Program$ Files\CMake\share\cmake-4.1\Modules\CMakeCXXInformation.cmake C$:\Program$ Files\CMake\share\cmake-4.1\Modules\CMakeCommonLanguageInclude.cmake C$:\Program$ Files\CMake\share\cmake-4.1\Modules\CMakeCompilerIdDetection.cmake C$:\Program$ Files\CMake\share\cmake-4.1\Modules\CMakeDetermineCCompiler.cmake C$:\Program$ Files\CMake\share\cmake-4.1\Modules\CMakeDetermineCXXCompiler.cmake C$:\Program$ Files\CMake\share\cmake-4.1\Modules\CMakeDetermineCompiler.cmake C$:\Program$ Files\CMake\share\cmake-4.1\Modules\CMakeDetermineCompilerABI.cmake C$:\Program$ Files\CMake\share\cmake-4.1\Modules\CMakeDetermineCompilerId.cmake C$:\Program$ Files\CMake\share\cmake-4.1\Modules\CMakeDetermineCompilerSupport.cmake C$:\Program$ Files\CMake\share\cmake-4.1\Modules\CMakeDetermineRCCompiler.cmake C$:\Program$ Files\CMake\share\cmake-4.1\Modules\CMakeDetermineSystem.cmake C$:\Program$ Files\CMake\share\cmake-4.1\Modules\CMakeFindBinUtils.cmake C$:\Program$ Files\CMake\share\cmake-4.1\Modules\CMakeFindDependencyMacro.cmake C$:\Program$ Files\CMake\share\cmake-4.1\Modules\CMakeGenericSystem.cmake C$:\Program$ Files\CMake\share\cmake-4.1\Modules\CMakeInitializeConfigs.cmake C$:\Program$ Files\CMake\share\cmake-4.1\Modules\CMakeLanguageInformation.cmake C$:\Program$ Files\CMake\share\cmake-4.1\Modules\CMakeNinjaFindMake.cmake C$:\Program$ Files\CMake\share\cmake-4.1\Modules\CMakeParseArguments.cmake C$:\Program$ Files\CMake\share\cmake-4.1\Modules\CMakeParseImplicitIncludeInfo.cmake C$:\Program$ Files\CMake\share\cmake-4.1\Modules\CMakeParseImplicitLinkInfo.cmake C$:\Program$ Files\CMake\share\cmake-4.1\Modules\CMakeParseLibraryArchitecture.cmake C$:\Program$ Files\CMake\share\cmake-4.1\Modules\CMakeRCCompiler.cmake.in C$:\Program$ Files\CMake\share\cmake-4.1\Modules\CMakeRCInformation.cmake C$:\Program$ Files\CMake\share\cmake-4.1\Modules\CMakeSystem.cmake.in C$:\Program$ Files\CMake\share\cmake-4.1\Modules\CMakeSystemSpecificInformation.cmake C$:\Program$ Files\CMake\share\cmake-4.1\Modules\CMakeSystemSpecificInitialize.cmake C$:\Program$ Files\CMake\share\cmake-4.1\Modules\CMakeTestCCompiler.cmake C$:\Program$ Files\CMake\share\cmake-4.1\Modules\CMakeTestCXXCompiler.cmake C$:\Program$ Files\CMake\share\cmake-4.1\Modules\CMakeTestCompilerCommon.cmake C$:\Program$ Files\CMake\share\cmake-4.1\Modules\CMakeTestRCCompiler.cmake C$:\Program$ Files\CMake\share\cmake-4.1\Modules\CheckCSourceCompiles.cmake C$:\Program$ Files\CMake\share\cmake-4.1\Modules\CheckIncludeFile.cmake C$:\Program$ Files\CMake\share\cmake-4.1\Modules\CheckLibraryExists.cmake C$:\Program$ Files\CMake\share\cmake-4.1\Modules\Compiler\ADSP-DetermineCompiler.cmake C$:\Program$ Files\CMake\share\cmake-4.1\Modules\Compiler\ARMCC-DetermineCompiler.cmake C$:\Program$ Files\CMake\share\cmake-4.1\Modules\Compiler\ARMClang-DetermineCompiler.cmake C$:\Program$ Files\CMake\share\cmake-4.1\Modules\Compiler\AppleClang-DetermineCompiler.cmake C$:\Program$ Files\CMake\share\cmake-4.1\Modules\Compiler\Borland-DetermineCompiler.cmake C$:\Program$ Files\CMake\share\cmake-4.1\Modules\Compiler\Bruce-C-DetermineCompiler.cmake C$:\Program$ Files\CMake\share\cmake-4.1\Modules\Compiler\CMakeCommonCompilerMacros.cmake C$:\Program$ Files\CMake\share\cmake-4.1\Modules\Compiler\Clang-DetermineCompiler.cmake C$:\Program$ Files\CMake\share\cmake-4.1\Modules\Compiler\Clang-DetermineCompilerInternal.cmake C$:\Program$ Files\CMake\share\cmake-4.1\Modules\Compiler\Compaq-C-DetermineCompiler.cmake C$:\Program$ Files\CMake\share\cmake-4.1\Modules\Compiler\Compaq-CXX-DetermineCompiler.cmake C$:\Program$ Files\CMake\share\cmake-4.1\Modules\Compiler\Cray-DetermineCompiler.cmake C$:\Program$ Files\CMake\share\cmake-4.1\Modules\Compiler\CrayClang-DetermineCompiler.cmake C$:\Program$ Files\CMake\share\cmake-4.1\Modules\Compiler\Diab-DetermineCompiler.cmake C$:\Program$ Files\CMake\share\cmake-4.1\Modules\Compiler\Embarcadero-DetermineCompiler.cmake C$:\Program$ Files\CMake\share\cmake-4.1\Modules\Compiler\Fujitsu-DetermineCompiler.cmake C$:\Program$ Files\CMake\share\cmake-4.1\Modules\Compiler\FujitsuClang-DetermineCompiler.cmake C$:\Program$ Files\CMake\share\cmake-4.1\Modules\Compiler\GHS-DetermineCompiler.cmake C$:\Program$ Files\CMake\share\cmake-4.1\Modules\Compiler\GNU-C-DetermineCompiler.cmake C$:\Program$ Files\CMake\share\cmake-4.1\Modules\Compiler\GNU-CXX-DetermineCompiler.cmake C$:\Program$ Files\CMake\share\cmake-4.1\Modules\Compiler\HP-C-DetermineCompiler.cmake C$:\Program$ Files\CMake\share\cmake-4.1\Modules\Compiler\HP-CXX-DetermineCompiler.cmake C$:\Program$ Files\CMake\share\cmake-4.1\Modules\Compiler\IAR-DetermineCompiler.cmake C$:\Program$ Files\CMake\share\cmake-4.1\Modules\Compiler\IBMCPP-C-DetermineVersionInternal.cmake C$:\Program$ Files\CMake\share\cmake-4.1\Modules\Compiler\IBMCPP-CXX-DetermineVersionInternal.cmake C$:\Program$ Files\CMake\share\cmake-4.1\Modules\Compiler\IBMClang-C-DetermineCompiler.cmake C$:\Program$ Files\CMake\share\cmake-4.1\Modules\Compiler\IBMClang-CXX-DetermineCompiler.cmake C$:\Program$ Files\CMake\share\cmake-4.1\Modules\Compiler\Intel-DetermineCompiler.cmake C$:\Program$ Files\CMake\share\cmake-4.1\Modules\Compiler\IntelLLVM-DetermineCompiler.cmake C$:\Program$ Files\CMake\share\cmake-4.1\Modules\Compiler\LCC-C-DetermineCompiler.cmake C$:\Program$ Files\CMake\share\cmake-4.1\Modules\Compiler\LCC-CXX-DetermineCompiler.cmake C$:\Program$ Files\CMake\share\cmake-4.1\Modules\Compiler\MSVC-C.cmake C$:\Program$ Files\CMake\share\cmake-4.1\Modules\Compiler\MSVC-CXX-CXXImportStd.cmake C$:\Program$ Files\CMake\share\cmake-4.1\Modules\Compiler\MSVC-CXX.cmake C$:\Program$ Files\CMake\share\cmake-4.1\Modules\Compiler\MSVC-DetermineCompiler.cmake C$:\Program$ Files\CMake\share\cmake-4.1\Modules\Compiler\MSVC.cmake C$:\Program$ Files\CMake\share\cmake-4.1\Modules\Compiler\NVHPC-DetermineCompiler.cmake C$:\Program$ Files\CMake\share\cmake-4.1\Modules\Compiler\NVIDIA-DetermineCompiler.cmake C$:\Program$ Files\CMake\share\cmake-4.1\Modules\Compiler\OpenWatcom-DetermineCompiler.cmake C$:\Program$ Files\CMake\share\cmake-4.1\Modules\Compiler\OrangeC-DetermineCompiler.cmake C$:\Program$ Files\CMake\share\cmake-4.1\Modules\Compiler\PGI-DetermineCompiler.cmake C$:\Program$ Files\CMake\share\cmake-4.1\Modules\Compiler\PathScale-DetermineCompiler.cmake C$:\Program$ Files\CMake\share\cmake-4.1\Modules\Compiler\Renesas-DetermineCompiler.cmake C$:\Program$ Files\CMake\share\cmake-4.1\Modules\Compiler\SCO-DetermineCompiler.cmake C$:\Program$ Files\CMake\share\cmake-4.1\Modules\Compiler\SDCC-C-DetermineCompiler.cmake C$:\Program$ Files\CMake\share\cmake-4.1\Modules\Compiler\SunPro-C-DetermineCompiler.cmake C$:\Program$ Files\CMake\share\cmake-4.1\Modules\Compiler\SunPro-CXX-DetermineCompiler.cmake C$:\Program$ Files\CMake\share\cmake-4.1\Modules\Compiler\TI-DetermineCompiler.cmake C$:\Program$ Files\CMake\share\cmake-4.1\Modules\Compiler\TIClang-DetermineCompiler.cmake C$:\Program$ Files\CMake\share\cmake-4.1\Modules\Compiler\Tasking-DetermineCompiler.cmake C$:\Program$ Files\CMake\share\cmake-4.1\Modules\Compiler\TinyCC-C-DetermineCompiler.cmake C$:\Program$ Files\CMake\share\cmake-4.1\Modules\Compiler\VisualAge-C-DetermineCompiler.cmake C$:\Program$ Files\CMake\share\cmake-4.1\Modules\Compiler\VisualAge-CXX-DetermineCompiler.cmake C$:\Program$ Files\CMake\share\cmake-4.1\Modules\Compiler\Watcom-DetermineCompiler.cmake C$:\Program$ Files\CMake\share\cmake-4.1\Modules\Compiler\XL-C-DetermineCompiler.cmake C$:\Program$ Files\CMake\share\cmake-4.1\Modules\Compiler\XL-CXX-DetermineCompiler.cmake C$:\Program$ Files\CMake\share\cmake-4.1\Modules\Compiler\XLClang-C-DetermineCompiler.cmake C$:\Program$ Files\CMake\share\cmake-4.1\Modules\Compiler\XLClang-CXX-DetermineCompiler.cmake C$:\Program$ Files\CMake\share\cmake-4.1\Modules\Compiler\zOS-C-DetermineCompiler.cmake C$:\Program$ Files\CMake\share\cmake-4.1\Modules\Compiler\zOS-CXX-DetermineCompiler.cmake C$:\Program$ Files\CMake\share\cmake-4.1\Modules\FindPackageHandleStandardArgs.cmake C$:\Program$ Files\CMake\share\cmake-4.1\Modules\FindPackageMessage.cmake C$:\Program$ Files\CMake\share\cmake-4.1\Modules\FindThreads.cmake C$:\Program$ Files\CMake\share\cmake-4.1\Modules\GNUInstallDirs.cmake C$:\Program$ Files\CMake\share\cmake-4.1\Modules\Internal\CMakeCLinkerInformation.cmake C$:\Program$ Files\CMake\share\cmake-4.1\Modules\Internal\CMakeCXXLinkerInformation.cmake C$:\Program$ Files\CMake\share\cmake-4.1\Modules\Internal\CMakeCommonLinkerInformation.cmake C$:\Program$ Files\CMake\share\cmake-4.1\Modules\Internal\CMakeDetermineLinkerId.cmake C$:\Program$ Files\CMake\share\cmake-4.1\Modules\Internal\CMakeInspectCLinker.cmake C$:\Program$ Files\CMake\share\cmake-4.1\Modules\Internal\CMakeInspectCXXLinker.cmake C$:\Program$ Files\CMake\share\cmake-4.1\Modules\Internal\CheckSourceCompiles.cmake C$:\Program$ Files\CMake\share\cmake-4.1\Modules\Internal\FeatureTesting.cmake C$:\Program$ Files\CMake\share\cmake-4.1\Modules\Linker\MSVC-C.cmake C$:\Program$ Files\CMake\share\cmake-4.1\Modules\Linker\MSVC-CXX.cmake C$:\Program$ Files\CMake\share\cmake-4.1\Modules\Linker\MSVC.cmake C$:\Program$ Files\CMake\share\cmake-4.1\Modules\Platform\Linker\Windows-MSVC-C.cmake C$:\Program$ Files\CMake\share\cmake-4.1\Modules\Platform\Linker\Windows-MSVC-CXX.cmake C$:\Program$ Files\CMake\share\cmake-4.1\Modules\Platform\Linker\Windows-MSVC.cmake C$:\Program$ Files\CMake\share\cmake-4.1\Modules\Platform\Windows-Determine-CXX.cmake C$:\Program$ Files\CMake\share\cmake-4.1\Modules\Platform\Windows-Initialize.cmake C$:\Program$ Files\CMake\share\cmake-4.1\Modules\Platform\Windows-MSVC-C.cmake C$:\Program$ Files\CMake\share\cmake-4.1\Modules\Platform\Windows-MSVC-CXX.cmake C$:\Program$ Files\CMake\share\cmake-4.1\Modules\Platform\Windows-MSVC.cmake C$:\Program$ Files\CMake\share\cmake-4.1\Modules\Platform\Windows.cmake C$:\Program$ Files\CMake\share\cmake-4.1\Modules\Platform\WindowsPaths.cmake C$:\Users\wsd07\.conan2\p\asio-c31dc2a380bf0\p\lib\cmake\asio-grpc\AsioGrpcProtobufGenerator.cmake C$:\Users\wsd07\.conan2\p\bzip2e1752ec0a98ec\p\lib\cmake\conan-official-bzip2-variables.cmake C$:\Users\wsd07\.conan2\p\grpc8b6c73b4e5756\p\lib\cmake\conan_trick\grpc_cpp_plugin.cmake C$:\Users\wsd07\.conan2\p\opens13d3a0b103336\p\lib\cmake\conan-official-openssl-variables.cmake C$:\Users\wsd07\.conan2\p\proto47a676cb9257b\p\lib\cmake\protobuf\protobuf-conan-protoc-target.cmake C$:\Users\wsd07\.conan2\p\proto47a676cb9257b\p\lib\cmake\protobuf\protobuf-generate.cmake C$:\Users\wsd07\.conan2\p\proto47a676cb9257b\p\lib\cmake\protobuf\protobuf-module.cmake C$:\Users\wsd07\.conan2\p\proto47a676cb9257b\p\lib\cmake\protobuf\protobuf-options.cmake C$:\Users\wsd07\.conan2\p\standb1cf9a2ad7ef9\p\res\cmake\StandardProjectSettings.cmake C$:\Users\wsd07\vscode\Cura-Dev\CuraEngine\CMakeLists.txt CMakeCache.txt CMakeFiles\4.1.0-rc2\CMakeCCompiler.cmake CMakeFiles\4.1.0-rc2\CMakeCXXCompiler.cmake CMakeFiles\4.1.0-rc2\CMakeRCCompiler.cmake CMakeFiles\4.1.0-rc2\CMakeSystem.cmake generators\BZip2-Target-release.cmake generators\BZip2-release-x86_64-data.cmake generators\BZip2Config.cmake generators\BZip2ConfigVersion.cmake generators\BZip2Targets.cmake generators\Boost-Target-release.cmake generators\Boost-release-x86_64-data.cmake generators\BoostConfig.cmake generators\BoostConfigVersion.cmake generators\BoostTargets.cmake generators\OpenSSL-Target-release.cmake generators\OpenSSL-release-x86_64-data.cmake generators\OpenSSLConfig.cmake generators\OpenSSLConfigVersion.cmake generators\OpenSSLTargets.cmake generators\RapidJSON-Target-release.cmake generators\RapidJSON-release-x86_64-data.cmake generators\RapidJSONConfig.cmake generators\RapidJSONConfigVersion.cmake generators\RapidJSONTargets.cmake generators\ZLIB-Target-release.cmake generators\ZLIB-release-x86_64-data.cmake generators\ZLIBConfig.cmake generators\ZLIBConfigVersion.cmake generators\ZLIBTargets.cmake generators\absl-Target-release.cmake generators\absl-config-version.cmake generators\absl-config.cmake generators\absl-release-x86_64-data.cmake generators\abslTargets.cmake generators\arcus-Target-release.cmake generators\arcus-config-version.cmake generators\arcus-config.cmake generators\arcus-release-x86_64-data.cmake generators\arcusTargets.cmake generators\asio-grpc-Target-release.cmake generators\asio-grpc-config-version.cmake generators\asio-grpc-config.cmake generators\asio-grpc-release-x86_64-data.cmake generators\asio-grpcTargets.cmake generators\c-ares-Target-release.cmake generators\c-ares-config-version.cmake generators\c-ares-config.cmake generators\c-ares-release-x86_64-data.cmake generators\c-aresTargets.cmake generators\clipper-Target-release.cmake generators\clipper-config-version.cmake generators\clipper-config.cmake generators\clipper-release-x86_64-data.cmake generators\clipperTargets.cmake generators\cmakedeps_macros.cmake generators\conan_toolchain.cmake generators\curaengine_grpc_definitions-Target-release.cmake generators\curaengine_grpc_definitions-config-version.cmake generators\curaengine_grpc_definitions-config.cmake generators\curaengine_grpc_definitions-release-x86_64-data.cmake generators\curaengine_grpc_definitionsTargets.cmake generators\fmt-Target-release.cmake generators\fmt-config-version.cmake generators\fmt-config.cmake generators\fmt-release-x86_64-data.cmake generators\fmtTargets.cmake generators\gRPC-Target-release.cmake generators\gRPC-release-x86_64-data.cmake generators\gRPCConfig.cmake generators\gRPCConfigVersion.cmake generators\gRPCTargets.cmake generators\mapbox-geometry-Target-release.cmake generators\mapbox-geometry-config-version.cmake generators\mapbox-geometry-config.cmake generators\mapbox-geometry-release-x86_64-data.cmake generators\mapbox-geometryTargets.cmake generators\mapbox-variant-Target-release.cmake generators\mapbox-variant-config-version.cmake generators\mapbox-variant-config.cmake generators\mapbox-variant-release-x86_64-data.cmake generators\mapbox-variantTargets.cmake generators\mapbox-wagyu-Target-release.cmake generators\mapbox-wagyu-config-version.cmake generators\mapbox-wagyu-config.cmake generators\mapbox-wagyu-release-x86_64-data.cmake generators\mapbox-wagyuTargets.cmake generators\protobuf-Target-release.cmake generators\protobuf-config-version.cmake generators\protobuf-config.cmake generators\protobuf-release-x86_64-data.cmake generators\protobufTargets.cmake generators\range-v3-Target-release.cmake generators\range-v3-config-version.cmake generators\range-v3-config.cmake generators\range-v3-release-x86_64-data.cmake generators\range-v3Targets.cmake generators\re2-Target-release.cmake generators\re2-config-version.cmake generators\re2-config.cmake generators\re2-release-x86_64-data.cmake generators\re2Targets.cmake generators\scripta-Target-release.cmake generators\scripta-config-version.cmake generators\scripta-config.cmake generators\scripta-release-x86_64-data.cmake generators\scriptaTargets.cmake generators\semver-Target-release.cmake generators\semver-config-version.cmake generators\semver-config.cmake generators\semver-release-x86_64-data.cmake generators\semverTargets.cmake generators\spdlog-Target-release.cmake generators\spdlog-config-version.cmake generators\spdlog-config.cmake generators\spdlog-release-x86_64-data.cmake generators\spdlogTargets.cmake generators\standardprojectsettings-Target-release.cmake generators\standardprojectsettings-config-version.cmake generators\standardprojectsettings-config.cmake generators\standardprojectsettings-release-x86_64-data.cmake generators\standardprojectsettingsTargets.cmake generators\stb-Target-release.cmake generators\stb-config-version.cmake generators\stb-config.cmake generators\stb-release-x86_64-data.cmake generators\stbTargets.cmake: phony


#############################################
# Clean all the built files.

build clean: CLEAN


#############################################
# Print all primary targets available.

build help: HELP


#############################################
# Make the all target the default.

default all

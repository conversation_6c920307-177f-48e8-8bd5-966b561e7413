########### AGGREGATED COMPONENTS AND DEPENDENCIES FOR THE MULTI CONFIG #####################
#############################################################################################

set(arcus_COMPONENT_NAMES "")
if(DEFINED arcus_FIND_DEPENDENCY_NAMES)
  list(APPEND arcus_FIND_DEPENDENCY_NAMES protobuf)
  list(REMOVE_DUPLICATES arcus_FIND_DEPENDENCY_NAMES)
else()
  set(arcus_FIND_DEPENDENCY_NAMES protobuf)
endif()
set(protobuf_FIND_MODE "NO_MODULE")

########### VARIABLES #######################################################################
#############################################################################################
set(arcus_PACKAGE_FOLDER_RELEASE "C:/Users/<USER>/.conan2/p/b/arcusee7666f716199/p")
set(arcus_BUILD_MODULES_PATHS_RELEASE )


set(arcus_INCLUDE_DIRS_RELEASE "${arcus_PACKAGE_FOLDER_RELEASE}/include")
set(arcus_RES_DIRS_RELEASE )
set(arcus_DEFINITIONS_RELEASE )
set(arcus_SHARED_LINK_FLAGS_RELEASE )
set(arcus_EXE_LINK_FLAGS_RELEASE )
set(arcus_OBJECTS_RELEASE )
set(arcus_COMPILE_DEFINITIONS_RELEASE )
set(arcus_COMPILE_OPTIONS_C_RELEASE )
set(arcus_COMPILE_OPTIONS_CXX_RELEASE )
set(arcus_LIB_DIRS_RELEASE "${arcus_PACKAGE_FOLDER_RELEASE}/lib")
set(arcus_BIN_DIRS_RELEASE "${arcus_PACKAGE_FOLDER_RELEASE}/bin")
set(arcus_LIBRARY_TYPE_RELEASE SHARED)
set(arcus_IS_HOST_WINDOWS_RELEASE 1)
set(arcus_LIBS_RELEASE Arcus)
set(arcus_SYSTEM_LIBS_RELEASE ws2_32)
set(arcus_FRAMEWORK_DIRS_RELEASE )
set(arcus_FRAMEWORKS_RELEASE )
set(arcus_BUILD_DIRS_RELEASE )
set(arcus_NO_SONAME_MODE_RELEASE FALSE)


# COMPOUND VARIABLES
set(arcus_COMPILE_OPTIONS_RELEASE
    "$<$<COMPILE_LANGUAGE:CXX>:${arcus_COMPILE_OPTIONS_CXX_RELEASE}>"
    "$<$<COMPILE_LANGUAGE:C>:${arcus_COMPILE_OPTIONS_C_RELEASE}>")
set(arcus_LINKER_FLAGS_RELEASE
    "$<$<STREQUAL:$<TARGET_PROPERTY:TYPE>,SHARED_LIBRARY>:${arcus_SHARED_LINK_FLAGS_RELEASE}>"
    "$<$<STREQUAL:$<TARGET_PROPERTY:TYPE>,MODULE_LIBRARY>:${arcus_SHARED_LINK_FLAGS_RELEASE}>"
    "$<$<STREQUAL:$<TARGET_PROPERTY:TYPE>,EXECUTABLE>:${arcus_EXE_LINK_FLAGS_RELEASE}>")


set(arcus_COMPONENTS_RELEASE )
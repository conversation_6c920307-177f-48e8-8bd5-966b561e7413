## ignore such files
*.tar.bz2
*.tar.gz
*.7z
*.zip
.DS_Store
*~
*.bak
NUL
*.gcode

## Directories used for other stuff
Trash/*
output/*
callgrind/*

## Building result.
build/
tests/build/
test_package/build/
debug_build/*
release_build/*
cmake-build-*
libs/stb
*.pyc
*.exe
*.a
*.o
_bin
_obj
x64/*
out/*

## CMake files
cmake_install.cmake
CMakeCache.txt
CMakeFiles/
CPackSourceConfig.cmake

# Visual Studio files generated by CMake
*.vcxproj
*.vcxproj.filters
CuraEngine.sln

# Makefile generated by CMake
Makefile

## IDE project files.
*.layout
*.cbp
*kdev*
*.kate-swp
nbproject/*
.idea
*.depend
.*.swp
.vs
.cproject
.project
.settings

## Documentation.
documentation/html/*
documentation/latex/*

## Test results.
tests/output.xml
callgrind.out.*

## Conan
CMakeUserPresets.json
/conan_imports_manifest.txt
conan.lock
conanbuildinfo.txt
conaninfo.txt
graph_info.json
build/*

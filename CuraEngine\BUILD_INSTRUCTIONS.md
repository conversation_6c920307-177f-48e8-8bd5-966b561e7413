# CuraEngine Build Instructions for Windows

## 🎉 Build Status: SUCCESS!

CuraEngine has been successfully built and is ready to use!

## 📁 Generated Files

- **CuraEngine.exe**: `build\Release\CuraEngine.exe`
- **Static Library**: `build\Release\_CuraEngine.lib`
- **Dependencies**: All DLL files are in `build\Release\`

## 🚀 Quick Start

### Test the Build
```cmd
build\Release\CuraEngine.exe --help
```

### Run a Slice Operation
```cmd
build\Release\CuraEngine.exe slice -j settings.def.json -l model.stl -o output.gcode
```

## 🛠️ Build Scripts

### 1. Full Build Script (First Time)
```cmd
build_curaengine.bat
```
- Cleans previous build
- Reinstalls Conan dependencies with correct Boost configuration
- Configures CMake
- Builds the project

### 2. Quick Build Script (Incremental)
```cmd
quick_build.bat
```
- Use this for quick rebuilds when dependencies haven't changed
- Only rebuilds the source code
- Much faster than full build

### 3. PowerShell Version
```powershell
.\build_curaengine.ps1
```
- PowerShell version with better error handling
- Supports parameters: `-Clean`, `-SkipConan`

## 🔧 Manual Build Commands

If you prefer to build manually:

```cmd
# 1. Set up Visual Studio environment
call "C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\Build\vcvars64.bat"

# 2. Install dependencies (if needed)
conan install . --build=missing -o "boost/*:header_only=False" -o "boost/*:without_exception=False"

# 3. Configure CMake
cmake --preset conan-release

# 4. Build
cmake --build build\Release --config Release
```

## 🐛 Troubleshooting

### Issue: Boost Link Errors
**Solution**: The build scripts automatically fix this by:
- Setting `boost/*:header_only=False`
- Setting `boost/*:without_exception=False`
- Adding custom `boost_exception_fix.cpp` file

### Issue: Missing Visual Studio Environment
**Solution**: Make sure Visual Studio 2022 is installed and run the build script from a regular command prompt (not VS Developer Command Prompt).

### Issue: CMake Configuration Fails
**Solution**: 
1. Delete the `build` directory
2. Run the full build script again
3. Make sure all Conan dependencies are properly installed

## 📋 Dependencies

The following dependencies are automatically managed by Conan:
- Boost (with compiled libraries)
- Protobuf & gRPC
- Arcus (Ultimaker's communication library)
- Clipper (polygon clipping)
- spdlog (logging)
- fmt (string formatting)
- And many more...

## 🎯 Development Workflow

1. **First time setup**: Run `build_curaengine.bat`
2. **Code changes**: Run `quick_build.bat`
3. **Dependency changes**: Run `build_curaengine.bat` again
4. **Clean rebuild**: Delete `build` folder and run `build_curaengine.bat`

## ✅ Verification

The build is successful if:
- `CuraEngine.exe` exists in `build\Release\`
- Running `CuraEngine.exe --help` shows the help message
- No link errors about `boost::throw_exception`

## 📝 Notes

- The build includes a custom fix for Boost exception handling on Windows
- All necessary DLLs are copied to the output directory
- The executable is statically linked where possible for better portability
- Build time: ~5-10 minutes for full build, ~1-2 minutes for incremental

## 🔗 Related Files

- `src/boost_exception_fix.cpp`: Custom Boost exception handling implementation
- `CMakeLists.txt`: Modified to include the exception fix
- `conanfile.py`: Dependency configuration
- `build\Release\generators\`: Conan-generated CMake files
